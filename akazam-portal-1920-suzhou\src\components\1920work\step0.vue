<template>
  <div class="pageWork0 flex-box-between">
    <div class="leftStep flex-box">
      <div class="stepList flex-box">
        <div class="item on flex-box-center">
          <div class="icon"></div>
          <div>需求输入</div>
        </div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(2)">资源池分配</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(3)">运行环境设置</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(4)">模型训练</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(5)">推理部署</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(7)">验证访问</div>
      </div>
      <div class="wordList">
        <div class="wordListItem">
          <p :class="wordAni1 ? 'ani' : ''">应用-智算作业：图像分类学习</p>
          <div class="flex-box">
            <p :class="wordAni2 ? 'ani' : ''" class="flex-box">
              <i v-if="iconAni1" class="el-icon-loading"></i>
              <img v-if="iconAni2" src="~@/assets/images/page4/step1/success.png" alt="">
              上传数据集
            </p>
            <p :class="wordAni3 ? 'ani' : ''" class="flex-box">
              <i v-if="iconAni3" class="el-icon-loading"></i>
              <img v-if="iconAni4" src="~@/assets/images/page4/step1/success.png" alt="">
              输入资源需求
            </p>
          </div>
        </div>
        <div class="wordListItem">
          <p :class="wordAni4 ? 'ani' : ''">应用-通算作业：图像应用打包
          </p>
          <p :class="wordAni5 ? 'ani' : ''" class="flex-box">
            <i v-if="iconAni5" class="el-icon-loading"></i>
            <img v-if="iconAni6" src="~@/assets/images/page4/step1/success.png" alt="">
            输入资源需求
          </p>
        </div>
      </div>
    </div>

    <div class="rightBox flex-box-between">
      <div>
        <div class="centerCore">
          <div class="item flex-box" :class="itemAni0 ? 'ani' : ''">
            <div class="iconBox">
              <div class="icon"><img src="~@/assets/images/1920/work/step1/icon1.png" alt=""></div>
              <div class="word">数据集</div>
            </div>
            <div class="pointList">
              <VueMatrixRaindrop class="codeBox" :backgroundColor="`rgba(42,42,45,0.2)`" :canvasWidth="canvasWidth"
                :canvasHeight="canvasHeight">
              </VueMatrixRaindrop>
              <div class="percentList">
                <div class="word flex-box-between">
                  <p>{{ percentdesc }}</p>
                  <!-- <p>存储位置：华为云-内蒙古3（合营）</p> -->
                  <p>存储位置：华为云-华南-广州</p>
                  <p>{{ percent }}%</p>
                </div>
                <div class="line">
                  <div class="inner" :style="{ 'width': `${percent}%` }"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="item flex-box" :class="itemAni1 ? 'ani' : ''">
            <div class="iconBox">
              <div class="icon"><img src="~@/assets/images/1920/work/step1/icon2.png" alt=""></div>
              <div class="word">智算需求</div>
            </div>
            <div class="pointList on">
              <div class="smallItemList flex-box-between">
                <div class="smallItem" :class="cwordAni1 ? 'on' : ''">CPU≥8核<div class="svg" id="svg1"></div>
                </div>
                <div class="smallItem" :class="cwordAni2 ? 'on' : ''">内存≥64G<div class="svg" id="svg2"></div>
                </div>
                <div class="smallItem" :class="cwordAni3 ? 'on' : ''">GPU卡数量≥1个<div class="svg" id="svg3"></div>
                </div>
                <div class="smallItem" :class="cwordAni4 ? 'on' : ''">GPU型号: V100<div class="svg" id="svg4"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="centerCore">
          <div class="item flex-box" :class="itemAni2 ? 'ani' : ''">
            <div class="iconBox">
              <div class="icon"><img src="~@/assets/images/1920/work/step1/icon3.png" alt=""></div>
              <div class="word">通算需求</div>
            </div>
            <div class="pointList on">
              <div class="smallItemList pad flex-box-between">
                <div class="smallItem" :class="cwordAni5 ? 'on' : ''">CPU≥2核<div class="svg" id="svg5"></div>
                </div>
                <div class="smallItem" :class="cwordAni6 ? 'on' : ''">内存≥4G<div class="svg" id="svg6"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="item flex-box on" :class="itemAni3 ? 'ani' : ''">
            <div class="iconBox">
              <div class="icon"><img src="~@/assets/images/1920/work/step1/icon4.png" alt=""></div>
              <div class="word">池间网络连接</div>
            </div>
            <div class="pointList on">
              <div class="smallItemList pad flex-box-between">
                <div class="smallItem" :class="cwordAni7 ? 'on' : ''">专线网络连接<div class="svg" id="svg7"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="centerCore">
          <div class="wordCore" :class="itemAni4 ? 'ani' : ''">
            <div class="top flex-box-center">
              <div class="t1">3<p>队列中</p>
              </div>
              <div class="linec"></div>
              <div class="t1">20<p>执行中</p>
              </div>
            </div>
            <div class="line"></div>
            <div class="t2">智算作业</div>
          </div>
        </div>
        <div class="centerCore">
          <div class="wordCore" :class="itemAni5 ? 'ani' : ''">
            <div class="top flex-box-center">
              <div class="t1">6<p>队列中</p>
              </div>
              <div class="linec"></div>
              <div class="t1">16<p>执行中</p>
              </div>
            </div>
            <div class="line"></div>
            <div class="t2">通算作业</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getStore, setStore } from "@/common/util";
import VueMatrixRaindrop from '../vueMatrixDigitRain/index.vue'
import * as d3 from 'd3' // d3

export default {
  name: 'workstep1',
  components: {
    VueMatrixRaindrop,
  },
  data() {
    return {
      point: [],
      percent: 0,
      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      wordAni4: false,
      wordAni5: false,
      wordAni6: false,
      wordAni7: false,
      iconAni1: true,
      iconAni2: false,
      iconAni3: true,
      iconAni4: false,
      iconAni5: true,
      iconAni6: false,
      showCode: false,
      timer: null,
      itemAni1: false,
      itemAni2: false,
      itemAni3: false,
      itemAni4: false,
      itemAni5: false,
      percent: 0,
      percent2: 0,
      percentdesc: '上传数据集...',
      percentdesc2: '作业入池中...',
      percentdesc3: '作业入池中...',
      wordBottomAni: false,
      imgItemAni: false,
      imgItemAni2: false,
      time1: 0,
      time2: 0,
      time1Timer: null,
      time2Timer: null,
      timeDate: '',
      imgLeftAni: false,
      imgRightAni: false,
      ponit1Index: null,
      ponit2Index: null,
      pointAnitimer1: null,
      pointAnitimer2: null,
      wordBottomAni2: null,
      canvasWidth: 770,
      canvasHeight: 90,
      isAuto: null,
      cwordAni1: false,
      cwordAni2: false,
      cwordAni3: false,
      cwordAni4: false,
      cwordAni5: false,
      cwordAni6: false,
      cwordAni7: false,
      cwordAni1_2: false,
      cwordAni2_2: false,
      cwordAni3_2: false,
      cwordAni4_2: false,
      cwordAni5_2: false,
      itemAni0: false,
    }
  },
  created() {
    this.isAuto = this.GLOBAL.isAuto
    const date = new Date(); // 时间戳是一个以毫秒为单位的整数
    const year = date.getFullYear(); // 获取当前年份，比如2022
    const month = date.getMonth() + 1; // 获取当前月份，返回值为0-11，需要+1
    const day = date.getDate(); // 获取当前日期，比如12
    const hour = date.getHours(); // 获取当前小时数，0-23
    const minute = date.getMinutes(); // 获取当前分钟数，0-59
    const second = date.getSeconds(); // 获取当前秒数，0-59
    this.timeDate = `${year}/${month}/${day} ${hour}:${minute}:${second}`
    this.canvasWidth = this.GLOBAL.relPx(430)
    this.canvasHeight = this.GLOBAL.relPx(88)
  },
  mounted() {
    this.init()
  },
  methods: {
    // 通用添加小球方法
    addBallNext() {
      var height = 32
      var width = 200
      var svg = d3.select('#svg1').append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 30; i++) {
        svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (31) + 1).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 200 - (5* i)).ease(d3.easeLinear)
      }
    },
    addBallNext2() {
      var height = 32
      var width = 200
      var svg = d3.select('#svg2').append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 30; i++) {
        svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (31) + 1).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 200 - (5* i)).ease(d3.easeLinear)
      }
    },
    addBallNext3() {
      var height = 32
      var width = 200
      var svg = d3.select('#svg3').append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 30; i++) {
        svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (31) + 1).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 200 - (5* i)).ease(d3.easeLinear)
      }
    },
    addBallNext4() {
      var height = 32
      var width = 200
      var svg = d3.select('#svg4').append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 30; i++) {
        svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (31) + 1).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 200 - (5* i)).ease(d3.easeLinear)
      }
    },
    addBallNext5() {
      var height = 32
      var width = 200
      var svg = d3.select('#svg5').append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 30; i++) {
        svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (31) + 1).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 200 - (5* i)).ease(d3.easeLinear)
      }
    },
    addBallNext6() {
      var height = 32
      var width = 200
      var svg = d3.select('#svg6').append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 30; i++) {
        svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (31) + 1).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 200 - (5* i)).ease(d3.easeLinear)
      }
    },
    addBallNext7() {
      var height = 32
      var width = 200
      var svg = d3.select('#svg7').append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 30; i++) {
        svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (31) + 1).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 200 - (5* i)).ease(d3.easeLinear)
      }
    },
    init() {
      let arr = []
      for (let i = 0; i < 96; i++) {
        arr.push(i)
      }
      this.point = arr
      let settimer1 = setTimeout(() => {
        this.wordAni1 = true
      }, 400);
      let settimer2 = setTimeout(() => {
        this.wordAni2 = true
      }, 800);
      let settimer3 = setTimeout(() => {
        this.wordAni3 = true
        this.itemAni0 = true
      }, 1200);
      let settimer4 = setTimeout(() => {
        this.itemAni1 = true
        this.wordAni4 = true
        this.lineAni()
      }, 1600);
      let settimer5 = setTimeout(() => {
        this.wordAni5 = true
        this.itemAni2 = true
      }, 2000);
      let settimer6 = setTimeout(() => {
        this.wordAni6 = true
        this.wordBottomAni = true
        this.imgItemAni = true
        this.imgLeftAni = true
        this.timeFun()
        this.cwordAni1 = true
        this.addBallNext()
        this.itemAni3 = true
      }, 2400);
      let settimer7 = setTimeout(() => {
        this.wordAni7 = true
        this.imgItemAni2 = true
        this.imgRightAni = true
        this.wordBottomAni2 = true
        this.cwordAni2 = true
        this.addBallNext2()
      }, 3000);
      let settimer8 = setTimeout(() => {
        this.cwordAni3 = true
        this.addBallNext3()
        this.cwordAni1_2 = true
      }, 3600);
      let settimer9 = setTimeout(() => {
        this.cwordAni4 = true
        this.addBallNext4()
        this.cwordAni2_2 = true
        this.itemAni4 = true
      }, 4200);
      let settimer10 = setTimeout(() => {
        this.cwordAni5 = true
        this.addBallNext5()
        this.cwordAni3_2 = true
      }, 4800);
      let settimer11 = setTimeout(() => {
        this.cwordAni6 = true
        this.cwordAni4_2 = true
        this.addBallNext6()
        this.iconAni3 = false
        this.iconAni4 = true
      }, 5400);
      let settimer12 = setTimeout(() => {
        this.cwordAni5_2 = true
        this.iconAni5 = false
        this.iconAni6 = true
        this.cwordAni7 = true
        this.addBallNext7()
        this.itemAni5 = true
      }, 6000);

      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
        clearTimeout(settimer8)
        settimer8 = null;
        clearTimeout(settimer9)
        settimer9 = null;
        clearTimeout(settimer10)
        settimer10 = null;
        clearTimeout(settimer11)
        settimer11 = null;
        clearTimeout(settimer12)
        settimer12 = null;
      })
    },
    timeFun() {
      this.time1Timer = setInterval(() => {
        this.time1 += 1
        if (this.time1 >= 36) {
          clearInterval(this.time1Timer)
          this.$emit('stepEnd', 1)
        }
      }, 200);
      this.time2Timer = setInterval(() => {
        this.time2 += 1
        if (this.time2 >= 20) {
          clearInterval(this.time2Timer)
        }
      }, 200);
    },
    endFun(index) {
      if (this.isAuto) {
        return false
      }
      this.$emit('endFun', index)
    },
    lineAni() {
      this.timer = setInterval(() => {
        this.percent += 1
        if (this.percent >= 100) {
          clearInterval(this.timer)
          this.percentdesc = '上传完成'
          this.percentdesc3 = '作业已入池'
          this.iconAni1 = false
          this.iconAni2 = true
        }
      }, 10);
    },
    pointAni() {
      this.ponit1Index = 0
      this.pointAnitimer1 = setInterval(() => {
        this.ponit1Index += 1
        if (this.ponit1Index > 96) {
          clearInterval(this.pointAnitimer1)
        }
      }, 150);
    },
    pointAni2() {
      this.ponit2Index = 0
      this.pointAnitimer2 = setInterval(() => {
        this.ponit2Index += 1
        if (this.ponit2Index > 96) {
          clearInterval(this.pointAnitimer2)
        }
      }, 110);
    },
    resetFun() {
      if (this.timer1) {
        clearInterval(this.timer1)
      }
      if (this.timer2) {
        clearInterval(this.timer2)
      }
      if (this.time1Timer) {
        clearInterval(this.time1Timer)
      }
      if (this.time2Timer) {
        clearInterval(this.time2Timer)
      }
      if (this.pointAnitimer1) {
        clearInterval(this.pointAnitimer1)
      }
      if (this.pointAnitimer2) {
        clearInterval(this.pointAnitimer2)
      }
    },
  },
  beforeDestroy() {
    this.resetFun()
  },
}
</script>

<style lang="scss">
.pageWork0 {
  .rightBox {
    width: 1202px;
    box-sizing: border-box;

    .centerCore {
      background-color: rgba($color: #26262A, $alpha: 0.15);
      width: 578px;
      height: 356px;
      margin-bottom: 40px;

      .item {
        margin-bottom: 60px;
        opacity: 0;

        .iconBox {
          width: 130px;
          // height: 138px;
          box-sizing: border-box;
          // padding: 0 42px;
          padding-top: 23px;

          .icon {
            width: 45px;
            height: 45px;
            margin: 0 auto;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .word {
            font-size: 18px;
            color: #ffffff;
            text-align: center;
            padding-top: 30px;
          }

          &.on {
            padding-top: 43px;
          }
        }

        .arrow {
          width: 41px;
          height: 14px;
          margin-top: 60px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .pointList {
          width: 430px;
          // height: 88px;
          border-radius: 4px;
          box-sizing: border-box;
          // padding: 10px 20px;
          position: relative;

          .codeBox {
            // opacity: 1;
            // position: absolute;
            z-index: 1;
            top: 10px;
            left: 20px;
          }

          .point {
            width: 100%;
            // height: 72px;

            .son {
              width: 10px;
              height: 10px;
              border-radius: 50%;
              background-color: rgba($color: #7FB41C, $alpha: 0.4);
              margin: 10px 7px;

              &.on {
                background-color: rgba($color: #3174F3, $alpha: 1);
              }

              &.s2 {
                &.on {
                  background-color: rgba($color: #00C2FC, $alpha: 1);
                }
              }
            }
          }

          .percentList {
            margin-top: 5px;
            // padding: 0 7px;

            .word {
              p {
                font-size: 16px;
                color: #ffffff;
                padding: 0;
                margin: 10px 0;
              }
            }

            .line {
              background-color: #1A191C;
              border-radius: 3px;

              .inner {
                background-image: url('~@/assets/images/page4/step1/percent.png');
                border-radius: 3px;
                // background-size: cover;
                width: 80%;
                height: 6px;
              }
            }
          }

          &.on {
            padding: 0;
          }
        }

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }

        &.on {
          padding-top: 20px;
        }
      }

      .wordCore {
        padding-top: 55px;
        text-align: center;
        text-align: center;
        opacity: 0;
        .t1 {
          font-size: 80px;
          line-height: 80px;
          color: #ffffff;
          width: 250px;
          padding-top: 30px;

          p {
            font-size: 20px;
            margin: 0;
          }
        }

        .line {
          width: 30px;
          height: 3px;
          background: #1B81DF;
          margin: 0 auto;
          margin-top: 40px;
        }

        .t2 {
          font-size: 18px;
          margin-top: 10px;
        }

        .linec {
          background-image: url('~@/assets/images/1920/work2/line.png');
          background-size: cover;
          width: 1px;
          height: 188px;
        }

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }


  }

  .svg {
    width: 100%;
    height: 100%;
  }

  .smallItemList {
    box-sizing: border-box;
    padding: 0;
    // position: absolute;
    top: 0;
    left: 0;
    z-index: 1;

    .smallItem {
      width: 202px;
      height: 34px;
      background: rgba($color: #26262A, $alpha: 0.5);
      border: 1px solid #26A6E0;
      border-radius: 4px;
      font-size: 16px;
      text-align: center;
      line-height: 32px;
      opacity: 0;
      margin-top: 20px;
      margin-bottom: 10px;
      position: relative;

      .svg {
        position: absolute;
        top: 1px;
        left: 1px;
        z-index: -1;
        width: 200px;
        height: 32px;
      }

      &.trans {
        transform: translateY(30px);
      }

      &.on {
        animation: imgOpacity 1s linear 1 forwards;
      }
    }

    &.pad {
      padding-top: 35px;
    }
  }
}

@keyframes imgOpacity {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
</style>
