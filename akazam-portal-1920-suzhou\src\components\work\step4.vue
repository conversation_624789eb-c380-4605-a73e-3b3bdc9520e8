<template>
  <div class="pageWork4 flex-box-between">
    <div class="leftStep flex-box">
      <div class="stepList">
        <div class="item on al" @click="endFun(1)">基础配置</div>
        <div class="arraw on al"></div>
        <div class="item on al" @click="endFun(2)">资源池分配</div>
        <div class="arraw on al"></div>
        <div class="item on" @click="endFun(3)">运行设置</div>
        <div class="arraw on al"></div>
        <div class="item on al" @click="endFun(4)">模型训练</div>
        <div class="arraw"></div>
        <div class="item on">推理部署</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(6)">访问验证</div>
      </div>
      <div class="wordList">
        <p :class="wordAni1 ? 'ani' : ''">应用名称：图像识别-app16na84</p>
        <!-- <p :class="wordAni2 ? 'ani' : ''">应用部署文件：Image Application Build</p> -->
        <p :class="wordAni2 ? 'ani' : ''">部署资源池：天翼云-内蒙古3（合营）</p>
        <p :class="wordAni3 ? 'ani' : ''">部署规格：m1.large</p>
        <p :class="wordAni4 ? 'ani' : ''">支持公网访问</p>
      </div>
    </div>
    <div class="rightBox flex-box-between">
      <div class="left">
        <div class="tabs flex-box-center" :class="tabsAni ? 'ani' : ''">
          <div class="item" :class="tabIndex == 0 ? 'on' : ''">内蒙古3</div>
          <div class="item" :class="tabIndex == 1 ? 'on' : ''">太原2</div>
          <div class="item" :class="tabIndex == 2 ? 'on' : ''">绍兴2</div>
          <div class="item" :class="tabIndex == 3 ? 'on' : ''">华南-广州</div>
        </div>
        <div class="barBox" :class="barBoxAni ? 'ani' : ''">
          <div class="size s1 flex-box-center">
            <div class="son" :class="[sonIndex === 1 ? 'on' : '', sonIndexChoose === 1 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 2 ? 'on' : '', sonIndexChoose === 2 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 3 ? 'on' : '', sonIndexChoose === 3 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 4 ? 'on' : '', sonIndexChoose === 4 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 5 ? 'on' : '', sonIndexChoose === 5 ? 'choose' : '']"></div>
          </div>
          <div class="size s2 flex-box-center">
            <div class="son" :class="[sonIndex === 6 ? 'on' : '', sonIndexChoose === 6 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 7 ? 'on' : '', sonIndexChoose === 7 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 8 ? 'on' : '', sonIndexChoose === 8 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 9 ? 'on' : '', sonIndexChoose === 9 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 10 ? 'on' : '', sonIndexChoose === 10 ? 'choose' : '']"></div>
          </div>
          <div class="size s3 flex-box-center">
            <div class="son" :class="[sonIndex === 11 ? 'on' : '', sonIndexChoose === 11 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 12 ? 'on' : '', sonIndexChoose === 12 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 13 ? 'on' : '', sonIndexChoose === 12 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 14 ? 'on' : '', sonIndexChoose === 14 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 15 ? 'on' : '', sonIndexChoose === 15 ? 'choose' : '']"></div>
          </div>
          <div class="size s4 flex-box-center">
            <div class="son" :class="[sonIndex === 16 ? 'on' : '', sonIndexChoose === 16 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 17 ? 'on' : '', sonIndexChoose === 17 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 18 ? 'on' : '', sonIndexChoose === 18 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 19 ? 'on' : '', sonIndexChoose === 19 ? 'choose' : '']"></div>
            <div class="son" :class="[sonIndex === 20 ? 'on' : '', sonIndexChoose === 20 ? 'choose' : '']"></div>
          </div>
        </div>
        <div class="lineBox">
          <div class="line" :class="lineAni ? 'ani' : ''">
            <div class="inner" :style="{ 'width': `${percent}%` }"></div>
          </div>
          <div class="word" :class="lineWordAni ? 'ani' : ''">部署完成</div>
        </div>
      </div>
      <div class="right">
        <div class="box flex-box-between">
          <div class="item" :class="item1Ani ? 'ani' : ''">
            <div class="ititle">Cpu 利用率</div>
            <div class="per">{{ num1 }}<span>%</span></div>
            <div class="line"></div>
            <div class="label">last of the averages</div>
          </div>
          <div class="item" :class="item2Ani ? 'ani' : ''">
            <div class="ititle">内存利用率</div>
            <div class="per">{{ num2 }}<span>%</span></div>
            <div class="line"></div>
            <div class="label">last of the averages</div>
          </div>
        </div>
        <div class="charts" :class="item3Ani ? 'ani' : ''">
          <div class="line1" ref="line1"></div>
        </div>
        <div class="charts" :class="item4Ani ? 'ani' : ''">
          <div class="line2" ref="line2"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getStore, setStore } from "@/common/util";
import * as echarts from 'echarts';

export default {
  name: 'workstep2',
  data() {
    return {
      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      wordAni4: false,
      wordAni5: false,
      chartsMap: null,
      tabIndex: null,
      sonIndex: null,
      chartsline1: null,
      chartsline2: null,
      tabsAni: null,
      barBoxAni: null,
      lineAni: null,
      lineWordAni: null,
      tabsTimer: null,
      item1Ani: null,
      item2Ani: null,
      item3Ani: null,
      item4Ani: null,
      sonTimer: null,
      timerpercent: null,
      percent: 0,
      num1: 1.99,
      num2: 2.99,
      sonIndexChoose: null,
      isAuto: null
    }
  },
  created() {
    this.isAuto = this.GLOBAL.isAuto
  },
  mounted() {
    this.init()
  },
  methods: {
    numAni() {
      for (let i = 0; i < 20; i++) {
        setTimeout(() => {
          if (!this.num1 || !this.num2) {
            return false
          }
          this.num1 = (Math.random() * 10).toFixed(2)
          this.num2 = (Math.random() * 10).toFixed(2)
          if (i > 18) {
            this.num1 = 1.99
            this.num2 = 2.99
          }
        }, 500 * i);
      }
    },
    init() {
      let settimer1 = setTimeout(() => {
        this.wordAni1 = true
      }, 500);
      let settimer2 = setTimeout(() => {
        this.wordAni2 = true
      }, 1000);
      let settimer3 = setTimeout(() => {
        this.wordAni3 = true
        this.tabsAni = true
      }, 1500);
      let settimer4 = setTimeout(() => {
        this.wordAni4 = true
        this.barBoxAni = true
        this.tabsAniFun()
      }, 2000);
      let settimer5 = setTimeout(() => {
        this.wordAni5 = true
        this.lineAni = true
        this.lineAniFun()
        this.sizeAniFun()
      }, 2500);
      let settimer6 = setTimeout(() => {
        this.item1Ani = true
      }, 4000);
      let settimer7 = setTimeout(() => {
        this.item2Ani = true
        this.numAni()
      }, 4500);
      let settimer8 = setTimeout(() => {
        this.item3Ani = true
        this.init1()
      }, 5000);
      let settimer9 = setTimeout(() => {
        this.item4Ani = true
        this.lineWordAni = true
        this.init2()
        this.$emit('stepEnd', 6)
      }, 5500);

      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
        clearTimeout(settimer8)
        settimer8 = null;
        clearTimeout(settimer9)
        settimer9 = null;
      })
    },
    endFun(index) {
      if (this.isAuto) {
        return false
      }
      this.$emit('endFun', index)
    },
    tabsAniFun() {
      this.tabIndex = 0
      this.tabsTimer = setInterval(() => {
        this.tabIndex += 1
        if (this.tabIndex > 3) {
          this.tabIndex = 0
        }
      }, 400);
      setTimeout(() => {
        clearInterval(this.tabsTimer)
      }, 3200);
    },
    sizeAniFun() {
      this.sonIndex = 0
      this.sonTimer = setInterval(() => {
        this.sonIndex += 1
        if (this.sonIndex > 20) {
          this.sonIndex = 0
        }
      }, 400);
      setTimeout(() => {
        clearInterval(this.sonTimer)
        this.sonIndexChoose = this.sonIndex
      }, 4000);
    },
    lineAniFun() {
      this.timerpercent = setInterval(() => {
        this.percent += 1
        if (this.percent >= 100) {
          clearInterval(this.timerpercent)
        }
      }, 40);
    },
    init1() {
      let option = {
        title: {
          text: '网络吞吐量',
          textStyle: {
            color: 'rgba(255,255,255,0.8)',
            fontSize: '16',
          },
          left: '1%',
          top: '5%'
        },
        grid: {
          left: '5%',
          right: '5%',
          top: '30%',
          bottom: '15%',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['9:00', '9:30', '10:00', '10:30', '11:00'],
          axisLine: {
            lineStyle: {
              width: 0.5,
            }
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 2,
          min: 0,
          max: 100,
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [
          {
            data: [50, 90, 20, 70, 90],
            type: 'line',
            areaStyle: {
              normal: {
                // 渐变填充色（线条下半部分）
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(50, 94, 252,0.3)" },
                  { offset: 1, color: "rgba(50, 94, 252,0.05)" }
                ])
              }
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: "rgba(50, 94, 252,0.3)"
                }
              }
            },
            symbol: "none",
            smooth: true,
          }
        ]
      }
      // 内存泄漏 无dom 不执行
      if (!this.$refs.line1) { return false }
      this.chartsline1 = echarts.init(this.$refs.line1, null, { width: 947, height: 188 });

      this.chartsline1.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsline1.setOption(option);
    },
    init2() {
      let option = {
        title: {
          text: '磁盘IO',
          textStyle: {
            color: 'rgba(255,255,255,0.8)',
            fontSize: '16',
          },
          left: '1%',
          top: '5%'
        },
        grid: {
          left: '5%',
          right: '5%',
          top: '30%',
          bottom: '15%',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['9:00', '9:30', '10:00', '10:30', '11:00'],
          axisLine: {
            lineStyle: {
              width: 0.5,
            }
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 2,
          min: 0,
          max: 100,
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [
          {
            data: [50, 90, 20, 70, 90],
            type: 'line',
            areaStyle: {
              normal: {
                // 渐变填充色（线条下半部分）
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(43, 193, 255,0.3)" },
                  { offset: 1, color: "rgba(43, 193, 255,0.05)" }
                ])
              }
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: "rgba(43, 193, 255,0.3)"
                }
              }
            },
            symbol: "none",
            smooth: true,
          }
        ]
      }
      // 内存泄漏 无dom 不执行
      if (!this.$refs.line2) { return false }
      this.chartsline2 = echarts.init(this.$refs.line2, null, { width: 947, height: 188 });
      this.chartsline2.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsline2.setOption(option);
    }
  },
  beforeDestroy() {
    if (this.chartsline1) {
      this.chartsline1.dispose()
    }
    if (this.chartsline2) {
      this.chartsline2.dispose()
    }
    if (this.tabsTimer) {
      clearInterval(this.tabsTimer)
    }
    if (this.sonTimer) {
      clearInterval(this.sonTimer)
    }
    if (this.timerpercent) {
      clearInterval(this.timerpercent)
    }
  },
}
</script>

<style lang="scss">
.pageWork4 {

  .rightBox {
    width: 2400px;
    box-sizing: border-box;
    padding-right: 50px;

    .left {
      .tabs {
        margin-top: 160px;
        opacity: 0;

        .item {
          background-image: url('~@/assets/images/page4/step4/tab.png');
          background-size: cover;
          width: 245px;
          height: 90px;
          box-sizing: border-box;
          padding: 31px 0 0 127px;
          font-size: 16px;
          line-height: 16px;
          margin: 0 34px;
          opacity: 0.3;

          &.on {
            opacity: 1;
          }
        }

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }

      .barBox {
        margin-top: 130px;
        opacity: 0;

        .size {
          .son {
            width: 72px;
            height: 72px;
            background-image: url('~@/assets/images/page4/step4/s1.png');
            background-size: cover;
            margin: 0 20px;

            &.on {
              background-image: url('~@/assets/images/page4/step4/s1-on.png');
            }

            &.choose {
              background-image: url('~@/assets/images/page4/step4/s1-on-c.png');
            }
          }

          &.s2 {
            transform: translateY(-40px);

            .son {
              width: 93px;
              height: 92px;
              background-image: url('~@/assets/images/page4/step4/s2.png');
              margin: 0 26px;

              &.on {
                background-image: url('~@/assets/images/page4/step4/s2-on.png');
              }

              &.choose {
                background-image: url('~@/assets/images/page4/step4/s2-on-c.png');
              }
            }
          }

          &.s3 {
            transform: translateY(-90px);

            .son {
              width: 122px;
              height: 124px;
              background-image: url('~@/assets/images/page4/step4/s3.png');
              margin: 0 34px;

              &.on {
                background-image: url('~@/assets/images/page4/step4/s3-on.png');
              }

              &.choose {
                background-image: url('~@/assets/images/page4/step4/s3-on-c.png');
              }
            }
          }

          &.s4 {
            transform: translateY(-140px);

            .son {
              width: 153px;
              height: 155px;
              background-image: url('~@/assets/images/page4/step4/s4.png');
              margin: 0 43.5px;

              &.on {
                background-image: url('~@/assets/images/page4/step4/s4-on.png');
              }

              &.choose {
                background-image: url('~@/assets/images/page4/step4/s4-on-c.png');
              }
            }
          }
        }

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }

      .lineBox {

        .line {
          width: 815px;
          height: 10px;
          border-radius: 5px;
          background-color: #1A191C;
          margin: 0 auto;
          opacity: 0;

          .inner {
            background-image: url('~@/assets/images/page4/step4/percent.png');
            width: 0%;
            height: 10px;
            border-radius: 5px;
          }

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }

        .word {
          text-align: center;
          padding-top: 20px;
          font-size: 26px;
          opacity: 0;

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }


      }
    }

    .right {
      width: 947px;
      margin-top: 170px;

      .box {
        .item {
          width: 463px;
          height: 187px;
          background-color: rgba($color: #26262A, $alpha: 0.15);
          box-sizing: border-box;
          padding: 18px;
          opacity: 0;

          .ititle {
            font-size: 16px;
          }

          .per {
            font-size: 67px;
            text-align: center;
            margin-top: 25px;
            line-height: 68px;

            span {
              font-size: 23px;
            }
          }

          .line {
            width: 25px;
            height: 2px;
            background-color: #4C4C4C;
            margin: 0 auto;
          }

          .label {
            font-size: 13px;
            color: #A9A9AB;
            text-align: center;
            margin-top: 10px;
          }

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }
      }

      .charts {
        width: 100%;
        height: 188px;
        background-color: rgba($color: #26262A, $alpha: 0.15);
        margin-top: 20px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }
  }
}

@keyframes imgOpacity {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
</style>
