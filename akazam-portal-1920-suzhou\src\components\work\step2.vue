<template>
  <div class="pageWork2 flex-box-between">
    <div class="leftStep flex-box">
      <div class="stepList">
        <div class="item on al" @click="endFun(1)">基础配置</div>
        <div class="arraw on al"></div>
        <div class="item on al" @click="endFun(2)">资源池分配</div>
        <div class="arraw on al"></div>
        <div class="item on">运行设置</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(4)">模型训练</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(5)">推理部署</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(6)">访问验证</div>
      </div>
      <div>
        <div class="wordList">
          <p :class="wordAni1 ? 'ani' : ''">应用作业1-智算作业-图像分类学习</p>
          <p :class="wordAni2 ? 'ani' : ''">运行资源池：华为云-上海一</p>
          <p :class="wordAni3 ? 'ani' : ''" class="flex-box">
            <i v-if="iconAni1" class="el-icon-loading"></i>
            <img v-if="iconAni2" src="~@/assets/images/page4/step1/success.png" alt="">
            {{ word1 }}
          </p>
        </div>
        <div class="wordList">
          <p :class="wordAni4 ? 'ani' : ''">应用作业2-通算作业-图像应用Build</p>
          <p :class="wordAni5 ? 'ani' : ''">运行资源池：天翼云-内蒙3(合营)</p>
          <p :class="wordAni6 ? 'ani' : ''" class="flex-box">
            <i v-if="iconAni3" class="el-icon-loading"></i>
            <img v-if="iconAni4" src="~@/assets/images/page4/step1/success.png" alt="">
            {{ word1_2 }}
          </p>
        </div>
      </div>
    </div>
    <div class="rightBox flex-box-between">
      <div class="centerCore">
        <div class="core flex-box-between">
          <div class="item" :class="centerOpa1 ? 'ani' : ''">
            <div class="son" :class="ccAni1 ? 'ani' : ''">
              <div class="ict flex-box">
                <div class="icon"><img src="~@/assets/images/page4/step1/s2-icon1.png" alt=""></div>
                <div class="text">智算作业-数据集</div>
              </div>
              <div class="pointList">
                <div class="point flex-box-between">
                  <div class="pointson" v-for="(item, index) in point" :class="ponit1Index > index ? 'on' : ''"
                    :key="index">
                  </div>
                </div>
              </div>
            </div>
            <div class="son" :class="ccAni2 ? 'ani' : ''">
              <div class="ict flex-box">
                <div class="icon"><img src="~@/assets/images/page4/step1/s2-icon3.png" alt=""></div>
                <div class="text">输入路径</div>
              </div>
              <div class="textBox">/Ip0705/v1/data/input/</div>
            </div>
            <div class="son" :class="ccAni3 ? 'ani' : ''">
              <div class="ict flex-box">
                <div class="icon"><img src="~@/assets/images/page4/step1/s2-icon4.png" alt=""></div>
                <div class="text">输出路径</div>
              </div>
              <div class="textBox">/lp0705/v1/data/output/</div>
            </div>
            <div class="son" :class="ccAni4 ? 'ani' : ''">
              <div class="ict flex-box">
                <div class="icon"><img src="~@/assets/images/page4/step1/s2-icon5.png" alt=""></div>
                <div class="text">学习框架</div>
              </div>
              <div class="itemImgList flex-box-between">
                <div class="imgSon">
                  <div class="img"><img src="~@/assets/images/page4/step1/s2-img1.png" alt=""></div>
                  <div class="dp" :class="chooseAni ? 'on' : ''"></div>
                </div>
                <div class="imgSon">
                  <div class="img"><img src="~@/assets/images/page4/step1/s2-img2.png" alt=""></div>
                  <div class="dp"></div>
                </div>
                <div class="imgSon">
                  <div class="img"><img src="~@/assets/images/page4/step1/s2-img3.png" alt=""></div>
                  <div class="dp"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="item" :class="centerOpa2 ? 'ani' : ''">
            <div class="son" :class="ccAni1_2 ? 'ani' : ''">
              <div class="ict flex-box">
                <div class="icon"><img src="~@/assets/images/page4/step1/s2-icon1.png" alt=""></div>
                <div class="text">智算作业-数据集</div>
              </div>
              <div class="pointList">
                <div class="point flex-box-between">
                  <div class="pointson" v-for="(item, index) in point" :class="ponit2Index > index ? 'on' : ''"
                    :key="index">
                  </div>
                </div>
              </div>
            </div>
            <div class="son" :class="ccAni2_2 ? 'ani' : ''">
              <div class="ict flex-box">
                <div class="icon"><img src="~@/assets/images/page4/step1/s2-icon3.png" alt=""></div>
                <div class="text">输入来源</div>
              </div>
              <div class="textBox">依赖智算作业-图像分类学习输出</div>
            </div>
          </div>
        </div>
        <div class="bottomTitle" :class="bottomTitleAni1 ? 'ani' : ''">作业运行环境设置</div>
      </div>
      <div class="rightCore">
        <div class="core flex-box-between">
          <div class="left" :class="rightOpa1 ? 'ani' : ''">
            <div class=" flex-box-between">
              <div class="item">
                <div class="title">作业队列</div>
                <div class="wordCore">
                  <div class="value"><span>6</span>/20</div>
                  <div class="small">排队中</div>
                </div>
              </div>
              <div class="item">
                <div class="title">GPU使用率</div>
                <round-common :value="86"></round-common>
              </div>
              <div class="item on">
                <div class="title">CPU使用率</div>
                <round-common :value="35"></round-common>
              </div>
              <div class="item on">
                <div class="title">内存使用率</div>
                <round-common :value="24"></round-common>
              </div>
            </div>
            <div class="leftTitle">华为云-上海一</div>
          </div>
          <div class="left" :class="rightOpa2 ? 'ani' : ''">
            <div class=" flex-box-between">
              <div class="item long">
                <div class="title">作业队列</div>
                <div class="wordCore">
                  <div class="value"><span>8</span>/37</div>
                  <div class="small">排队中</div>
                </div>
              </div>
              <div class="item on">
                <div class="title">CPU使用率</div>
                <round-common :value="45"></round-common>
              </div>
              <div class="item on">
                <div class="title">内存使用率</div>
                <round-common :value="22"></round-common>
              </div>
            </div>
            <div class="leftTitle">天翼云-内蒙3(合营)</div>
          </div>
        </div>
        <div class="bottomTitle" :class="bottomTitleAni2 ? 'ani' : ''">资源池水位</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getStore, setStore } from "@/common/util";
import VueMatrixRaindrop2 from '../vueMatrixDigitRain/index2.vue';
import * as d3 from 'd3' // d3
import roundCommon from '../round/Index'

export default {
  name: 'workstep1',
  components: {
    VueMatrixRaindrop2,
    roundCommon
  },
  data() {
    return {
      point: [],
      percent: 0,
      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      wordAni4: false,
      wordAni5: false,
      wordAni6: false,
      iconAni1: true,
      iconAni2: false,
      iconAni3: true,
      iconAni4: false,
      showCode: false,
      timer: null,
      itemAni1: false,
      itemAni2: false,
      percent: 0,
      percent2: 0,
      percentdesc: '上传训练数据集...',
      percentdesc2: '作业入池中...',
      percentdesc3: '作业入池中...',
      wordBottomAni: false,
      imgItemAni: false,
      imgItemAni2: false,
      time1: 0,
      time2: 0,
      time1Timer: null,
      time2Timer: null,
      timeDate: '',
      imgLeftAni: false,
      imgRightAni: false,
      ponit1Index: null,
      ponit2Index: null,
      pointAnitimer1: null,
      pointAnitimer2: null,
      wordBottomAni2: null,
      isAuto: null,
      centerOpa: false,
      rightOpa: false,

      cwordAni1: false,
      cwordAni2: false,
      cwordAni3: false,
      cwordAni4: false,
      cwordAni5: false,
      ccAni1: false,
      ccAni2: false,
      ccAni3: false,
      ccAni4: false,
      sixItemSonAni1: false,
      sixItemSonAni2: false,
      sixItemSonAni3: false,
      sixItemSonAni4: false,
      sixItemSonAni5: false,
      sixItemSonAni6: false,
      rightValBoxitem1: false,
      rightValBoxitem2: false,
      rightValBoxitem3: false,
      rightValBoxitem4: false,
      rightValBoxitem5: false,
      rightValBoxitem6: false,
      rightValBoxitem7: false,
      rightValBoxitem8: false,
      rightValBoxitemChoose: false,

      cwordAni1_2: false,
      cwordAni2_2: false,
      cwordAni3_2: false,
      cwordAni4_2: false,
      cwordAni5_2: false,
      ccAni1_2: false,
      ccAni2_2: false,
      ccAni3_2: false,
      ccAni4_2: false,
      sixItemSonAni1_2: false,
      sixItemSonAni2_2: false,
      sixItemSonAni3_2: false,
      sixItemSonAni4_2: false,
      sixItemSonAni5_2: false,
      sixItemSonAni6_2: false,
      rightValBoxitem1_2: false,
      rightValBoxitem2_2: false,
      rightValBoxitem3_2: false,
      rightValBoxitem4_2: false,
      rightValBoxitem5_2: false,
      rightValBoxitem6_2: false,
      rightValBoxitem7_2: false,
      rightValBoxitem8_2: false,
      rightValBoxitemChoose_2: false,

      rightOpa1: false,
      rightOpa2: false,
      word1: '设置中',
      word1_2: '设置中',
      showWorkSecond: false,
      ponit1Index: null,
      ponit: [],
      centerOpa1: false,
      centerOpa2: false,
      ccAni1: false,
      ccAni2: false,
      ccAni3: false,
      ccAni4: false,
      ccAni1_2: false,
      ccAni2_2: false,
      chooseAni: false,
      bottomTitleAni1: false,
      bottomTitleAni2: false,
    }
  },
  created() {
    this.isAuto = this.GLOBAL.isAuto
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      let arr = []
      for (let i = 0; i < 44; i++) {
        arr.push(i)
      }
      this.point = arr
      let settimer1 = setTimeout(() => {
        this.wordAni1 = true
      }, 500);
      let settimer2 = setTimeout(() => {
        this.wordAni2 = true
      }, 1000);
      let settimer3 = setTimeout(() => {
        this.wordAni3 = true
        this.centerOpa = true
      }, 1500);
      let settimer4 = setTimeout(() => {
        this.wordAni4 = true
        this.centerOpa1 = true
      }, 2000);
      let settimer5 = setTimeout(() => {
        this.wordAni5 = true
        this.ccAni1 = true
      }, 2500);
      let settimer6 = setTimeout(() => {
        this.wordAni6 = true
        this.ccAni2 = true
        this.pointAni()
        this.bottomTitleAni1 = true
      }, 3000);
      let settimer7 = setTimeout(() => {
        this.centerOpa2 = true
        this.ccAni3 = true
        this.rightOpa1 = true
      }, 3500);
      let settimer8 = setTimeout(() => {
        this.ccAni4 = true
      }, 4000);
      let settimer9 = setTimeout(() => {
        this.ccAni1_2 = true
        this.chooseAni = true
      }, 4500);
      let settimer10 = setTimeout(() => {
        this.ccAni2_2 = true
        this.rightOpa2 = true
        this.pointAni2()
        this.bottomTitleAni2 = true
      }, 5000);

      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
        clearTimeout(settimer8)
        settimer8 = null;
        clearTimeout(settimer9)
        settimer9 = null;
        clearTimeout(settimer10)
        settimer10 = null;
      })
    },
    pointAni() {
      this.ponit1Index = 0
      this.pointAnitimer1 = setInterval(() => {
        this.ponit1Index += 1
        if (this.ponit1Index > 44) {
          this.iconAni1 = false
          this.iconAni2 = true
          this.word1 = '设置完成'
          clearInterval(this.pointAnitimer1)
        }
      }, 100);
    },
    pointAni2() {
      this.ponit2Index = 0
      this.pointAnitimer2 = setInterval(() => {
        this.ponit2Index += 1
        if (this.ponit2Index > 44) {
          this.iconAni3 = false
          this.iconAni4 = true
          this.word1_2 = '设置完成'
          clearInterval(this.pointAnitimer2)
          this.$emit('stepEnd', 3)
        }
      }, 60);
    },
    endFun(index) {
      if (this.isAuto) {
        return false
      }
      this.$emit('endFun', index)
    },
    resetFun() {
      if (this.pointAnitimer1) {
        clearInterval(this.pointAnitimer1)
      }
      if (this.pointAnitimer2) {
        clearInterval(this.pointAnitimer2)
      }
    },
  },
  beforeDestroy() {
    this.resetFun()
  },
}
</script>

<style lang="scss">
.pageWork2 {

  .rightBox {
    width: 2600px;
    box-sizing: border-box;
    padding-right: 60px;
    padding-top: 120px;

    .centerCore {
      .core {
        width: 1200px;

        .item {
          width: 570px;
          height: 527px;
          background: #1F1E21;
          border-radius: 4px;
          box-sizing: border-box;
          padding: 20px;
          opacity: 0;

          .son {
            margin-bottom: 40px;
            opacity: 0;

            .ict {
              .icon {
                width: 21px;
                height: 21px;
                margin-right: 10px;

                img {
                  width: 100%;
                  height: 100%;
                }
              }

              .text {
                font-size: 18px;
                line-height: 21px;
              }
            }

            .pointList {
              margin-top: 20px;

              .point {
                width: 100%;

                .pointson {
                  width: 10px;
                  height: 10px;
                  border-radius: 50%;
                  background-color: rgba($color: #7FB41C, $alpha: 0.4);
                  margin: 10px 7px;

                  &.on {
                    background-color: rgba($color: #3174F3, $alpha: 1);
                  }

                  &.s2 {
                    &.on {
                      background-color: rgba($color: #00C2FC, $alpha: 1);
                    }
                  }
                }
              }
            }

            .textBox {
              margin-top: 20px;
              font-size: 16px;
              color: #999999;
              line-height: 16px;
            }

            .itemImgList {
              margin-top: 20px;
              padding: 0 20px;

              .imgSon {
                .img {
                  width: 102px;
                  height: 79px;

                  img {
                    width: 100%;
                    height: 100%;
                  }
                }

                .dp {
                  width: 10px;
                  height: 10px;
                  background: #666666;
                  border-radius: 50%;
                  margin: 15px auto 0 auto;

                  &.on {
                    background: #21D571;
                  }
                }
              }
            }

            &.ani {
              animation: imgOpacity 1s linear 1 forwards;
            }
          }

          &.ani {
            animation: imgOpacity 0.5s linear 1 forwards;
          }
        }
      }

      .bottomTitle {
        font-size: 26px;
        text-align: center;
        padding-top: 70px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }

      &.opa {
        opacity: 1;
      }
    }

    .rightCore {
      width: 1150px;

      .core {
        .left {
          width: 550px;
          // height: 430px;
          opacity: 0;

          .item {
            height: 200px;
            width: 260px;
            background-color: rgba($color: #26262A, $alpha: 0.15);
            text-align: center;
            box-sizing: border-box;
            padding: 18px;

            .title {
              font-size: 16px;
              line-height: 16px;
              text-align: left;
            }

            .value {
              padding-top: 40px;
              font-size: 42px;
              color: #666666;
              line-height: 42px;

              span {
                color: #ffffff;
              }
            }

            .small {
              font-size: 12px;
              margin-top: 20px;
            }

            &.long {
              width: 100%;
            }

            &.on {
              margin-top: 30px;
            }
          }

          .leftTitle {
            font-size: 20px;
            text-align: center;
            margin-top: 60px;
          }

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }
      }

      .bottomTitle {
        font-size: 26px;
        text-align: center;
        padding-top: 90px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }
  }
}

@keyframes imgOpacity {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes imgOpacity2 {

  0% {
    opacity: 1;
  }

  100% {
    opacity: 0.2;
  }
}
</style>
