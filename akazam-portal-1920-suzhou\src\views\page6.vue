<template>
  <div class="pageCommon page6">
    <!-- 头部-s -->
    <header-common :icon="2" :name="'算力服务时延圈'"></header-common>
    <!-- 头部-e -->
    <div class="main flex-box-between">
      <div class="dialogMap">
        <!-- <div class="map" ref="dialogMap"></div> -->
        <div class="flex-box-between">
          <div class="leftMapCore">
            <div class="map" ref="dialogMap"></div>
          </div>
          <div class="rightMap" ref="rightMap"></div>
        </div>
        <div class="tableList">
          <div class="ul flex-box-between">
            <div class="item w1">源节点</div>
            <div class="item w1">目标节点</div>
            <div class="item w5">延迟(ms)</div>
            <div class="item w6">抖动(ms)</div>
            <div class="item w7">丢包(%)</div>
          </div>
          <div class="liList">
            <div class="li flex-box-between" v-for="(item, index) in tableData" :key="index"
              :class="index % 2 === 0 ? 'on' : ''">
              <div class="item w1">{{ item.xcity == '苏州吴江' ? suzhouCity : item.xcity }}</div>
              <div class="item w2">{{ item.ycity || '-' }}</div>
              <div class="item w5">{{ item.delay || 0 }}</div>
              <div class="item w6">{{ item.jitter || 0 }}</div>
              <div class="item w7">{{ item.lost || 0 }}%</div>
            </div>
          </div>
        </div>
      </div>
      <div class="centerRight">
        <div class="top">
          <div class="titleCore flex-box">
            <div class="icon"><img src="~@/assets/images/page5/title.png"></div>
            <div class="title">{{ chooseCityName }}</div>
          </div>
          <div class="hlCore">
            <!-- <div class="hl1"></div>
              <div class="hl2"></div>
              <div class="hl3"></div>
              <div class="hl4"></div> -->
            <div class="hlms1">{{ hllist4ms }}(ms)</div>
            <div class="hlms2">{{ hllist3ms }}(ms)</div>
            <div class="hlms3">{{ hllist2ms }}(ms)</div>
            <div class="hlms4">{{ hllist1ms }}(ms)</div>
            <div class="hlname1">{{ hllist4 }}</div>
            <div class="hlname2">{{ hllist3 }}</div>
            <div class="hlname3">{{ hllist2 }}</div>
            <div class="hlname4">{{ hllist1 }}</div>
          </div>
        </div>
        <div class="down flex-box-between">
          <div class="item flex-box-center on" @click="tabChange(4)">
            <div class="icon"><img src="~@/assets/images/page5/r-icon2.png"></div>
            <div class="text">算力时延圈</div>
          </div>
          <!-- <div class="item flex-box-center" @click="tabChange(1)">
            <div class="icon"><img src="~@/assets/images/page5/r-icon1.png"></div>
            <div class="text">算力网络</div>
          </div> -->
          <!-- <div class="item flex-box-center" :class="tabIndex == 2 ? 'on' : ''" @click="tabChange(2)">
            <div class="icon"><img src="~@/assets/images/page5/r-icon2.png"></div>
            <div class="text">多云DCI</div>
          </div> -->
          <div class="item flex-box-center" @click="tabChange(3)">
            <div class="icon"><img src="~@/assets/images/page5/r-icon3.png"></div>
            <div class="text">云间互联</div>
          </div>
        </div>
      </div>
    </div>
    <div class="mapDialog" v-show="showmapDialog">
      <div class="flex-box-end">
        <div class="close" @click.stop="showmapDialog = false"></div>
      </div>
      <div class="map" ref="mapsuzhou"></div>
    </div>
  </div>
</template>


<script>
import headerCommon from '../components/header/Index'
import dataJson from '@/assets/json/cloud_matrix7.json'
import dataJsonday2 from '@/assets/json/cloud_matrix_day2.json'
import dataJson2 from '@/assets/json/cloud_matrix2.json'
import dataJson3 from '@/assets/json/cloud_matrix3.json'
import dataJson4 from '@/assets/json/yzw.json'
import * as echarts from 'echarts';
import chinaMap from '@/assets/json/chinaMap.json'
import geoCoordMap from '@/assets/json/geoCoordMap.json'
import worldMap from '@/assets/json/world.json'
import suzhouMap from '@/assets/json/suzhou.json'
import vueSeamlessScroll from 'vue-seamless-scroll'

export default {
  components: {
    headerCommon,
    vueSeamlessScroll
  },
  data() {
    return {
      tabIndex: 4,
      mainList: dataJson.data,
      mainList2: dataJson2.data,
      mainList3: dataJson3.data,
      lengedIndex: 1,
      showDialog: false,
      dialogMap: null,
      value: 0,
      value2: [],
      tableData: [],
      startName: '',
      mapIndex: 2,
      hllist1: '',
      hllist2: '',
      hllist3: '',
      hllist4: '',
      hllist1ms: 0,
      hllist2ms: 0,
      hllist3ms: 0,
      hllist4ms: 0,
      chooseCityName: '',
      checked: true,
      dialogMapEarth: null,
      list163: [
        [{ name: '北艾大楼', g: 100, num: 646, value: [121.54284, 31.18961] }, { name: '桂箐数据中心大楼', g: 120, num: 391, value: [121.41127, 31.17118] }, { name: '国定数据中心大楼', g: 160, num: 582, value: [121.51530, 31.30272] }, { name: '呼兰数据中心大楼', g: 60, num: 476, value: [121.45707, 31.35104] }, { name: '纪蕰数据中心大楼', g: 120, num: 402, value: [121.44280, 31.34771] }, { name: '欧阳数据中心大楼', g: 80, num: 724, value: [121.49491, 31.27279] }, { name: '浦川大楼', g: 40, num: 555, value: [121.68401, 31.25773] }, { name: '金海大楼', g: 6, num: 96, value: [121.62954, 31.26740] }, { name: '蕰川数据中心四期大楼', g: 80, num: 1926, value: [121.34857, 31.49385] }, { name: '同普大楼', g: 20, num: 211, value: [121.38270, 31.23619] }, { name: '新桃浦大楼', g: 40, num: 108, value: [121.36387, 31.28687] }, { name: '信息枢纽大楼', g: 4, num: 92, value: [121.51733, 31.24125] }, { name: '信息园区B1楼', g: 40, num: 425, value: [121.54202, 31.13398] }, { name: '信息园区B24B楼', g: 40, num: 1654, value: [121.54242, 31.13235] }, { name: '信息园区B7楼', g: 120, num: 841, value: [121.54416, 31.13237] }, { name: '张东1号楼', g: 120, num: 340, value: [121.64228, 31.22177] }, { name: '周家渡数据中心', g: 40, num: 156, value: [121.50354, 31.18507] }],
        [{ name: '安晓数据中心', g: 400, num: 1901, value: [121.24404, 31.31952] }, { name: '富特数据中心大楼', g: 800, num: 972, value: [121.61088, 31.32671] }, { name: '富特数据中心三期大楼', g: 200, num: 2412, value: [121.61170, 31.32667] }, { name: '桂桥数据中心大楼', g: 280, num: 299, value: [121.63456, 31.25358] }, { name: '华信大楼', g: 440, num: 1680, value: [121.60671, 31.33901] }, { name: '建安数据中心大楼', g: 320, num: 2572, value: [121.10331, 30.86593] }, { name: '金京数据中心大楼', g: 480, num: 1819, value: [121.62213, 31.28442] }, { name: '钦州大楼', g: 480, num: 204, value: [121.40764, 31.18305] }, { name: '兴顺数据中心', g: 400, num: 2551, value: [121.19049, 31.44915] }, { name: '信息园区B2', g: 280, num: 1181, value: [121.54242, 31.13235] }, { name: '真如1号楼', g: 520, num: 234, value: [121.39931, 31.28430] }, { name: '真如3号楼', g: 400, num: 1553, value: [121.39900, 31.28432] }],
        [{ name: '蕰川数据中心二期大楼', g: 2760, num: 3956, value: [121.34857, 31.49385] }, { name: '信息园区B15A楼', g: 1200, num: 1378, value: [121.54242, 31.13235] }, { name: '卓青数据中心大楼', g: 2800, num: 3217, value: [121.09785, 31.18554] }, { name: '漕盈数据中心1号楼', g: 4000, num: 1096, value: [121.10219, 31.19123] }, { name: '信息园区B14B楼', g: 1400, num: 292, value: [121.54202, 31.13398] }]
      ],
      listCy: [{ name: '华京（北块局大楼）', g: 800, num: 920, value: [121.61712, 31.32784] }, { name: '华信大楼', g: 2000, num: 1680, value: [121.60671, 31.33901] }, { name: '浦川大楼', g: 1200, num: 555, value: [121.68401, 31.25773] }, { name: '真如1号楼', g: 950, num: 234, value: [121.39931, 31.28430] }],
      outType: '163出口带宽',
      chartsMapsuzhou: null,
      showmapDialog: false,
      leftMap: null,
      rightMap: null,
      suzhouCity: '苏州吴江',
      leftCity: '苏州吴江'
    };
  },
  filters: {

  },
  computed: {
    classOption() {
      return {
        step: 0.5, // 数值越大速度滚动越快
        limitMoveNum: 2, // 开始无缝滚动的数据量 
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) 
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) 
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      }
    }
  },
  mounted() {
    this.initData()
  },
  created() {
    echarts.registerMap('china', { geoJSON: chinaMap })
    echarts.registerMap('world', { geoJSON: worldMap })
    echarts.registerMap('suzhou', { geoJSON: suzhouMap })
  },
  methods: {
    
    leftCityChange(city, index) {
      return false
      if (this.leftCity != city) {
        this.leftCity = city
        this.initRightMap()
        this.value = index
        this.selectAll(true)
      }
    },
    initData() {
      let _this = this
      this.tabIndex = 1
      this.lengedIndex = 1
      let row = this.mainList[0]
      this.startName = row.xname
      this.value = 0
      this.selectAll(true)
      // this.initLeftMap()
      setTimeout(() => {
        _this.initRightMap()
      }, 10);
    },
    initLeftMap() {
      let _this = this
      if (!this.leftMap) {
        this.leftMap = echarts.init(this.$refs.leftMap, null, { width: this.GLOBAL.relPx(860), height: this.GLOBAL.relPx(660) });
        this.GLOBAL.echartsDomArray.push(this.leftMap)
      }
      let fromList = [
        {
          name: "苏州吴江",
          value: [
            121.472641,
            31.231707
          ]
        }
      ]
      let toList = [
        {
          name: "苏州吴江",
          value: [
            121.472641,
            31.231707
          ]
        },
        {
          name: "北京",
          value: [
            116.41339,
            39.91092
          ]
        },
        {
          name: "中卫",
          value: [
            105.20357,
            37.5057
          ]
        },
        {
          name: "深圳",
          value: [
            113.06455,
            23.54846
          ]
        }
      ]
      let option = {
        backgroundColor: '',
        title: {
          left: 'center',
          textStyle: {
            color: '#fff'
          }
        },
        geo: {
          map: 'china',
          aspectScale: 0.72, //长宽比
          center: [102.51888, 35.84850],
          zoom: 1.7,
          roam: false,
          label: {
            show: false
          },
          emphasis: {
            disabled: true,
            itemStyle: {
              normal: {
                areaColor: {
                  type: 'radial',
                  x: 0.3,
                  y: 0.3,
                  r: 0.8,
                  colorStops: [{
                    offset: 0,
                    color: '#464646' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: '#464646'  // 100% 处的颜色
                  }],
                  globalCoord: true // 缺省为 false
                },
                shadowColor: '#464646',
                shadowOffsetX: 5,
                shadowOffsetY: 5
              },
            },
          },
          itemStyle: {
            normal: {
              areaColor: {
                type: 'radial',
                x: 0.3,
                y: 0.3,
                r: 0.8,
                colorStops: [{
                  offset: 0,
                  color: '#464646' // 0% 处的颜色
                }, {
                  offset: 1,
                  color: '#464646'  // 100% 处的颜色
                }],
                globalCoord: true // 缺省为 false
              },
              shadowColor: '#464646',
              shadowOffsetX: 5,
              shadowOffsetY: 5
            },
          },
          regions: [{
            name: '南海诸岛',
            itemStyle: {
              areaColor: 'rgba(0, 10, 52, 1)',
              borderColor: 'rgba(0, 10, 52, 1)',
              normal: {
                opacity: 0,
                label: {
                  show: false,
                  color: "#009cc9",
                }
              }
            },
          }],
        },
        series: [
          {
            type: 'map',
            roam: false,
            center: [103.41888, 35.74850],
            label: {
              show: true,
              textStyle: {
                color: '#ccc'
              },
            },
            // selectedMode: false,
            selectedMode: 'multiple',
            emphasis: {
              disabled: true
            },
            select: {
              disabled: true,
              itemStyle: {
                borderColor: '#787879',
                borderWidth: 1,
                areaColor: '#334053'
              },
              label: {
                color: '#828282'
              },
            },

            itemStyle: {
              borderColor: '#787879',
              borderWidth: 1,
              areaColor: '#333'
            },
            zoom: 1.7,
            //     roam: false,
            map: 'china', //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            label: {
              show: true,
              formatter: '{b}',
              fontWeight: 'bold',
              fontSize: 16,
              color: '#fff',
              textBorderColor: '#000',
              position: `bottom`,
              textBorderWidth: 3,
              offset: [0, 10]
            },
            data: fromList,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 10
              // return params.data.warning
            },
            rippleEffect: {
              scale: 5, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: '#ffe900',
              }
            },
            zlevel: 10
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: toList,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 10
              // return params.data.warning
            },
            label: {
              show: true,
              formatter: '{b}',
              fontWeight: 'bold',
              fontSize: 16,
              color: '#fff',
              textBorderColor: '#000',
              position: 'bottom',
              textBorderWidth: 3,
              offset: [0, 10]
            },
            rippleEffect: {
              scale: 2, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: 'rgba(246, 238, 58, 0.7)',
              }
            },
            zlevel: 10
          },
        ]
      };
      this.leftMap.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.leftMap.setOption(option);
    },
    initRightMap() {
      let _this = this
      if (!this.rightMap) {
        this.rightMap = echarts.init(this.$refs.rightMap, null, { width: this.GLOBAL.relPx(450), height: this.GLOBAL.relPx(670) });
        this.GLOBAL.echartsDomArray.push(this.rightMap)
      }
      let name = ''
      if (this.leftCity != '苏州吴江') {
        name = this.leftCity
      } else {
        name = this.suzhouCity
      }
      let center = geoCoordMap[name]
      let zoom = 20
      let fromList = [
        {
          name: name,
          value: geoCoordMap[name]
        }
      ]
      let toList1 = []
      let toList2 = []
      let toList3 = []
      let toList4 = []
      let bjData1 = []
      let bjData2 = []
      let bjData3 = []
      let bjData4 = []
      let row = {}
      let value = 0
      this.mainList.forEach((ele, index) => {
        if (ele.xname.indexOf(name) > -1) {
          row = ele
          value = index
        }
      })
      row.list.forEach(ele => {
        if (Number(ele.delay) <= 1) {
          toList1.push({ name: ele.ycity, value: geoCoordMap[ele.ycity] })
          bjData1.push([{ name: name }, { name: ele.ycity }])
        } else if (Number(ele.delay) > 1 && Number(ele.delay) <= 8) {
          toList2.push({ name: ele.ycity, value: geoCoordMap[ele.ycity] })
          bjData2.push([{ name: name }, { name: ele.ycity }])
        } else if (Number(ele.delay) > 8 && Number(ele.delay) <= 20) {
          toList3.push({ name: ele.ycity, value: geoCoordMap[ele.ycity] })
          bjData3.push([{ name: name }, { name: ele.ycity }])
        } else {
          toList4.push({ name: ele.ycity, value: geoCoordMap[ele.ycity] })
          bjData4.push([{ name: name }, { name: ele.ycity }])
        }
      })
      let convert = (data) => {
        let res = [];
        for (var i = 0; i < data.length; i++) {
          var dataItem = data[i];
          var fromCoord = geoCoordMap[dataItem[0].name];
          var toCoord = geoCoordMap[dataItem[1].name];
          if (fromCoord && toCoord) {
            res.push({
              fromName: dataItem[0].name,
              toName: dataItem[1].name,
              coords: [fromCoord, toCoord]
            });
          }
        }
        return res;
      };
      let list1 = convert(bjData1)
      let list2 = convert(bjData2)
      let list3 = convert(bjData3)
      let list4 = convert(bjData4)
      let option = {
        backgroundColor: '',
        title: {
          left: 'center',
          textStyle: {
            color: '#fff'
          }
        },
        geo: {
          map: 'china',
          aspectScale: 0.72, //长宽比
          center: center,
          zoom: zoom,
          roam: true,
          label: {
            show: false
          },
          emphasis: {
            disabled: true,
            itemStyle: {
              normal: {
                areaColor: {
                  type: 'radial',
                  x: 0.3,
                  y: 0.3,
                  r: 0.8,
                  colorStops: [{
                    offset: 0,
                    color: '#464646' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: '#464646'  // 100% 处的颜色
                  }],
                  globalCoord: true // 缺省为 false
                },
                shadowColor: '#464646',
                shadowOffsetX: 5,
                shadowOffsetY: 5
              },
            },
          },
          itemStyle: {
            normal: {
              areaColor: {
                type: 'radial',
                x: 0.3,
                y: 0.3,
                r: 0.8,
                colorStops: [{
                  offset: 0,
                  color: '#464646' // 0% 处的颜色
                }, {
                  offset: 1,
                  color: '#464646'  // 100% 处的颜色
                }],
                globalCoord: true // 缺省为 false
              },
              shadowColor: '#464646',
              shadowOffsetX: 5,
              shadowOffsetY: 5
            },
          },
          regions: [{
            name: '南海诸岛',
            itemStyle: {
              areaColor: 'rgba(0, 10, 52, 1)',
              borderColor: 'rgba(0, 10, 52, 1)',
              normal: {
                opacity: 0,
                label: {
                  show: false,
                  color: "#009cc9",
                }
              }
            },
          }],
        },
        series: [
          {
            type: 'map',
            roam: true,
            center: center,
            zoom: zoom,
            label: {
              show: true,
              textStyle: {
                color: '#ccc'
              },
            },
            // selectedMode: false,
            selectedMode: 'multiple',
            emphasis: {
              disabled: true
            },
            select: {
              disabled: true,
              itemStyle: {
                borderColor: '#787879',
                borderWidth: 1,
                areaColor: '#334053'
              },
              label: {
                color: '#828282'
              },
            },

            itemStyle: {
              borderColor: '#787879',
              borderWidth: 1,
              areaColor: '#333'
            },

            //     roam: false,
            map: 'china', //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
          }
          ,
          {
            name: '苏州吴江',
            type: 'lines',
            zlevel: 10,
            symbol: ['none'],
            symbolSize: 10,
            effect: {
              show: true,
              period: 4, //箭头指向速度，值越小速度越快
              trailLength: 0.1, //特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: 'arrow', //箭头图标
              symbolSize: 8, //图标大小
              opacity: 0.5,
            },
            lineStyle: {
              normal: {
                color: 'rgba(2, 130, 235,0.5)',
                width: 1,
                opacity: 0.2,
                type: 'solid',
                curveness: 0.5
              }
            },
            data: list1,
            zlevel: 2
          },
          {
            name: '苏州吴江',
            type: 'lines',
            zlevel: 10,
            symbol: ['none'],
            symbolSize: 10,
            effect: {
              show: true,
              period: 4, //箭头指向速度，值越小速度越快
              trailLength: 0.1, //特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: 'arrow', //箭头图标
              symbolSize: 8, //图标大小
              opacity: 0.5,
            },
            lineStyle: {
              normal: {
                color: 'rgba(31, 207, 143,0.5)',
                width: 1,
                opacity: 0.2,
                type: 'solid',
                curveness: 0.5
              }
            },
            data: list2,
            zlevel: 2
          },
          {
            name: '苏州吴江',
            type: 'lines',
            zlevel: 10,
            symbol: ['none'],
            symbolSize: 10,
            effect: {
              show: true,
              period: 4, //箭头指向速度，值越小速度越快
              trailLength: 0.1, //特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: 'arrow', //箭头图标
              symbolSize: 8, //图标大小
              opacity: 0.5,
            },
            lineStyle: {
              normal: {
                color: 'rgba(19, 200, 222,0.5)',
                width: 1,
                opacity: 0.2,
                type: 'solid',
                curveness: 0.5
              }
            },
            data: list3,
            zlevel: 2
          },
          {
            name: '苏州吴江',
            type: 'lines',
            zlevel: 10,
            symbol: ['none'],
            symbolSize: 10,
            effect: {
              show: true,
              period: 4, //箭头指向速度，值越小速度越快
              trailLength: 0.1, //特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: 'arrow', //箭头图标
              symbolSize: 8, //图标大小
              opacity: 0.5,
            },
            lineStyle: {
              normal: {
                color: 'rgba(19, 200, 222,0.5)',
                width: 1,
                opacity: 0.2,
                type: 'solid',
                curveness: 0.5
              }
            },
            data: list4,
            zlevel: 2
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: fromList,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 10
              // return params.data.warning
            },
            rippleEffect: {
              scale: 2, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            label: {
              show: true,
              formatter: '{b}',
              fontWeight: 'bold',
              fontSize: 12,
              color: '#fff',
              textBorderColor: '#000',
              position: 'bottom',
              textBorderWidth: 3,
              offset: [0, 5]
            },
            itemStyle: {
              normal: {
                color: '#FFEE33',
              }
            },
            zlevel: 10
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: toList1,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 9
              // return params.data.warning
            },
            rippleEffect: {
              scale: 3, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            label: {
              show: true,
              formatter: '{b}',
              fontWeight: 'bold',
              fontSize: 12,
              color: '#fff',
              textBorderColor: '#000',
              position: 'bottom',
              textBorderWidth: 3,
              offset: [0, 5]
            },
            itemStyle: {
              normal: {
                color: '#0282EB',
              }
            },
            zlevel: 3
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: toList2,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 9
              // return params.data.warning
            },
            rippleEffect: {
              scale: 3, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            label: {
              show: true,
              formatter: '{b}',
              fontWeight: 'bold',
              fontSize: 12,
              color: '#fff',
              textBorderColor: '#000',
              position: 'bottom',
              textBorderWidth: 3,
              offset: [0, 5]
            },
            itemStyle: {
              normal: {
                color: '#1FCF8F',
              }
            },
            zlevel: 3
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: toList3,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 9
              // return params.data.warning
            },
            rippleEffect: {
              scale: 3, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            label: {
              show: true,
              formatter: '{b}',
              fontWeight: 'bold',
              fontSize: 12,
              color: '#fff',
              textBorderColor: '#000',
              position: 'bottom',
              textBorderWidth: 3,
              offset: [0, 5]
            },
            itemStyle: {
              normal: {
                color: '#13C8DE',
              }
            },
            zlevel: 3
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: toList4,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 9
              // return params.data.warning
            },
            rippleEffect: {
              scale: 3, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            label: {
              show: true,
              formatter: '{b}',
              fontWeight: 'bold',
              fontSize: 12,
              color: '#fff',
              textBorderColor: '#000',
              position: 'bottom',
              textBorderWidth: 3,
              offset: [0, 5]
            },
            itemStyle: {
              normal: {
                color: '#13C8DE',
              }
            },
            zlevel: 3
          },
        ]
      };
      this.rightMap.off('click')
      this.rightMap.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.rightMap.setOption(option);
      this.rightMap.on("georoam", function (params) {
        let option = _this.rightMap.getOption(); //获得option对象
        if (params.zoom != null && params.zoom != undefined) {
          //捕捉到缩放时
          option.geo[0].zoom = option.series[0].zoom; //下层geo的缩放等级跟着上层的geo一起改变
          option.geo[0].center = option.series[0].center; //下层的geo的中心位置随着上层geo一起改变
          option.geo[0].animationDurationUpdate = 0;//防止地图缩放卡顿
          option.series[0].animationDurationUpdate = 0;//防止地图缩放卡顿
        } else {
          //捕捉到拖曳时
          option.geo[0].center = option.series[0].center; //下层的geo的中心位置随着上层geo一起改变
        }
        _this.rightMap.setOption(option); //设置option
      });
    },
    outTypeFun() {
      this.outType = this.outType == '163出口带宽' ? '城域网出口带宽' : '163出口带宽'
      if (this.outType == '163出口带宽') {
        this.mapIndex = 2
      }
    },
    initEarth(fromName, toNameList, xname) {
      let _this = this
      let fromList = [{ name: fromName, value: geoCoordMap[fromName], xname: xname ? xname : '' }]
      let toList = []
      let bjData = []
      toNameList.forEach(ele => {
        toList.push({ name: ele.ycity, value: geoCoordMap[ele.ycity], xname: xname ? xname : '' })
        bjData.push([{ name: fromName }, { name: ele.ycity }])
      })
      let convert = (data) => {
        let res = [];
        for (var i = 0; i < data.length; i++) {
          var dataItem = data[i];
          var fromCoord = geoCoordMap[dataItem[0].name];
          var toCoord = geoCoordMap[dataItem[1].name];
          if (fromCoord && toCoord) {
            res.push({
              fromName: dataItem[0].name,
              toName: dataItem[1].name,
              coords: [fromCoord, toCoord]
            });
          }
        }
        return res;
      };
      let list = convert(bjData)
      let option = {
        backgroundColor: '',
        geo: {
          map: 'world',
          roam: false,
          zoom: 1.2,
          label: {
            emphasis: {
              show: false
            }
          },
          silent: true,
          itemStyle: {
            normal: {
              areaColor: {
                type: 'radial',
                x: 0.3,
                y: 0.3,
                r: 0.8,
                colorStops: [{
                  offset: 0,
                  color: '#464646' // 0% 处的颜色
                }, {
                  offset: 1,
                  color: '#464646'  // 100% 处的颜色
                }],
                globalCoord: true // 缺省为 false
              },
              shadowColor: '#464646',
              shadowOffsetX: 5,
              shadowOffsetY: 5
            },
          },
        },
        series: [
          {
            type: 'map',
            roam: false,
            label: {
              show: false,
              textStyle: {
                color: '#ccc'
              },
            },
            // selectedMode: false,
            selectedMode: 'multiple',
            emphasis: {
              disabled: true
            },
            select: {
              disabled: true,
              itemStyle: {
                borderColor: '#787879',
                borderWidth: 1,
                areaColor: '#334053'
              },
              label: {
                color: '#828282'
              },
            },

            itemStyle: {
              borderColor: '#787879',
              borderWidth: 1,
              areaColor: '#333'
            },
            zoom: 1.2,
            //     roam: false,
            map: 'world', //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
          },
          {
            name: '苏州吴江',
            type: 'lines',
            zlevel: 10,
            symbol: ['none'],
            symbolSize: 10,
            effect: {
              show: true,
              period: 4, //箭头指向速度，值越小速度越快
              trailLength: 0.1, //特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: 'arrow', //箭头图标
              symbolSize: 8, //图标大小
              opacity: 0.5,
            },
            lineStyle: {
              normal: {
                color: 'rgba(0, 228, 255,0.5)',
                width: 1,
                opacity: 0.2,
                type: 'solid',
                curveness: 0.5
              }
            },
            data: list,
            zlevel: 2
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: fromList,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 10
              // return params.data.warning
            },
            rippleEffect: {
              scale: 2, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: '#FF9633',
              }
            },
            zlevel: 10
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: toList,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 9
              // return params.data.warning
            },
            rippleEffect: {
              scale: 3, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: '#00E4FF',
              }
            },
            zlevel: 3
          },
        ]
      };
      if (!this.dialogMapEarth) {
        this.dialogMapEarth = echarts.init(this.$refs.dialogMapEarthRef, null, { width: this.GLOBAL.relPx(860), height: this.GLOBAL.relPx(660) });
        this.GLOBAL.echartsDomArray.push(this.dialogMapEarth)
      }
      this.dialogMapEarth.off('click')
      this.dialogMapEarth.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.dialogMapEarth.setOption(option);
      this.dialogMapEarth.on('click', function (params) {
        let seriesType = params.seriesType
        if (seriesType === 'effectScatter') {
          let toName = params.name
          _this.mapClickLinesRow(toName)
        }
      });
    },
    selectAll(type) {
      this.value2 = []
      let list = this.mainList
      if (type) {
        list.forEach((ele, index) => {
          this.value2.push(index)
        })
      } else {
        this.value2 = []
      }
      this.checked = type
      this.endChangeFun(this.value2)
    },
    mapIndexChangeFun(index) {
      if (this.mapIndex != index) {
        this.mapIndex = index
      }
      this.showmapDialog = true
      this.initMapsuzhou()
    },
    mapClickLinesRow(name) {
      let list = this.tabIndex === 1 ? this.mainList : this.tabIndex === 2 ? this.mainList2 : this.mainList3
      let row = {}
      let value = 0
      list.forEach((ele, index) => {
        if (ele.xname.indexOf(name) > -1) {
          row = ele
          value = index
        }
      })
      if (row.xname.indexOf('_') > -1) {
        let newArr = []
        row.list.forEach(ele => {
          newArr.push({ delay: ele.delay, jitter: ele.jitter, lost: ele.lost, xcity: ele.xcity.split('_')[0], ycity: ele.ycity.split('_')[0] })
        })
        let newName = row.xname.split('_')[0]
        this.startName = row.xname
        this.value = value
        // this.value2 = []
        this.initRightMap(true)
        // this.openMap(newName, newArr)
        // this.initEarth(newName, newArr)
        this.centerChoose(row)
      } else {
        this.startName = row.xname
        this.value = value
        // this.value2 = []
        this.initRightMap(true)
        // this.openMap(row.xname, row.list)
        // this.initEarth(row.xname, row.list)
        this.centerChoose(row)
      }
    },
    endChangeFun(e) {
      let row = this.mainList[this.value]
      let list = []
      row.list.forEach((ele, index) => {
        if (e.indexOf(index) > -1) {
          list.push(ele)
        }
      })
      this.centerChoose(row)
      this.centerChoose2(row, this.tabIndex === 2 ? 2 : 0)
    },
    centerChoose(row) {
      this.chooseCityName = row.xname
      let str2 = JSON.parse(JSON.stringify(row.list))
      let data2 = str2.sort(function (a, b) {
        return Number(a.delay) - Number(b.delay)
      });
      let list = []
      data2.forEach((ele, index) => {
        if (this.value2.indexOf(index) > -1) {
          list.push(ele)
        }
      })
      let str = JSON.parse(JSON.stringify(list))
      let data = str.sort(function (a, b) {
        return Number(a.delay) - Number(b.delay)
      });
      let length = data.length
      let num1 = 2
      let num2 = 5
      let num3 = Math.floor(length * 0.35)
      let num4 = data.length - num1 - num2 - num3
      let list1 = []
      let list2 = []
      let list3 = []
      let list4 = []
      data2.forEach(ele => {
        if (Number(ele.delay) <= 1) {
          list1.push(ele)
        } else if (Number(ele.delay) <= 8) {
          list2.push(ele)
        } else if (Number(ele.delay) <= 20) {
          list3.push(ele)
        } else {
          list4.push(ele)
        }
      })
      let arr1 = []
      list1.forEach(ele => {
        arr1.push(ele.ycity)
      })
      let arr2 = []
      list2.forEach(ele => {
        arr2.push(ele.ycity)
      })
      let arr3 = []
      list3.forEach(ele => {
        arr3.push(ele.ycity)
      })
      let arr4 = []
      list4.forEach(ele => {
        arr4.push(ele.ycity)
      })
      this.hllist1ms = list1 && list1.length > 0 ? list1[list1.length - 1].delay ? list1[list1.length - 1].delay : 0 : '-'
      this.hllist2ms = list2 && list2.length > 0 ? list2[list2.length - 1].delay ? list2[list2.length - 1].delay : 0 : '-'
      this.hllist3ms = list3 && list3.length > 0 ? list3[list3.length - 1].delay ? list3[list3.length - 1].delay : 0 : '-'
      this.hllist4ms = list4 && list4.length > 0 ? list4[list4.length - 1].delay ? list4[list4.length - 1].delay : 0 : '-'
      // this.hllist1ms = Math.ceil((list1[list1.length - 1].delay) / 10) * 10
      // this.hllist2ms = Math.ceil((list2[list2.length - 1].delay) / 10) * 10
      // this.hllist3ms = Math.ceil((list3[list3.length - 1].delay) / 10) * 10
      // this.hllist4ms = Math.ceil((list4[list4.length - 1].delay) / 10) * 10
      this.hllist1 = arr1.join('、')
      this.hllist2 = arr2.join('、')
      this.hllist3 = arr3.join('、')
      this.hllist4 = arr4.join('、')

      this.tableData = data2
    },
    centerChoose2(row, index) {
      // 注释-暂时取消 2023年10月1日 15:17:03
      // if (this.showDialog) {
      //   return false
      // }
      this.startName = row.xname
      this.value = index
      // this.value2 = [9999]
      // this.selectAll(true)
      // this.tableData = row.list
      // this.showDialog = true
      let str2 = JSON.parse(JSON.stringify(row.list))
      let data2 = str2.sort(function (a, b) {
        return Number(a.delay) - Number(b.delay)
      });
      let list = []
      data2.forEach((ele, index) => {
        if (this.value2.indexOf(index) > -1) {
          list.push(ele)
        }
      })
      let str = JSON.parse(JSON.stringify(list))
      let data = str.sort(function (a, b) {
        return Number(a.delay) - Number(b.delay)
      });

      if (row.xname.indexOf('_') > -1) {
        let newArr = []
        list.forEach(ele => {
          newArr.push({ delay: ele.delay, jitter: ele.jitter, lost: ele.lost, xcity: ele.xcity.split('_')[0], ycity: ele.ycity.split('_')[0] })
        })
        let newName = row.xname.split('_')[0]
        this.openMap(newName, newArr, row.xname)
        // this.initEarth(newName, newArr, row.xname)
      } else {
        this.openMap(row.xname, row.list)
        // this.initEarth(row.xname, list)
      }
    },
    openMap(fromName, toNameList, xname) {
      let _this = this
      if (!this.dialogMap) {
        this.dialogMap = echarts.init(this.$refs.dialogMap, null, { width: this.GLOBAL.relPx(860), height: this.GLOBAL.relPx(660) });
        this.GLOBAL.echartsDomArray.push(this.dialogMap)
      }
      let fromList = [{ name: fromName, value: geoCoordMap[fromName], xname: xname ? xname : '' }]
      let toList = []
      let bjData = []
      toNameList.forEach(ele => {
        toList.push({ name: ele.ycity, value: geoCoordMap[ele.ycity], xname: xname ? xname : '' })
        bjData.push([{ name: fromName }, { name: ele.ycity }])
      })
      let convert = (data) => {
        let res = [];
        for (var i = 0; i < data.length; i++) {
          var dataItem = data[i];
          var fromCoord = geoCoordMap[dataItem[0].name];
          var toCoord = geoCoordMap[dataItem[1].name];
          if (fromCoord && toCoord) {
            res.push({
              fromName: dataItem[0].name,
              toName: dataItem[1].name,
              coords: [fromCoord, toCoord]
            });
          }
        }
        return res;
      };
      let list = convert(bjData)
      let option = {
        backgroundColor: '',
        title: {
          left: 'center',
          textStyle: {
            color: '#fff'
          }
        },
        geo: {
          map: 'china',
          aspectScale: 0.72, //长宽比
          center: [102.51888, 35.84850],
          zoom: 1.7,
          roam: false,
          label: {
            show: false
          },
          emphasis: {
            disabled: true,
            itemStyle: {
              normal: {
                areaColor: {
                  type: 'radial',
                  x: 0.3,
                  y: 0.3,
                  r: 0.8,
                  colorStops: [{
                    offset: 0,
                    color: '#464646' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: '#464646'  // 100% 处的颜色
                  }],
                  globalCoord: true // 缺省为 false
                },
                shadowColor: '#464646',
                shadowOffsetX: 5,
                shadowOffsetY: 5
              },
            },
          },
          itemStyle: {
            normal: {
              areaColor: {
                type: 'radial',
                x: 0.3,
                y: 0.3,
                r: 0.8,
                colorStops: [{
                  offset: 0,
                  color: '#464646' // 0% 处的颜色
                }, {
                  offset: 1,
                  color: '#464646'  // 100% 处的颜色
                }],
                globalCoord: true // 缺省为 false
              },
              shadowColor: '#464646',
              shadowOffsetX: 5,
              shadowOffsetY: 5
            },
          },
          regions: [{
            name: '南海诸岛',
            itemStyle: {
              areaColor: 'rgba(0, 10, 52, 1)',
              borderColor: 'rgba(0, 10, 52, 1)',
              normal: {
                opacity: 0,
                label: {
                  show: false,
                  color: "#009cc9",
                }
              }
            },
          }],
        },
        series: [
          {
            name: '苏州吴江',
            type: 'lines',
            zlevel: 10,
            symbol: ['none'],
            symbolSize: 10,
            effect: {
              show: true,
              period: 4, //箭头指向速度，值越小速度越快
              trailLength: 0.1, //特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: 'arrow', //箭头图标
              symbolSize: 8, //图标大小
              opacity: 0.5,
            },
            lineStyle: {
              normal: {
                color: 'rgba(0, 228, 255,0.5)',
                width: 1,
                opacity: 0.2,
                type: 'solid',
                curveness: -0.2
              }
            },
            data: list,
            zlevel: 2
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: fromList,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 10
              // return params.data.warning
            },
            rippleEffect: {
              scale: 2, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            label: {
              show: true,
              formatter: '{b}',
              fontWeight: 'bold',
              fontSize: 12,
              color: '#fff',
              textBorderColor: '#000',
              position: 'bottom',
              textBorderWidth: 3,
              offset: [0, 5]
            },
            itemStyle: {
              normal: {
                color: '#FFEE33',
              }
            },
            zlevel: 10
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: toList,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 9
              // return params.data.warning
            },
            rippleEffect: {
              scale: 3, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: '#00E4FF',
              }
            },
            zlevel: 3
          },
          {
            type: 'map',
            roam: false,
            center: [103.41888, 35.74850],
            label: {
              show: true,
              textStyle: {
                color: '#ccc'
              },
            },
            // selectedMode: false,
            selectedMode: 'multiple',
            emphasis: {
              disabled: true
            },
            select: {
              disabled: true,
              itemStyle: {
                borderColor: '#787879',
                borderWidth: 1,
                areaColor: '#334053'
              },
              label: {
                color: '#828282'
              },
            },

            itemStyle: {
              borderColor: '#787879',
              borderWidth: 1,
              areaColor: '#333'
            },
            zoom: 1.7,
            //     roam: false,
            map: 'china', //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
          },
        ]
      };
      this.dialogMap.off('click')
      this.dialogMap.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.dialogMap.setOption(option);
    },
    initMap() {
      if (!this.chartsMap) {
        this.chartsMap = echarts.init(this.$refs.map, null, { width: this.GLOBAL.relPx(380), height: this.GLOBAL.relPx(270) });
        this.GLOBAL.echartsDomArray.push(this.chartsMap)
      }
      let data = []
      if (this.mapIndex == 1) {
        data = [
          { name: '上海信息园区', value: [121.54462, 31.13231], warning: 5 },
          { name: '京津冀产业园数据中心', value: [116.84461, 39.63426], warning: 5 },
          { name: '武汉未来城二期', value: [114.51023, 30.39377], warning: 5 },
          { name: '常州国际数据中心', value: [119.87584, 31.80354], warning: 5 }
        ]
      }
      if (this.mapIndex == 2) {
        data = [
          { name: '上海蕰川数据中心', value: [121.42574, 31.42167], warning: 5 },
          { name: '兆维数据中心', value: [116.49956, 39.97992], warning: 5 },
          { name: '南京电信河西国际数据中心', value: [118.74620, 32.03908], warning: 5 },
          { name: '福州云谷仓科数据中心', value: [119.35398, 26.03350], warning: 5 }
        ]
      }
      if (this.mapIndex == 3) {
        data = [
          { name: '上海卓青数据中心', value: [121.09785, 31.18554], warning: 5 },
          { name: '芜湖城东数据中心', value: [118.43798, 31.35540], warning: 5 },
          { name: '杭州兴议数据中心', value: [120.24545, 30.21114], warning: 5 },
        ]
      }
      let option = {
        backgroundColor: '',
        title: {
          left: 'center',
          textStyle: {
            color: '#fff'
          }
        },
        geo: {
          map: 'china',
          roam: false,
          zoom: 1.5,
          center: [103.41888, 35.54850],
          label: {
            show: false,
            color: "rgba(255,255,255,0.6)",
            fontSize: this.GLOBAL.relPx(12),
            fontFamily: 'Microsoft YaHei',
            // textBorderColor: '#f00000',
            // textBorderWidth: 5,
            // fontWeight: 'bold'
            emphasis: {
              show: true
            }
          },
          silent: true,
          itemStyle: {
            normal: {
              areaColor: 'rgba(51, 51, 51, 0.9)',
              borderColor: '#111'
            },
            emphasis: {
              areaColor: '#2a333d'
            }
          }
        },
        series: [
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: data,
            showEffectOn: 'render',
            tooltip: {
              show: true,
              formatter: '{a}',
              triggerOn: "click",
            },
            symbolSize(value, params) {
              // return 0
              return params.data.warning
            },
            rippleEffect: {
              scale: 4, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "rgba(0, 214, 223,0.3)"
                },
                {
                  offset: 1,
                  color: "rgba(0, 214, 223,1)"
                }
                ]),
                shadowBlur: 10,
                shadowColor: '#333'
              }
            },
            emphasis: {
              show: false,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "rgba(0, 154, 228,0.3)"
                },
                {
                  offset: 1,
                  color: "rgba(0, 154, 228,1)"
                }
                ]),
              }
            },
            zlevel: 3
          },
          {
            type: 'map',
            roam: false,
            center: [103.41888, 35.74850],
            label: {
              show: true,
              textStyle: {
                color: '#828282'
              },
            },
            // selectedMode: false,
            selectedMode: 'multiple',
            emphasis: {
              disabled: true
            },
            select: {
              disabled: true,
              itemStyle: {
                borderColor: '#787879',
                borderWidth: 1,
                areaColor: '#334053'
              },
              label: {
                color: '#828282'
              },
            },

            itemStyle: {
              borderColor: '#787879',
              borderWidth: 1,
              areaColor: '#333'
            },
            zoom: 1.7,
            //     roam: false,
            map: 'china', //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
          },
        ]
      };
      this.chartsMap.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsMap.setOption(option);
      this.chartsMap.on('click', function (params) {
      });
    },
    initMapsuzhou() {
      if (!this.chartsMapsuzhou) {
        this.chartsMapsuzhou = echarts.init(this.$refs.mapsuzhou, null, { width: this.GLOBAL.relPx(340), height: this.GLOBAL.relPx(376) });
        this.GLOBAL.echartsDomArray.push(this.chartsMapsuzhou)
      }
      let data = []
      if (this.outType == '163出口带宽') {
        if (this.mapIndex == 1) {
          data = this.list163[0]
        }
        if (this.mapIndex == 2) {
          data = this.list163[1]
        }
        if (this.mapIndex == 3) {
          data = this.list163[2]
        }
      } else {
        data = this.listCy
      }
      let option = {
        backgroundColor: '',
        title: {
          left: 'center',
          textStyle: {
            color: '#fff'
          }
        },
        geo: {
          map: 'suzhou',
          roam: false,
          zoom: 1.25,
          center: [121.47066, 31.27382],
          label: {
            show: false,
            color: "rgba(255,255,255,0.6)",
            fontSize: this.GLOBAL.relPx(12),
            fontFamily: 'Microsoft YaHei',
            // textBorderColor: '#f00000',
            // textBorderWidth: 5,
            // fontWeight: 'bold'
            emphasis: {
              show: true
            }
          },
          silent: true,
          itemStyle: {
            normal: {
              areaColor: 'rgba(51, 51, 51, 0.9)',
              borderColor: '#111'
            },
            emphasis: {
              areaColor: '#2a333d'
            }
          }
        },
        tooltip: {
          trigger: 'item',
          // className: 'custom-tooltip-box', // 命名父级类名
          triggerOn: 'mousemove',
          position: 'right',
          formatter: '{b}',
          backgroundColor: 'none',
          borderColor: 'none',
          borderWidth: 0,
          textStyle: {
            color: '#00E4FF'
          }
          // formatter: function (params) {
          //   const { name, value } = params
          //   var htmlText = `<div class='custom-tooltip-style'>  
          //                   <div class='custom-tooltip-top'>${name}</div>
          //               </div>`
          //   return htmlText
          // }
        },
        series: [
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: data,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 6
              // return params.data.warning
            },
            // rippleEffect: {
            // scale: 4, // 波纹的最大缩放比例
            // brushType: 'stroke'
            // },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "rgba(0, 214, 223,0.3)"
                },
                {
                  offset: 1,
                  color: "rgba(0, 214, 223,1)"
                }
                ]),
                shadowBlur: 10,
                shadowColor: '#333'
              }
            },
            emphasis: {
              show: false,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "rgba(0, 154, 228,0.3)"
                },
                {
                  offset: 1,
                  color: "rgba(0, 154, 228,1)"
                }
                ]),
              }
            },
            zlevel: 3
          },
          {
            type: 'map',
            roam: false,
            center: [121.47066, 31.27382],
            label: {
              show: true,
              textStyle: {
                color: '#828282',
                fontSize: 8
              },
            },
            selectedMode: false,
            // selectedMode: 'multiple',
            emphasis: {
              disabled: true
            },
            tooltip: {
              show: false
            },
            select: {
              disabled: true,
              itemStyle: {
                borderColor: '#787879',
                borderWidth: 1,
                areaColor: '#334053'
              },
              label: {
                color: '#828282'
              },
            },

            itemStyle: {
              borderColor: '#787879',
              borderWidth: 1,
              areaColor: '#333'
            },
            zoom: 1.2,
            //     roam: false,
            map: 'suzhou', //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
          },
        ]
      };
      this.chartsMapsuzhou.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsMapsuzhou.setOption(option);
    },
    lengedChange(index) {
      if (this.lengedIndex != index) {
        this.lengedIndex = index
        this.$refs.scrollContainer.scrollTop = 0;
        this.$refs.scrollContainer.scrollLeft = 0;
      }
    },
    tabChange(index) {
      if (index != 4) {
        this.$router.push({
          path: '/page5',
          query: {
            index: index
          }
        })
        return false
      }
      if (this.tabIndex != index) {
        this.tabIndex = index
        this.lengedIndex = 1
        let row = index === 1 ? this.mainList[23] : index === 2 ? this.mainList2[2] : this.mainList3[0]
        this.startName = row.xname
        this.value = index === 1 ? 23 : index === 2 ? 2 : 0
        // this.value2 = 9999
        this.selectAll(true)
        // this.tableData = row.list
        // this.centerChoose(row)
        // this.centerChoose2(row, index === 2 ? 2 : 0)
      }
    },
  },
  destroyed() {
  },
};
</script>

<style lang="scss" scoped>
.page6 {
  .main {
    padding-top: 10px;

    .tabCore {
      width: 700px;

      .tab {
        .item {
          background-image: url('~@/assets/images/page5/tab_item.png');
          background-size: cover;
          width: 231px;
          height: 79px;
          cursor: pointer;

          .icon {
            width: 40px;
            height: 40px;
            margin-top: 19.5px;
            opacity: 0.5;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .word {
            line-height: 79px;
            font-size: 26px;
            margin-left: 20px;
            opacity: 0.5;
          }

          &.on {
            background-image: url('~@/assets/images/page5/tab_item_on.png');

            .icon {
              opacity: 1;
            }

            .word {
              opacity: 1;
            }
          }
        }
      }

      .tabDownCore {

        .tabDown {
          padding: 25px 18px 0 18px;
          margin-top: 15px;
          position: relative;

          .imgCore {
            background-color: rgba($color: #ccc, $alpha: 0.05);
            border-radius: 10px;
            padding: 40px 0;
          }

          .img {
            width: 100%;
            display: block;
            margin: 0 auto;
          }

          .lengedBottom {
            margin-top: 20px;

            .item {
              margin-left: 20px;

              .color {
                width: 12px;
                height: 4px;
                background: #00DEFF;
                margin-top: 7px;
              }

              .text {
                font-size: 12px;
                padding-left: 10px;
              }

              &.on {
                .color {
                  background: #00FF90;
                }
              }
            }
          }

          &.tab1 {
            padding-left: 0;
            padding-right: 0;
            margin-top: 0;
            padding-top: 15px;

            .lengedBottom {
              position: absolute;
              bottom: 10px;
              right: 20px;
            }
          }

          &.tab3 {
            padding-left: 0;
            padding-right: 0;
            margin-top: 0px;
            padding-top: 15px;

            .img {
              width: 509px;
              height: 344px;
              display: block;
              margin: 0 auto;
            }
          }
        }
      }
    }

    .centerRight {
      width: 469px;

      .top {
        background-color: rgba($color: #26262A, $alpha: 0.15);
        // height: 670px;
        // width: 100%;
        border: 1px solid rgba($color: #ffffff, $alpha: 0.15);

        .titleCore {
          padding: 10px 0 10px 12px;
          border-bottom: 1px solid rgba($color: #ffffff, $alpha: 0.15);

          .icon {
            width: 25px;
            height: 18px;

            img {
              width: 100%;
              height: 100%
            }
          }

          .title {
            font-size: 14px;
            line-height: 18px;
            margin-left: 10px;
          }
        }

        .hlCore {
          position: relative;
          background-image: url('~@/assets/images/1920/page5/hl.png');
          background-size: cover;
          width: 469px;
          height: 753px;

          .hlname1 {
            width: 220px;
            height: 270px;
            position: absolute;
            top: 155px;
            left: 165px;
            z-index: 1;
            display: flex; //弹性布局
            justify-content: center; //子元素相对父元素水平（主轴）居中
            align-items: center; //子元素相对父元素垂直（交叉轴）居中
            font-size: 13px;
            line-height: 18px;
          }

          .hlname2 {
            width: 240px;
            height: 110px;
            position: absolute;
            top: 590px;
            left: 125px;
            z-index: 1;
            display: flex; //弹性布局
            justify-content: center; //子元素相对父元素水平（主轴）居中
            align-items: center; //子元素相对父元素垂直（交叉轴）居中
            font-size: 13px;
            line-height: 18px;
          }

          .hlname3 {
            width: 70px;
            height: 95px;
            position: absolute;
            top: 530px;
            left: 10px;
            z-index: 1;
            display: flex; //弹性布局
            justify-content: center; //子元素相对父元素水平（主轴）居中
            align-items: center; //子元素相对父元素垂直（交叉轴）居中
            font-size: 13px;
            line-height: 18px;
          }

          .hlname4 {
            width: 90px;
            height: 40px;
            position: absolute;
            top: 480px;
            left: 85px;
            z-index: 1;
            display: flex; //弹性布局
            justify-content: center; //子元素相对父元素水平（主轴）居中
            align-items: center; //子元素相对父元素垂直（交叉轴）居中
            font-size: 13px;
            line-height: 18px;
            text-align: center;
          }

          .hlms1 {
            font-size: 17px;
            color: #00DDFF;
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1;
          }

          .hlms2 {
            font-size: 17px;
            color: #1FCF8F;
            position: absolute;
            top: 720px;
            right: 10px;
            z-index: 1;
          }

          .hlms3 {
            font-size: 17px;
            color: #00C0FA;
            position: absolute;
            top: 720px;
            left: 10px;
            z-index: 1;
          }

          .hlms4 {
            font-size: 17px;
            color: #0273EB;
            position: absolute;
            top: 450px;
            left: 10px;
            z-index: 1;
          }

          .hl1 {
            background-image: url('~@/assets/images/page5/hl-4.png');
            background-size: cover;
            width: 290px;
            height: 429px;
            position: absolute;
            z-index: 1;
            top: 0;
            left: 22px;
          }

          .hl2 {
            background-image: url('~@/assets/images/page5/hl-3.png');
            background-size: cover;
            width: 280px;
            height: 451px;
            position: absolute;
            z-index: 2;
            top: 144px;
            left: 110px;
          }

          .hl3 {
            background-image: url('~@/assets/images/page5/hl-2.png');
            background-size: cover;
            width: 281px;
            height: 186px;
            position: absolute;
            z-index: 1;
            top: 442px;
            left: 3px;
          }

          .hl4 {
            background-image: url('~@/assets/images/page5/hl-1.png');
            background-size: cover;
            width: 151px;
            height: 120px;
            position: absolute;
            z-index: 1;
            top: 386px;
            left: 0px;
          }
        }
      }

      .down {
        margin-top: 30px;

        .item {
          width: 230px;
          height: 37px;
          background: rgba($color: #26262A, $alpha: 1);
          border: 1px solid #FFFFFF;
          border-radius: 2px;
          opacity: 0.5;
          box-sizing: border-box;
          padding-top: 8px;
          cursor: pointer;

          // margin: 0 20px;
          .icon {
            width: 21px;
            height: 21px;
            margin-right: 10px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .text {
            font-size: 15px;
          }

          &.on {
            opacity: 1;
            border-color: #2A53A4;
            background-color: rgba($color: #2A53A4, $alpha: 0.5);
          }
        }
      }
    }



    .right {
      width: 930px;
      display: none;

      .topFour {
        color: #ffffff;

        .son {
          width: 450px;
          height: 158px;
          background: rgba($color: #26262A, $alpha: 0.15);
          box-sizing: border-box;
          padding: 20px 0 0 20px;
          margin-bottom: 20px;

          .title {
            font-size: 14px;
            line-height: 14px;
          }

          .value {
            font-size: 36px;
            line-height: 36px;
            text-align: center;
            padding-top: 40px;

            span {
              font-size: 20px;
            }
          }
        }
      }


    }
  }

  .center {
    width: 1300px;
    overflow: hidden;
    display: none;

    .centerBox {
      width: 100%;
    }

    .centerCore {
      width: 900px;
      height: 680px;
      overflow: scroll;
      box-sizing: border-box;
      display: none;

      &::-webkit-scrollbar {
        background: none;
        border-radius: 10px;
      }

      &::-webkit-scrollbar-thumb {
        width: 20px;
        background-color: rgba($color: #ffffff, $alpha: 0.1);
        border-radius: 10px;
      }

      &::-webkit-scrollbar-track {
        background: none;
        border-radius: 10px;
      }

      // 108 *34
      .topProvice {
        width: 3672px;
        margin-left: 108px;
        position: sticky;
        top: 0px;
        z-index: 5;

        .item {
          width: 108px;
          text-align: center;
          position: relative;
          z-index: 1;

          span {
            background-color: rgba($color: #000000, $alpha: 0.4);
            padding: 5px;
            border-radius: 3px;
          }
        }
      }

      .downProvice {
        width: 3780px;

        // z-index: 5;
        .item {
          margin-top: 33px;
          position: relative;
          z-index: 1;

          // width: 50px;
          .xname {
            width: 108px;
            line-height: 34px;
            position: sticky;
            left: 0;
            z-index: 5;
            cursor: pointer;

            span {
              background-color: rgba($color: #000000, $alpha: 0.4);
              padding: 5px;
              border-radius: 3px;
            }
          }

          .son {
            width: 108px;
            text-align: center;

            .box {
              width: 34px;
              height: 34px;
              background: #1A191C;
              border: 1px solid #5470C6;
              border-radius: 50%;
              font-size: 16px;
              text-align: center;
              line-height: 32px;
              margin: auto;
              color: rgba($color: #ffffff, $alpha: 0.8);
              position: relative;

              span {
                width: 14px;
                height: 14px;
                background: #FF6933;
                border-radius: 50%;
                position: absolute;
                top: -2px;
                right: -2px;
                z-index: 1;
                opacity: 0;
              }

              &.on {
                color: #FAA302;
                border: 1px dashed #FAA302;

                span {
                  opacity: 1;
                }
              }
            }
          }
        }
      }

      &.c2 {

        // 128 * 17
        .topProvice {
          width: 2176px;
          margin-left: 128px;

          .item {
            width: 128px;
          }
        }

        .downProvice {
          width: 2304px;

          .item {
            .xname {
              width: 128px;
            }

            .son {
              width: 128px;
            }
          }
        }
      }

      &.c3 {

        // 128 * 99
        .topProvice {
          width: 6400px;
          margin-left: 128px;

          .item {
            width: 128px;
          }
        }

        .downProvice {
          width: 6528px;

          .item {
            .xname {
              width: 128px;
            }

            .son {
              width: 128px;
            }
          }
        }
      }
    }

    .centerLenged {
      margin-top: 30px;
      width: 800px;

      .item {
        margin: 0 40px;
        cursor: pointer;

        .color {
          width: 12px;
          height: 12px;
          background: #999999;
          border-radius: 6px;
          margin-top: 4px;
          margin-right: 10px;
        }

        .text {
          font-size: 16px;
          color: #999999;
        }

        &.on {
          .color {
            background: #5470C6;
          }

          .text {
            color: #ffffff;
          }
        }
      }
    }
  }

  .dialogMap {

    // height: 900px;

    .map {
      width: 860px;
      height: 660px;
      margin: 0 auto;
    }

    .titleCore {
      padding: 20px;
      font-size: 14px;
      line-height: 14px;
      border-bottom: 1px solid rgba($color: #ffffff, $alpha: 0.1);

      .name {}

      .selectCore {
        .item {
          margin-right: 40px;
        }

        .label {
          color: #00E4FF;
        }
      }
    }

    .tableList {
      font-size: 14px;
      line-height: 14px;
      text-align: right;
      padding: 10px 13px;
      background-color: rgba($color: #26262A, $alpha: 0.15);
      margin-top: 20px;
      width: 1330px;

      .item {
        width: 80px;
      }

      .w2 {
        width: 160px;
      }

      .w3 {
        width: 160px;
      }

      .w4 {
        width: 80px;
      }

      .ul {
        color: #00E3FB;
        padding: 15px 10px;

        .w1 {
          width: 140px;
          text-align: left;
        }

      }

      .liList {
        height: 100px;
        overflow: hidden;
        overflow-y: scroll;
        margin-top: 10px;
      }

      .li {
        color: rgba($color: #ffffff, $alpha: 0.8);
        padding: 10px 10px;

        .w1 {
          width: 150px;
          text-align: left;
        }

        .w2 {
          text-align: left;
        }

        &.on {
          // background-color: rgba($color: #2A2A2D, $alpha: 0.3);
        }
      }
    }
  }

  .leftCore {
    width: 414px;

    .downMapCore {
      background: rgba($color: #26262A, $alpha: 0.15);

      .top {
        height: 80px;
        line-height: 40px;
        border-bottom: 1px solid rgba($color: #ffffff, $alpha: 0.05);
        font-size: 14px;
        box-sizing: border-box;
        padding: 0 20px;

        .title {}

        .tabs {
          color: rgba($color: #ffffff, $alpha: 0.8);

          .tabsflex {
            width: 210px;
          }

          .itemttp {
            margin-left: 15px;
            cursor: pointer;
            font-size: 14px;

            &.on {
              color: #00E4FF;
            }
          }
        }

        .label {
          color: #00E4FF;
          cursor: pointer;
        }
      }

      .down {
        .map {
          width: 380px;
          height: 270px;
          margin: 0 auto;
        }

        .roomList {
          width: 100%;
          height: 360px;
          overflow: hidden;
          overflow-y: scroll;
          font-size: 14px;
          padding: 20px 26px;
          box-sizing: border-box;

          .item {
            margin-bottom: 30px;

            .title {
              color: #00E4FF;
            }

            .ul {
              .li {
                margin-top: 15px;
                width: 50%;
              }
            }
          }
        }
      }
    }

    .top {
      .item {
        width: 100%;
        height: 200px;
        background-color: rgba($color: #26262A, $alpha: 0.15);
        box-sizing: border-box;
        padding: 15px;
        margin-bottom: 18px;

        .title {
          font-size: 16px;
          line-height: 16px;
        }

        .topval {
          padding: 0 53px;

          .line {
            background-image: url('~@/assets/images/1920/page5/line.png');
            background-size: cover;
            width: 309px;
            height: 2px;
            margin: 0 auto;
            margin-top: 30px;
          }

          .topvaldown {
            padding-top: 15px;
            font-size: 14px;
            line-height: 20px;

            .text {
              margin-left: 7px;
            }
          }
        }

        .value {
          font-size: 36px;
          line-height: 36px;
          text-align: center;
          margin-top: 50px;

          span {
            font-size: 20px;
          }

          i {
            font-size: 20px;
            vertical-align: text-top;
            font-style: normal;
          }
        }
      }
    }

  }



  .bottomRight {
    font-size: 10px;
    color: #A9A9AA;
    line-height: 20px;
    margin-top: 30px;
    text-align: right;
  }

  .mapDialog {
    position: fixed;
    left: 559px;
    top: 581px;
    z-index: 999;
    background-color: rgba($color: #201F23, $alpha: 0.8);
    width: 360px;
    height: 414px;
    border-radius: 8px;

    .close {
      background-image: url('~@/assets/images/page5/close.png');
      background-size: cover;
      width: 25px;
      height: 25px;
      cursor: pointer;
      margin-right: 5px;
      margin-top: 5px;
    }
  }
}
</style>

<style lang="scss">
.page6 {
  .dialogMap {
    .select {
      width: auto;

      .el-input__icon {
        line-height: 14px;
      }

      .el-input__suffix {
        height: 14px;
      }

      .el-input__inner {
        background: none;
        color: #fff;
        border: none;
        height: 14px;
      }

    }

    .el-select-dropdown {
      background: #1A191C;
    }

    .el-tag.el-tag--info {
      background: rgba($color: #26262a, $alpha: 0.8);
      border-color: rgba($color: #26262a, $alpha: 0.8);
      color: #fff;
    }
  }

  ::-webkit-scrollbar {
    width: 14px;
    height: 14px
  }

  ::-webkit-scrollbar-track,
  ::-webkit-scrollbar-thumb {
    border-radius: 999px;
    border: 5px solid transparent
  }

  ::-webkit-scrollbar-track {
    box-shadow: 1px 1px 5px rgba($color: #00E3FB, $alpha: 0.2) inset
  }

  ::-webkit-scrollbar-thumb {
    min-height: 20px;
    background-clip: content-box;
    box-shadow: 0 0 0 5px rgba($color: #00E3FB, $alpha: 0.2) inset
  }

  ::-webkit-scrollbar-corner {
    background: transparent
  }

  .leftMapCore {
    position: relative;
    width: 860px;
  }

  .roundCore {
    .item {
      position: absolute;
      z-index: 5;
      background-size: cover;
      cursor: pointer;

      &.posi1 {
        width: 366px;
        height: 618px;
        background-image: url('~@/assets/images/1920/page5/map-round-1-1.png');
        top: 50px;
        left: 595px;
      }

      &.posi2 {
        width: 88px;
        height: 103px;
        background-image: url('~@/assets/images/1920/page5/map-round-2.png');
        top: 210px;
        left: 560px;
      }

      &.posi3 {
        width: 118px;
        height: 79px;
        background-image: url('~@/assets/images/1920/page5/map-round-3.png');
        top: 270px;
        left: 380px;
      }

      &.posi4 {
        width: 92px;
        height: 66px;
        background-image: url('~@/assets/images/1920/page5/map-round-4.png');
        top: 510px;
        left: 520px;
      }
    }
  }

  .leftLenged {
    position: absolute;
    z-index: 5;
    bottom: 0;
    left: 30px;

    .name {}

    .item {
      padding-top: 20px;

      .round {
        width: 16px;
        height: 16px;
        background-size: cover;
      }

      .label {
        font-size: 16px;
        line-height: 16px;
        padding-left: 10px;
      }

      &.c1 {
        .round {
          background-image: url('~@/assets/images/1920/page5/r-1.png');
        }

        .label {
          color: #0282EB;
        }
      }

      &.c2 {
        .round {
          background-image: url('~@/assets/images/1920/page5/r-3.png');
        }

        .label {
          color: #1FCF8F;
        }
      }

      &.c3 {
        .round {
          background-image: url('~@/assets/images/1920/page5/r-5.png');
        }

        .label {
          color: #13C8DE;
        }
      }
    }
  }

  .rightMap {
    width: 450px;
    height: 670px;
  }
}

.el-select-dropdown {
  .myCheckBox {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: flex-end !important;
    padding-bottom: 5px;
    padding-right: 10px;

    .el-checkbox__input {
      margin-top: 2px;
    }

    .el-checkbox__label {
      color: #1A191C;
      font-size: 14px;
    }
  }
}
</style>

<style scoped lang="scss">
// 给父盒子清除默认已有样式
/deep/ .custom-tooltip-box {
  padding: 0 !important;
  border: none !important;
  background: none !important;

  // 给子盒子自定义样式
  .custom-tooltip-style {
    margin: 0;
    display: flex;
    flex-direction: column;
    background: none;

    .custom-tooltip-top {
      display: flex;
      color: #00E4FF;
      align-items: center;
      background: none;
      padding-left: 5px;
    }

    .custom-tooltip-middle {
      opacity: 0.5;
      height: 1px;
      background: #a3a199;
      margin: 8px 0 12px 0;
    }

    .custom-tooltip-bbotton {
      color: #000000;
      font-size: 30px;
      font-family: Helvetica;
      font-weight: bold;
    }
  }

  &:last-child {
    // opacity: 0;
  }
}
</style>