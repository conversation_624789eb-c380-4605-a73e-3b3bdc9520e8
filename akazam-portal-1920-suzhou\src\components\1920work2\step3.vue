<template>
  <div class="pageWork3_2 flex-box-between">
    <div class="leftStep">
      <div class="stepList work2 flex-box">
        <div class="item on al" @click="endFun(1)">需求输入</div>
        <div class="arraw on al"></div>
        <div class="item on al" @click="endFun(2)">资源池分配</div>
        <div class="arraw on al"></div>
        <div class="item on flex-box-center" @click="endFun(3)">
          提交作业
        </div>
        <div class="arraw on al"></div>
        <div class="item on flex-box-center">
          <div class="icon"></div>渲染
        </div>
      </div>
      <div>
        <div class="wordList">
          <p :class="wordAni1 ? 'ani' : ''">应用作业：3D应用-离线渲染</p>
          <p :class="wordAni2 ? 'ani' : ''">运行资源池：{{ propsdata.businesscity }}（{{ propsdata.input4 }}）</p>
          <p :class="wordAni3 ? 'ani' : ''" class="flex-box">
            <i v-if="iconAni1" class="el-icon-loading"></i>
            <img v-if="iconAni2" src="~@/assets/images/page4/step1/success.png" alt="">
            {{ word1 }}
          </p>
          <p :class="wordAni4 ? 'ani' : ''" class="flex-box">
            <i v-if="iconAni3" class="el-icon-loading"></i>
            <img v-if="iconAni4" src="~@/assets/images/page4/step1/success.png" alt="">
            {{ word2 }}
          </p>
        </div>
      </div>
    </div>
    <div class="rightBox flex-box-between">
      <div class="center">
        <div class="centerItem" :class="coreAni1 ? 'ani' : ''">
          <div class="titleCore flex-box-between">
            <div class="title">渲染片段预览</div>
            <div class="time" v-if="!propsdata.step4Loading">
              <i class="el-icon-warning"></i><span v-if="!showVideo">渲染完成预计6小时</span><span v-else>下载完成预计1小时</span>
            </div>
          </div>
          <div class="videocore">
            <div class="load" v-if="propsdata.step4Loading">
              <div class="icon"><i class="el-icon-loading"></i></div>
              <div class="text">渲染等待中...</div>
            </div>
            <div class="img" v-show="!showVideo">
              <div class="bg"></div>
              <el-carousel class="imgcore" trigger="click" :interval="2000">
                <el-carousel-item>
                  <img src="~@/assets/images/1920/work2/imgb1.png" alt="">
                </el-carousel-item>
                <el-carousel-item>
                  <img src="~@/assets/images/1920/work2/imgb2.png" alt="">
                </el-carousel-item>
                <el-carousel-item>
                  <img src="~@/assets/images/1920/work2/imgb3.png" alt="">
                </el-carousel-item>
                <el-carousel-item>
                  <img src="~@/assets/images/1920/work2/imgb4.png" alt="">
                </el-carousel-item>
              </el-carousel>
            </div>
            <div class="video" v-if="showVideo">
              <video src="~@/assets/images/1920/work2/video.mp4" autoplay muted loop></video>
            </div>
            <div class="posi" v-show="!showVideo">
              <div class="circleBox" :class="lineBoxAni ? 'ani' : ''">
                <circle-progressbar style="overflow-y:hidden;" barColor="rgba(20, 94, 165, 1)"
                  backgroundColor="rgba(51,51,51,1)" :radius="15" :width="170" :progress="percent"
                  :isAnimation="true"></circle-progressbar>
                <div class="per">{{ 100 - percent }}<span>秒</span></div>
                <div class="text">渲染中...</div>
              </div>
            </div>
          </div>
        </div>
        <div class="centerItem" :class="coreAni2 ? 'ani' : ''">
          <div class="title2">渲染资源消耗</div>
          <div class="fourCore flex-box-between" :class="fourCoreAni1 ? 'ani' : ''">
            <div class="son">
              <div class="t1">
                {{ num1 }}<span>%</span>
              </div>
              <div class="t2">GPU利用率</div>
            </div>
            <div class="son">
              <div class="t1">
                {{ num2 }}<span>%</span>
              </div>
              <div class="t2">CPU利用率</div>
            </div>
            <div class="son">
              <div class="t1">
                {{ num3 }}<span>%</span>
              </div>
              <div class="t2">内存利用率</div>
            </div>
            <div class="son">
              <div class="t1">
                {{ num4 }}<span>%</span>
              </div>
              <div class="t2">磁盘利用率</div>
            </div>
          </div>
        </div>
      </div>
      <div class="rightCore">
        <div class="rightitem left" :class="coreAni3 ? 'ani' : ''">
          <div class="title">算力网络</div>
          <div class="map" ref="chinamap"></div>
          <div class="textposi">{{ speed }}MB/S</div>
        </div>
        <div class="rightitem left" :class="coreAni4 ? 'ani' : ''">
          <div class="title">{{ propsdata.businesscity }}资源池</div>
          <div class="anchorBox">
            <div class="anchor" ref="anchor"></div>
            <div class="anchorcore">
              <div class="text1">已使用</div>
              <div class="text2">{{ propsdata.otherNow.use }}G/{{ propsdata.otherNow.max }}G</div>
            </div>
          </div>
          <div class="downtextCore flex-box-between">
            <div class="son">
              <div class="t1">
                {{ propsdata.otherNow.num1 }}
              </div>
              <div class="t2">正在渲染</div>
            </div>
            <div class="son">
              <div class="t1">
                {{ propsdata.otherNow.num2 }}
              </div>
              <div class="t2">等待渲染</div>
            </div>
            <div class="son">
              <div class="t1">
                {{ propsdata.otherNow.num3 }}
              </div>
              <div class="t2">已完成作业</div>
            </div>
            <div class="son">
              <div class="t1">
                {{ propsdata.otherNow.num4 }}
              </div>
              <div class="t2">失败作业</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getStore, setStore } from "@/common/util";
import VueMatrixRaindrop2 from '../vueMatrixDigitRain/index2.vue';
import * as d3 from 'd3' // d3
import roundCommon from '../round/Index'
import circleProgressbar from 'vue-circleprogressbar';
import Mosaic from 'image-mosaic';
import * as echarts from 'echarts';
import chinaMap from '@/assets/json/geoJson.json'

export default {
  name: 'workstep1',
  components: {
    VueMatrixRaindrop2,
    roundCommon,
    circleProgressbar
  },
  props: {
    propsdata: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      point: [],
      percent: 0,
      percent2: 0,
      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      wordAni4: false,
      wordAni5: false,
      wordAni6: false,
      iconAni1: true,
      iconAni2: false,
      iconAni3: true,
      iconAni4: false,
      showCode: false,
      timer: null,
      timer2: null,
      itemAni1: false,
      itemAni2: false,
      percent2: 0,
      percentdesc: '上传训练数据集...',
      percentdesc2: '作业入池中...',
      percentdesc3: '作业入池中...',
      wordBottomAni: false,
      imgItemAni: false,
      imgItemAni2: false,
      time1: 0,
      time2: 0,
      time1Timer: null,
      time2Timer: null,
      timeDate: '',
      imgLeftAni: false,
      imgRightAni: false,
      ponit1Index: null,
      ponit2Index: null,
      pointAnitimer1: null,
      pointAnitimer2: null,
      wordBottomAni2: null,
      isAuto: null,
      centerOpa: false,
      rightOpa: false,

      fourCoreAni1: false,
      fourCoreAni2: false,
      num1: 8,
      num2: 5,
      num3: 6,
      num4: 9,
      num5: 0,
      num6: 0,
      num7: 0,
      num8: 0,
      word1: '环境准备完成',
      word2: '渲染完成',
      word1_2: '渲染执行中',
      time10: 10,
      tsCoreAni1: false,
      tsCoreAni2: false,
      lineAni1: false,
      lineAni2: false,
      timerpercent: null,
      timerpercent2: null,
      timerpercent3: null,
      showVideo: false,
      imgSrc2: require('../../assets/images/1920/work2/img.png'),
      timeCoreAni: false,
      downtextAni1: false,
      downtextAni2: false,
      showimgAni: false,
      lineBoxAni: false,
      heightAni: false,
      coreAni1: false,
      coreAni2: false,
      coreAni3: false,
      coreAni4: false,
      speed: 10,
      timerspeed: null
    }
  },
  created() {
  },
  mounted() {
    echarts.registerMap('china', { geoJSON: chinaMap })
    this.init()
    this.speedRandom()
  },
  methods: {
    speedRandom() {
      this.timerspeed = setInterval(() => {
        this.speed = parseInt(Math.random() * (12 - 8) + 8)
      }, 1000);
      this.GLOBAL.timerArraySet.push(this.timerspeed)
    },
    initanchor(isData) {
      let otherNow = this.propsdata.otherNow
      let option = {
        tooltip: {
          show: false,
          formatter: '{a} <br/>{b} : {c}%'
        },
        grid: {
          top: 0
        },
        series: [
          {
            name: 'Pressure',
            type: 'gauge',
            splitNumber: 1,
            min: otherNow.min,
            max: otherNow.max,
            radius: 125,
            startAngle: 210,
            endAngle: -30,
            center: ['50%', '65%'],
            pointer: {
              itemStyle: {
                color: '#158EFF',
              }
            },
            title: {
              show: false
            },
            progress: {
              show: true,
              itemStyle: {
                color: '#158EFF',
              }
            },
            detail: {
              show: false,
              valueAnimation: false,
              formatter: '{value}'
            },
            anchor: {
              show: true,
              itemStyle: {
                size: 20
              }
            },
            itemStyle: {
              borderWidth: 20
            },
            data: [
              {
                value: otherNow.use,
                name: '已使用'
              }
            ]
          }
        ]
      };
      // 内存泄漏 无dom 不执行
      if (!this.chartsanchor) {
        this.chartsanchor = echarts.init(this.$refs.anchor, null, { width: 250, height: 200 });
      }
      this.chartsanchor.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsanchor.setOption(option);
    },
    initMap() {
      let points = [
        { value: [121.48054, 31.23593], name: '上海', itemStyle: { color: '#21D571' } },
      ]
      let chooseLngLat = []
      let points2 = [
        { value: [116.41339, 39.91092], name: '北京', itemStyle: { color: '#158EFF' } },
        { value: [119.41942, 32.70068], name: '扬州', itemStyle: { color: '#158EFF' } },
        { value: [121.48054, 31.23593], name: '上海', itemStyle: { color: '#158EFF' } },
        { value: [103.04954, 31.01679], name: '雅安', itemStyle: { color: '#158EFF' } },
        { value: [106.55844, 29.56900], name: '重庆', itemStyle: { color: '#158EFF' } },
        { value: [106.50192, 26.44210], name: '贵安', itemStyle: { color: '#158EFF' } },
        { value: [118.09643, 24.78541], name: '厦门', itemStyle: { color: '#158EFF' } },
        { value: [113.27143, 23.83534], name: '广州', itemStyle: { color: '#158EFF' } },
        { value: [114.18732, 22.24966], name: '香港', itemStyle: { color: '#158EFF' } }
      ]
      let list = points2.filter(item => this.propsdata.businesscity.indexOf(item.name) > - 1)
      points = points.concat(list)
      chooseLngLat = list[0].value
      let option = {
        backgroundColor: '',
        geo: {
          map: 'china',
          aspectScale: 0.72, //长宽比
          zoom: 1.1,
          roam: false,
          label: {
            show: false
          },
          emphasis: {
            disabled: false,
            itemStyle: {
              normal: {
                areaColor: {
                  type: 'radial',
                  x: 0.3,
                  y: 0.3,
                  r: 0.8,
                  colorStops: [{
                    offset: 0,
                    color: '#09132c' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: '#274d68'  // 100% 处的颜色
                  }],
                  globalCoord: true // 缺省为 false
                },
                shadowColor: '#618198',
                shadowOffsetX: 5,
                shadowOffsetY: 5
              },
            },
          },
          itemStyle: {
            normal: {
              areaColor: {
                type: 'radial',
                x: 0.3,
                y: 0.3,
                r: 0.8,
                colorStops: [{
                  offset: 0,
                  color: '#09132c' // 0% 处的颜色
                }, {
                  offset: 1,
                  color: '#274d68'  // 100% 处的颜色
                }],
                globalCoord: true // 缺省为 false
              },
              shadowColor: '#618198',
              shadowOffsetX: 5,
              shadowOffsetY: 5
            },
          },
          regions: [{
            name: '南海诸岛',
            itemStyle: {
              areaColor: 'rgba(0, 10, 52, 1)',
              borderColor: 'rgba(0, 10, 52, 1)',
              normal: {
                opacity: 0,
                label: {
                  show: false,
                  color: "#009cc9",
                }
              }
            },
          }],
        },
        series: [{
          type: 'map',
          roam: false,
          label: {
            normal: {
              show: false,
              textStyle: {
                color: '#888'
              }
            },
            emphasis: {
              show: false,
              disabled: false,
              textStyle: {
                color: 'rgb(183,185,14)'
              }
            }
          },
          selectedMode: false,
          emphasis: {
            disabled: true,
          },

          itemStyle: {
            normal: {
              borderColor: 'rgb(140, 140, 140)',
              borderWidth: 1,
              areaColor: {
                type: 'radial',
                x: 0.5,
                y: 0.5,
                r: 0.8,
                colorStops: [{
                  offset: 0,
                  color: '#09132c' // 0% 处的颜色
                }, {
                  offset: 1,
                  color: '#274d68'  // 100% 处的颜色
                }],
                globalCoord: true // 缺省为 false
              },
            },
            emphasis: {
              show: false,
              disabled: true,
              // areaColor: 'rgb(46,229,206)',
              //    shadowColor: 'rgb(12,25,50)',
              borderWidth: 0.1
            }
          },
          zoom: 1.1,
          //     roam: false,
          map: 'china' //使用
          // data: this.difficultData //热力图数据   不同区域 不同的底色
        }, {
          type: 'effectScatter',
          coordinateSystem: 'geo',
          showEffectOn: 'render',
          zlevel: 1,
          rippleEffect: {
            number: 1,
            period: 1,
            scale: 3,
            brushType: 'fill'
          },
          hoverAnimation: false,
          label: {
            show: true,
            formatter: '{b}',
            position: 'bottom',
            offset: [0, 5],
            color: '#ffffff'
          },
          itemStyle: {
            color: '#1DE9B6',
            shadowBlur: 2,
            shadowColor: '#333'
          },
          symbolSize: 8,
          data: points
        },
        //地图线的动画效果
        {
          type: 'lines',
          zlevel: 2,
          polyline: false,
          effect: {
            show: true,
            period: 2, //箭头指向速度，值越小速度越快
            trailLength: 0.4, //特效尾迹长度[0,1]值越大，尾迹越长重
            symbol: 'arrow', //箭头图标
            symbolSize: 5, //图标大小
          },
          lineStyle: {
            color: '#14A1FF',
            type: 'dashed',
            width: 1, //线条宽度
            opacity: 0.1, //尾迹线条透明度
            curveness: -0.3, //尾迹线条曲直度
          },
          label: {
            show: true,
            position: 'end',
            formatter: '{@val}',
            color: '#1DE9B6',
            fontSize: 16,
          },
          data: [
            { coords: [chooseLngLat, [121.48054, 31.23593]], val: '10MB/s' }
          ]
        }
        ]
      };
      if (!this.mapChart) {
        this.mapChart = echarts.init(this.$refs.chinamap, null, { width: this.GLOBAL.relPx(430), height: this.GLOBAL.relPx(310) });
        this.GLOBAL.echartsDomArray.push(this.mapChart)
      }
      this.mapChart.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.mapChart.setOption(option);
    },
    init() {
      let settimer1 = setTimeout(() => {
        this.wordAni1 = true
      }, 500);
      let settimer2 = setTimeout(() => {
        this.wordAni2 = true
      }, 1000);
      let settimer3 = setTimeout(() => {
        this.wordAni3 = true
        this.coreAni1 = true
      }, 1300);
      let settimer4 = setTimeout(() => {
        this.coreAni2 = true
      }, 1600);
      let settimer5 = setTimeout(() => {
        this.coreAni3 = true
        this.initMap()
      }, 1900);
      let settimer6 = setTimeout(() => {
        this.coreAni4 = true
        this.initanchor()
      }, 2200);
      let settimer7 = setTimeout(() => {
        this.iconAni1 = false
        this.iconAni2 = true
        this.$emit('step4Loading', 'step4Loading')
        this.wordAni4 = true
        this.lineAniFun()
      }, 2500);
      let settimer8 = setTimeout(() => {
        this.numRandom()
      }, 2800);
      let settimer9 = setTimeout(() => {
      }, 3100);
      let settimer10 = setTimeout(() => {

      }, 3400);
      let settimer11 = setTimeout(() => {
      }, 3700);
      let settimer12 = setTimeout(() => {
      }, 4000);

      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
        clearTimeout(settimer8)
        settimer8 = null;
        clearTimeout(settimer9)
        settimer9 = null;
        clearTimeout(settimer10)
        settimer10 = null;
        clearTimeout(settimer11)
        settimer11 = null;
        clearTimeout(settimer12)
        settimer12 = null;
      })
    },
    addTileAuto2(size) {
      let _this = this
      let timer = setInterval(() => {
        _this.addTile2(size)
        size -= 1
        if (size < 0) {
          clearInterval(timer)
          clearInterval(this.timer)
          this.numRandom2()
        }
      }, 100);
      _this.GLOBAL.timerArraySet.push(timer)
    },
    addTile2(size) {
      this.drawImageToCanvas2().then(ctx => {
        const mosaic = new Mosaic(ctx, {
          tileWidth: size <= 0 ? 10 : size,
          tileHeight: size <= 0 ? 10 : size,
        })
        if (size <= 0) {
          mosaic.eraseAllTiles()
          return false
        }
        mosaic.drawAllTiles()
      })
    },
    initMosaic2() {
      this.drawImageToCanvas2().then(ctx => {
        const mosaic = new Mosaic(ctx, {
          tileWidth: 40,
          tileHeight: 40,
        })
        mosaic.drawAllTiles(40)
      })
    },
    drawImageToCanvas2() {
      const canvas = document.querySelector('#canvas2')
      const ctx = canvas.getContext('2d')
      let imageUrl
      if (this.imgSrc2) {
        imageUrl = this.imgSrc2
      }
      return new Promise((resolve) => {
        const image = new Image()
        image.crossOrigin = 'Annoymous'
        image.onload = function () {
          canvas.width = 840
          canvas.height = 366
          // canvas.width = image.width
          // canvas.height = image.height
          // ctx.drawImage(this, 0, 0, image.width, image.height)
          ctx.drawImage(this, 0, 0, 840, 366)
          resolve(ctx)
        }
        image.src = imageUrl
      })
    },
    numRandom() {
      this.timer = setInterval(() => {
        this.num1 = parseInt(Math.random() * (85 - 75) + 75)
        this.num2 = parseInt(Math.random() * (80 - 70) + 70)
        this.num3 = parseInt(Math.random() * (75 - 65) + 65)
        this.num4 = parseInt(Math.random() * (75 - 70) + 70)
      }, 800);
      this.GLOBAL.timerArraySet.push(this.timer)
      this.timer2 = setInterval(() => {
        this.num5 = parseInt(Math.random() * (95 - 85) + 85)
        this.num6 = parseInt(Math.random() * (80 - 70) + 70)
        this.num7 = parseInt(Math.random() * (65 - 55) + 55)
        this.num8 = parseInt(Math.random() * (65 - 55) + 55)
      }, 800);
      this.GLOBAL.timerArraySet.push(this.timer2)
    },
    numRandom2() {
      let timer = setInterval(() => {
        this.num1 = parseInt(Math.random() * (35 - 25) + 25)
        this.num2 = parseInt(Math.random() * (30 - 20) + 20)
        this.num3 = parseInt(Math.random() * (25 - 15) + 15)
        this.num4 = parseInt(Math.random() * (25 - 20) + 20)
      }, 1500);
      this.GLOBAL.timerArraySet.push(timer)
    },
    numRandom3() {
      let timer2 = setInterval(() => {
        this.num5 = parseInt(Math.random() * (45 - 35) + 35)
        this.num6 = parseInt(Math.random() * (30 - 20) + 20)
        this.num7 = parseInt(Math.random() * (15 - 5) + 5)
        this.num8 = parseInt(Math.random() * (15 - 5) + 5)
      }, 1500);
      this.GLOBAL.timerArraySet.push(timer2)
    },
    lineAniFun() {
      this.timerpercent = setInterval(() => {
        this.percent += 1
        if (this.percent >= 100) {
          clearInterval(this.timerpercent)
          this.$emit('step4Finished', 'step4Finished')
          this.iconAni3 = false
          this.iconAni4 = true
          this.showVideo = true
          clearInterval(this.timer)
          this.numRandom2()
        }
      }, 20);
    },
    lineAniFun2() {
      this.timerpercent2 = setInterval(() => {
        this.percent2 += 1
        if (this.percent2 >= 100) {
          clearInterval(this.timerpercent2)
        }
      }, 40);
    },
    lineAniFun3() {
      this.timerpercent3 = setInterval(() => {
        this.time10 -= 0.1
        if (this.time10 <= 0) {
          this.time10 = 0
          clearInterval(this.timerpercent3)
          clearInterval(this.timer2)
          this.numRandom3()
        }
      }, 100);
    },
    endFun(index) {
      // this.$emit('endFun', index)
    },
    resetFun() {
      if (this.pointAnitimer1) {
        clearInterval(this.pointAnitimer1)
      }
      if (this.pointAnitimer2) {
        clearInterval(this.pointAnitimer2)
      }
      if (this.timerpercent) {
        clearInterval(this.timerpercent)
      }
      if (this.timerpercent2) {
        clearInterval(this.timerpercent2)
      }
      if (this.timerpercent3) {
        clearInterval(this.timerpercent3)
      }
    },
  },
  beforeDestroy() {
    this.resetFun()
  },
}
</script>

<style lang="scss">
.pageWork3_2 {

  .fourCore {
    background-image: url('~@/assets/images/1920/work/step4/bg.png');
    background-size: cover;
    width: 577px;
    height: 247px;
    margin-top: 20px;
    // opacity: 0;

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }

    .son {
      width: 50%;
      height: 50%;
      text-align: center;

      .t1 {
        font-size: 34px;
        line-height: 34px;
        padding-top: 30px;

        span {
          font-size: 16px;
        }
      }

      .t2 {
        font-size: 16px;
      }
    }
  }

  .centerCore {
    opacity: 0;

    &.ani {
      animation: imgOpacity 0.5s linear 1 forwards;
    }
  }

  .rightBox {
    padding-right: 10px;
    width: 1210px;

    .center {
      .centerItem {
        width: 578px;
        height: 380px;
        background: rgba($color: #26262A, $alpha: 0.15);
        border-radius: 4px;
        margin-bottom: 30px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 0.5s linear 1 forwards;
        }

        .title2 {
          padding: 13px;
        }

        .titleCore {
          padding: 18px 13px;
          line-height: 16px;
          font-size: 16px;

          .time {
            color: #00E4FF;

            i {
              padding-right: 10px;
            }
          }
        }

        .videocore {
          position: relative;

          .video {
            width: 100%;
            height: 325px;

            video {
              width: 100%;
              height: 100%;
            }
          }

          .load {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 12;
            text-align: center;
            box-sizing: border-box;
            padding-top: 80px;
            background-color: #2A2A2D;

            .icon {
              font-size: 72px;
            }

            .text {
              font-size: 18px;
              margin-top: 20px;
            }
          }

          .img {
            width: 100%;
            height: 325px;
            position: relative;

            .bg {
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              z-index: 10;
              background-color: rgba($color: #000000, $alpha: 0.4);
            }

            .imgcore {
              width: 100%;
              height: 325px;
            }

            img {
              width: 100%;
              height: 100%;
            }
          }

          .posi {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 11;
          }
        }
      }
    }

    .core {
      background: rgba($color: #26262A, $alpha: 0.15);
      width: 580px;
      height: 790px;

      .item {
        border-radius: 4px;
        box-sizing: border-box;
        padding: 24px 20px;
        // opacity: 0;
        margin-bottom: 30px;
        padding-bottom: 1px;
        height: 380px;

        .son {
          margin-bottom: 50px;
          // opacity: 0;

          .ict {
            .icon {
              width: 21px;
              height: 21px;
              margin-right: 10px;

              img {
                width: 100%;
                height: 100%;
              }
            }

            .text {
              font-size: 16px;
              line-height: 21px;

              &.on {
                padding-left: 30px;
              }
            }

            &.on {
              padding-left: 30px;
            }
          }

          .pointList {
            margin-top: 20px;

            .point {
              width: 100%;

              .pointson {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                background-color: rgba($color: #7FB41C, $alpha: 0.4);
                margin: 10px 7px;

                &.on {
                  background-color: rgba($color: #3174F3, $alpha: 1);
                }

                &.s2 {
                  &.on {
                    background-color: rgba($color: #00C2FC, $alpha: 1);
                  }
                }
              }
            }
          }

          .textBox {
            margin-top: 20px;
            font-size: 18px;
            color: #999999;
            line-height: 16px;
          }

          .itemImgList {
            margin-top: 20px;
            padding: 0 60px;

            .imgSon {
              .img {
                width: 68px;
                height: 49px;

                img {
                  width: 100%;
                  height: 100%;
                }
              }

              .dp {
                width: 10px;
                height: 10px;
                background: #666666;
                border-radius: 50%;
                margin: 15px auto 0 auto;

                &.on {
                  background: #21D571;
                }
              }
            }
          }

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }

        &.ani {
          animation: imgOpacity 0.5s linear 1 forwards;
        }
      }
    }
  }

  .coreItem {
    width: 578px;
    // height: 793px;
    background: rgba($color: #26262A, $alpha: 0.15);
    border-radius: 4px;
    opacity: 0;

    .title {
      font-size: 18px;
      padding: 15px;
    }

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }

  }

  .tsCore {
    margin-top: 10px;
    // height: 270px;
    font-size: 16px;
    text-align: center;
    position: relative;
    opacity: 0;

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }

    .top {
      .img {
        position: relative;
        margin: 0 auto;
        width: 97px;
        height: 95px;
        box-sizing: border-box;
        margin-top: 10px;

        .img1 {
          width: 97px;
          height: 95px;
          position: absolute;
          top: 0;
          left: 0;
        }

        .img2 {
          width: 34px;
          height: 29px;
          margin: 0 auto;
          display: block;
        }
      }

      .text {
        margin-top: 10px;
      }
    }

    .line {
      position: absolute;
      z-index: 0;
      width: 140px;
      height: 0;
      background-size: cover;
      top: 95px;

      &.l1 {
        background-image: url('~@/assets/images/1920/work2/L.png');
        left: 140px;
      }

      &.l2 {
        background-image: url('~@/assets/images/1920/work2/R.png');
        right: 140px;
      }

      &.ani {
        animation: heightAni3 0.5s linear 1 forwards;
      }
    }

    .down {
      margin-top: 17px;
      text-align: center;

      .son {
        position: relative;

        .text {
          position: absolute;
          top: 80px;
          left: -10px;
          width: 108px;

          p {
            margin: 0;
          }

          &.on {
            right: 0;
          }
        }
      }

      .cc {
        margin: 35px 5px 20px 5px;

        &.ani {
          animation: imgOpacity2 2s linear infinite forwards;

        }

        .text {
          width: 100%;
        }
      }
    }
  }

  .lineCore {
    margin: 15px 0;
    margin-left: 20px;

    .text {
      font-size: 16px;
      line-height: 16px;
    }

    .line {
      width: 430px;
      height: 10px;
      background: rgba(17, 17, 17, 1);
      border-radius: 5px;
      margin: 0 auto;
      margin-top: 3px;
      margin-left: 10px;

      .inner {
        background-image: url('~@/assets/images/page4/step4/percent.png');
        width: 0%;
        height: 10px;
        border-radius: 5px;
      }

      &.on {
        width: 400px;
      }
    }

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .showimg {
    width: 576px;
    height: 331px;
    margin: 0 auto;
    margin-top: 30px;
    opacity: 0;

    canvas {
      width: 100%;
      height: 100%;
    }

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .downtext {
    font-size: 18px;
    text-align: center;
    margin-top: 14px;
    opacity: 0;

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .timeCore {
    height: 331px;
    margin-top: 30px;
    padding-top: 80px;
    box-sizing: border-box;
    background-color: #2A2A2D;
    opacity: 0;

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }



  .circleBox {
    width: 146px;
    height: 146px;
    position: relative;
    margin: 0 auto;
    margin-top: 80px;
    // opacity: 0;

    &.ani {
      animation: imgOpacity 0.5s linear 1 forwards;
    }

    .center_text {
      display: none;
    }

    .per {
      font-size: 50px;
      text-align: center;
      width: 100%;
      line-height: 146px;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 9;
      color: #fff;

      span {
        font-size: 24px;
      }
    }

    .text {
      font-size: 20px;
      text-align: center;
      margin-top: 20px;
    }
  }

  .rightCore {
    .rightitem {
      width: 576px;
      height: 380px;
      background: rgba($color: #26262A, $alpha: 0.2);
      border-radius: 4px;
      margin-bottom: 30px;
      position: relative;
      opacity: 0;

      &.ani {
        animation: imgOpacity 1s linear 1 forwards;
      }

      .textposi {
        font-size: 16px;
        color: #14A1FF;
        position: absolute;
        bottom: 132px;
        right: 120px;
        z-index: 1;
      }

      .title {
        padding: 13px;
      }

      .map {
        margin: 0 auto;
        width: 430px;
      }

      .anchorBox {
        .anchor {
          width: 250px;
          margin: 0 auto;
        }

        .anchorcore {
          text-align: center;
          transform: translateY(-30px);

          .text1 {
            font-size: 16px;
          }

          .text2 {
            font-size: 24px;
          }
        }
      }

      .downtextCore {
        font-size: 16px;
        text-align: center;
        padding: 0 40px;

        .son {
          .t2 {
            margin-top: 10px;
          }
        }
      }
    }
  }

  @keyframes heightAni3 {

    0% {
      height: 0;
    }

    100% {
      height: 76px;
    }
  }
}

@keyframes imgOpacity {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes imgOpacity2 {

  0% {
    opacity: 1;
  }

  100% {
    opacity: 0.2;
  }
}
</style>

<style lang="scss">
.pageWork3_2 {
  .el-carousel__container {
    height: 325px;
  }
}
</style>