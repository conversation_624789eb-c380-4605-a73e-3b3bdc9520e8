<template>
  <div class="pageWork0_3 flex-box-between">
    <div class="leftStep2">
      <div class="stepList flex-box">
        <div class="item on flex-box-center">
          <div>应用服务加载</div>
        </div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(2)">运行资源池</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(3)">预测结果发布</div>
      </div>
      <div class="wordList">
        <div class="wordListItem">
          <p :class="wordAni3 ? 'ani' : ''">应用作业：气象风险预测服务-慢阻肺，中暑</p>
          <div class="flex-box">
            <p :class="wordAni4 ? 'ani' : ''" class="flex-box">
              <i v-if="iconAni3" class="el-icon-loading"></i>
              <img v-if="iconAni4" src="~@/assets/images/page4/step1/success.png" alt="">
              {{iconAni4 ? '预测数据加载完成' : '预测数据加载中'}}
            </p>
          </div>
        </div>
      </div>

    </div>

    <div class="rightBox flex-box-between">
      <div class="centerCore long" :class="coreAni4 ? 'ani' : ''">
        <div class="title">模型数据加载</div>
        <div class="item flex-box" :class="itemAni1_2 ? 'ani' : ''">
          <div class="iconBox">
            <div class="icon"><img src="~@/assets/images/1920/work3/p1-icon-1.png" alt=""></div>
            <div class="word">慢阻肺</div>
          </div>
          <div class="rightLine">
            <div class="word">预计加载时间50分钟</div>
            <div class="lineCore flex-box">
              <div class="line on">
                <div class="inner" :style="{ 'width': `${percent}%` }"></div>
              </div>
            </div>
            <div class="flex-box-between">
              <div class="word">预测数据加载</div>
              <div class="word">{{ percent }}%</div>
            </div>
          </div>
        </div>
        <div class="item flex-box" :class="itemAni2_2 ? 'ani' : ''">
          <div class="iconBox">
            <div class="icon"><img src="~@/assets/images/1920/work3/p1-icon-2.png" alt=""></div>
            <div class="word">中暑</div>
          </div>
          <div class="rightLine">
            <div class="word">预计加载时间46分钟</div>
            <div class="lineCore flex-box">
              <div class="line on">
                <div class="inner" :style="{ 'width': `${percent2}%` }"></div>
              </div>
            </div>
            <div class="flex-box-between">
              <div class="word">预测数据加载</div>
              <div class="word">{{ percent2 }}%</div>
            </div>
          </div>
        </div>
        <div class="item flex-box" :class="itemAni3_2 ? 'ani' : ''">
          <div class="iconBox">
            <div class="icon"><img src="~@/assets/images/1920/work3/p1-icon-3.png" alt=""></div>
            <div class="word">通算需求</div>
          </div>
          <div class="pointList on">
            <div class="smallItemList pad flex-box-between">
              <div class="smallItem" :class="cwordAni7_2 ? 'on' : ''">CPU{{ propsdata.input2 }}≥核<div class="svg"
                  id="svg7_2"></div>
              </div>
              <div class="smallItem" :class="cwordAni8_2 ? 'on' : ''">内存{{ propsdata.input3 }}≥G<div class="svg"
                  id="svg8_2">
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="item flex-box" :class="itemAni4_2 ? 'ani' : ''">
          <div class="iconBox">
            <div class="icon"><img src="~@/assets/images/1920/work3/p1-icon-4.png" alt=""></div>
            <div class="word">池间网络连接</div>
          </div>
          <div class="pointList on">
            <div class="smallItemList pad flex-box-between">
              <div class="smallItem" :class="cwordAni9_2 ? 'on' : ''">{{ propsdata.input6 }}<div class="svg" id="svg9_2">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="centerCore long" :class="coreAni1 ? 'ani' : ''">
          <div class="title">数据源</div>
          <div class="map" ref="chinamap"></div>
          <div class="maplenged flex-box-center">
            <div class="icon"><img src="~@/assets/images/1920/work3/p1-icon-5.png" alt=""></div>
            <div class="word">云专网</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getStore, setStore } from "@/common/util";
import VueMatrixRaindrop from '../vueMatrixDigitRain/index.vue'
import * as d3 from 'd3' // d3
import * as echarts from 'echarts';
import chinaMap from '@/assets/json/fourProvice.json'

export default {
  name: 'workstep1',
  components: {
    VueMatrixRaindrop,
  },
  props: {
    propsdata: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      coreAni1: false,
      coreAni2: false,
      coreAni3: false,
      coreAni4: false,

      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      wordAni4: false,

      iconAni1: true,
      iconAni2: false,
      iconAni3: true,
      iconAni4: false,

      itemAni1: false,
      itemAni2: false,
      itemAni3: false,
      itemAni4: false,

      cwordAni1: false,
      cwordAni2: false,
      cwordAni3: false,
      cwordAni4: false,
      cwordAni5: false,
      cwordAni6: false,
      cwordAni7: false,
      cwordAni8: false,
      cwordAni9: false,
      cwordAni10: false,

      itemAni1_2: false,
      itemAni2_2: false,
      itemAni3_2: false,
      itemAni4_2: false,

      cwordAni1_2: false,
      cwordAni2_2: false,
      cwordAni3_2: false,
      cwordAni4_2: false,
      cwordAni5_2: false,
      cwordAni6_2: false,
      cwordAni7_2: false,
      cwordAni8_2: false,
      cwordAni9_2: false,
      cwordAni10_2: false,

      mapChart: null,
      percent: 0,
      percent2: 0,
      timerpercent: null,
      timerpercent2: null,
    }
  },
  created() {
  },
  mounted() {
    echarts.registerMap('china', { geoJSON: chinaMap })
    this.openMap()
    // this.init()
    this.init2()
  },
  methods: {
    openMap() {
      let _this = this
      if (!this.mapChart) {
        this.mapChart = echarts.init(this.$refs.chinamap, null, { width: this.GLOBAL.relPx(463), height: this.GLOBAL.relPx(618) });
        this.GLOBAL.echartsDomArray.push(this.mapChart)
      }
      let center = [118.55843, 30.81189]
      let zoom = 2.4
      let fromList = [
        {
          "name": "上海",
          "value": [
            121.472641,
            31.231707
          ],
          "xname": ""
        }
      ]
      let toList = [
        {
          "name": "南京",
          "value": [118.80242, 32.06465],
          "xname": ""
        },
        {
          "name": "苏州",
          "value": [120.59241, 31.30356],
          "xname": ""
        },
        {
          "name": "合肥",
          "value": [117.23344, 31.82658],
          "xname": ""
        },
        {
          "name": "芜湖",
          "value": [118.43943, 31.35854],
          "xname": ""
        },
        {
          "name": "杭州",
          "value": [120.21551, 30.25308],
          "xname": ""
        },
        {
          "name": "台州",
          "value": [
            121.42743, 28.66219
          ],
          "xname": ""
        }
      ]
      let list = [
        {
          fromName: "台州",
          toName: "上海",
          coords: [
            [
              121.42743, 28.66219
            ],
            [
              121.472641, 31.231707
            ]
          ]
        },
        {
          fromName: "南京",
          toName: "上海",
          coords: [
            [118.80242, 32.06465],
            [
              121.472641, 31.231707
            ]
          ]
        },
        {
          fromName: "苏州",
          toName: "上海",
          coords: [
            [120.59241, 31.30356],
            [
              121.472641, 31.231707
            ]
          ]
        },
        {
          fromName: "合肥",
          toName: "上海",
          coords: [
            [117.23344, 31.82658],
            [
              121.472641, 31.231707
            ]
          ]
        },
        {
          fromName: "芜湖",
          toName: "上海",
          coords: [
            [118.43943, 31.35854],
            [
              121.472641, 31.231707
            ]
          ]
        },
        {
          fromName: "杭州",
          toName: "上海",
          coords: [
            [120.21551, 30.25308],
            [
              121.472641, 31.231707
            ]
          ]
        }
      ]
      let option = {
        backgroundColor: '',
        title: {
          left: 'center',
          textStyle: {
            color: '#fff'
          }
        },
        geo: {
          map: 'china',
          aspectScale: 0.72, //长宽比
          center: center,
          zoom: zoom,
          roam: false,
          label: {
            show: false
          },
          emphasis: {
            disabled: true,
            itemStyle: {
              normal: {
                areaColor: {
                  type: 'radial',
                  x: 0.3,
                  y: 0.3,
                  r: 0.8,
                  colorStops: [{
                    offset: 0,
                    color: '#464646' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: '#464646'  // 100% 处的颜色
                  }],
                  globalCoord: true // 缺省为 false
                },
                shadowColor: '#464646',
                shadowOffsetX: 5,
                shadowOffsetY: 5
              },
            },
          },
          itemStyle: {
            normal: {
              areaColor: {
                type: 'radial',
                x: 0.3,
                y: 0.3,
                r: 0.8,
                colorStops: [{
                  offset: 0,
                  color: '#464646' // 0% 处的颜色
                }, {
                  offset: 1,
                  color: '#464646'  // 100% 处的颜色
                }],
                globalCoord: true // 缺省为 false
              },
              shadowColor: '#464646',
              shadowOffsetX: 5,
              shadowOffsetY: 5
            },
          },
          regions: [{
            name: '南海诸岛',
            itemStyle: {
              areaColor: 'rgba(0, 10, 52, 1)',
              borderColor: 'rgba(0, 10, 52, 1)',
              normal: {
                opacity: 0,
                label: {
                  show: false,
                  color: "#009cc9",
                }
              }
            },
          }],
        },
        series: [
          {
            name: '上海',
            type: 'lines',
            zlevel: 10,
            symbol: ['none'],
            symbolSize: 10,
            effect: {
              show: true,
              period: 4, //箭头指向速度，值越小速度越快
              trailLength: 0.1, //特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: 'arrow', //箭头图标
              symbolSize: 8, //图标大小
              opacity: 0.5,
            },
            lineStyle: {
              normal: {
                color: 'rgba(21, 142, 255, 1)',
                width: 1,
                opacity: 0.2,
                type: 'solid',
                curveness: -0.2
              }
            },
            data: list,
            zlevel: 2
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: fromList,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 10
              // return params.data.warning
            },
            rippleEffect: {
              scale: 2, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: 'rgba(33, 213, 113, 1)',
              }
            },
            label: {
              show: true,
              formatter: '{b}',
              fontWeight: 'bold',
              fontSize: 12,
              color: '#fff',
              textBorderColor: '#000',
              position: 'bottom',
              textBorderWidth: 3,
              offset: [0, 10]
            },
            zlevel: 10
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: toList,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 9
              // return params.data.warning
            },
            rippleEffect: {
              scale: 3, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: 'rgba(21, 142, 255, 1)',
              }
            },
            label: {
              show: true,
              formatter: '{b}',
              fontWeight: 'bold',
              fontSize: 12,
              color: '#fff',
              textBorderColor: '#000',
              position: 'bottom',
              textBorderWidth: 3,
              offset: [0, 10]
            },
            zlevel: 3
          },
          {
            type: 'map',
            roam: false,
            center: center,
            zoom: zoom,
            label: {
              show: false,
              textStyle: {
                color: '#ccc'
              },
            },
            // selectedMode: false,
            selectedMode: 'multiple',
            emphasis: {
              disabled: true
            },
            select: {
              disabled: true,
              itemStyle: {
                borderColor: '#787879',
                borderWidth: 1,
                areaColor: '#334053'
              },
              label: {
                color: '#828282'
              },
            },

            itemStyle: {
              borderColor: '#787879',
              borderWidth: 1,
              areaColor: '#333'
            },

            //     roam: false,
            map: 'china', //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
          },
        ]
      };
      this.mapChart.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.mapChart.setOption(option);
    },
    initMap() {
      let points = [
        { value: [116.41339, 39.91092], name: '北京', itemStyle: { color: '#FCC858' } },
        { value: [119.41942, 32.70068], name: '扬州', itemStyle: { color: '#FCC858' } },
        { value: [121.48054, 31.23593], name: '上海', itemStyle: { color: '#FCC858' } },
        { value: [103.04954, 31.01679], name: '雅安', itemStyle: { color: '#FCC858' } },
        { value: [106.55844, 29.56900], name: '重庆', itemStyle: { color: '#FCC858' } },
        { value: [106.50192, 26.44210], name: '贵安', itemStyle: { color: '#FCC858' } },
        { value: [118.09643, 24.78541], name: '厦门', itemStyle: { color: '#FCC858' } },
        { value: [113.27143, 23.83534], name: '广州', itemStyle: { color: '#FCC858' } },
        { value: [114.18732, 22.24966], name: '香港', itemStyle: { color: '#FCC858' } }
      ]
      let option = {
        backgroundColor: '',
        geo: {
          map: 'china',
          aspectScale: 0.72, //长宽比
          zoom: 1.1,
          roam: false,
          label: {
            show: false
          },
          emphasis: {
            disabled: false,
            itemStyle: {
              normal: {
                areaColor: {
                  type: 'radial',
                  x: 0.3,
                  y: 0.3,
                  r: 0.8,
                  colorStops: [{
                    offset: 0,
                    color: '#09132c' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: '#274d68'  // 100% 处的颜色
                  }],
                  globalCoord: true // 缺省为 false
                },
                shadowColor: '#618198',
                shadowOffsetX: 5,
                shadowOffsetY: 5
              },
            },
          },
          itemStyle: {
            normal: {
              areaColor: {
                type: 'radial',
                x: 0.3,
                y: 0.3,
                r: 0.8,
                colorStops: [{
                  offset: 0,
                  color: '#09132c' // 0% 处的颜色
                }, {
                  offset: 1,
                  color: '#274d68'  // 100% 处的颜色
                }],
                globalCoord: true // 缺省为 false
              },
              shadowColor: '#618198',
              shadowOffsetX: 5,
              shadowOffsetY: 5
            },
          },
          regions: [{
            name: '南海诸岛',
            itemStyle: {
              areaColor: 'rgba(0, 10, 52, 1)',
              borderColor: 'rgba(0, 10, 52, 1)',
              normal: {
                opacity: 0,
                label: {
                  show: false,
                  color: "#009cc9",
                }
              }
            },
          }],
        },
        series: [{
          type: 'map',
          roam: false,
          label: {
            normal: {
              show: false,
              textStyle: {
                color: '#888'
              }
            },
            emphasis: {
              show: false,
              disabled: false,
              textStyle: {
                color: 'rgb(183,185,14)'
              }
            }
          },
          selectedMode: false,
          emphasis: {
            disabled: true,
          },

          itemStyle: {
            normal: {
              borderColor: 'rgb(140, 140, 140)',
              borderWidth: 1,
              areaColor: {
                type: 'radial',
                x: 0.5,
                y: 0.5,
                r: 0.8,
                colorStops: [{
                  offset: 0,
                  color: '#09132c' // 0% 处的颜色
                }, {
                  offset: 1,
                  color: '#274d68'  // 100% 处的颜色
                }],
                globalCoord: true // 缺省为 false
              },
            },
            emphasis: {
              show: false,
              disabled: true,
              // areaColor: 'rgb(46,229,206)',
              //    shadowColor: 'rgb(12,25,50)',
              borderWidth: 0.1
            }
          },
          zoom: 1.1,
          //     roam: false,
          map: 'china' //使用
          // data: this.difficultData //热力图数据   不同区域 不同的底色
        }, {
          type: 'effectScatter',
          coordinateSystem: 'geo',
          showEffectOn: 'render',
          zlevel: 1,
          rippleEffect: {
            number: 1,
            period: 1,
            scale: 3,
            brushType: 'fill'
          },
          hoverAnimation: false,
          label: {
            show: true,
            formatter: '{b}',
            position: 'right',
            offset: [5, 2],
            color: '#ffffff'
          },
          itemStyle: {
            normal: {
              color: '#1DE9B6',
              shadowBlur: 2,
              shadowColor: '#333'
            }
          },
          symbolSize: 8,
          data: points
        }, //地图线的动画效果
          // {
          //   type: 'lines',
          //   zlevel: 2,
          //   effect: {
          //     show: true,
          //     period: 2, //箭头指向速度，值越小速度越快
          //     trailLength: 0.4, //特效尾迹长度[0,1]值越大，尾迹越长重
          //     symbol: 'arrow', //箭头图标
          //     symbolSize: 5, //图标大小
          //   },
          //   lineStyle: {
          //     normal: {
          //       color: '#1DE9B6',
          //       width: 1, //线条宽度
          //       opacity: 0.1, //尾迹线条透明度
          //       curveness: .3 //尾迹线条曲直度
          //     }
          //   },
          //   data: [
          //     { coords: [[118.8062, 31.9208], [119.4543, 25.9222]], lineStyle: { color: '#4ab2e5' } }
          //   ]
          // }
        ]
      };
      if (!this.mapChart) {
        this.mapChart = echarts.init(this.$refs.chinamap, null, { width: this.GLOBAL.relPx(430), height: this.GLOBAL.relPx(310) });
        this.GLOBAL.echartsDomArray.push(this.mapChart)
      }
      this.mapChart.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.mapChart.setOption(option);
    },
    // 通用添加小球方法
    addBallNext(index) {
      var height = this.GLOBAL.relPx(32)
      var width = this.GLOBAL.relPx(200)
      var svg = d3.select(`#svg${index}`).append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 30; i++) {
        svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (31) + 1).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 200 - (5 * i)).ease(d3.easeLinear)
      }
    },
    init() {
      let _this = this
      let settimer1 = setTimeout(() => {
        this.wordAni1 = true
      }, 500);
      let settimer2 = setTimeout(() => {
        this.wordAni2 = true
        this.coreAni3 = true
      }, 1000);
      let settimer3 = setTimeout(() => {
        this.itemAni1 = true
      }, 1500);
      let settimer4 = setTimeout(() => {
        this.cwordAni1 = true
        this.addBallNext(1)
      }, 2000);
      let settimer5 = setTimeout(() => {
        this.cwordAni2 = true
        this.addBallNext(2)
      }, 2200);
      let settimer6 = setTimeout(() => {
        this.itemAni2 = true
      }, 2500);
      let settimer7 = setTimeout(() => {
        this.cwordAni3 = true
        this.addBallNext(3)
      }, 2700);
      let settimer8 = setTimeout(() => {
        this.cwordAni4 = true
        this.addBallNext(4)
      }, 2900);
      let settimer9 = setTimeout(() => {
        this.cwordAni5 = true
        this.addBallNext(5)
      }, 3100);
      let settimer10 = setTimeout(() => {
        this.cwordAni6 = true
        this.addBallNext(6)
      }, 3300);
      let settimer11 = setTimeout(() => {
        this.itemAni3 = true
      }, 3800);
      let settimer12 = setTimeout(() => {
        this.cwordAni7 = true
        this.addBallNext(7)
      }, 4000);
      let settimer13 = setTimeout(() => {
        this.cwordAni8 = true
        this.addBallNext(8)
      }, 4200);
      let settimer14 = setTimeout(() => {
        this.itemAni4 = true
      }, 4700);
      let settimer15 = setTimeout(() => {
        this.cwordAni9 = true
        this.addBallNext(9)
      }, 4900);
      let settimer16 = setTimeout(() => {
        this.cwordAni10 = true
        this.addBallNext(10)
        this.iconAni1 = false
        this.iconAni2 = true
      }, 5100);
      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
        clearTimeout(settimer8)
        settimer8 = null;
        clearTimeout(settimer9)
        settimer9 = null;
        clearTimeout(settimer10)
        settimer10 = null;
        clearTimeout(settimer11)
        settimer11 = null;
        clearTimeout(settimer12)
        settimer12 = null;
        clearTimeout(settimer13)
        settimer13 = null;
        clearTimeout(settimer14)
        settimer14 = null;
        clearTimeout(settimer15)
        settimer15 = null;
        clearTimeout(settimer16)
        settimer16 = null;
      })
    },
    init2() {
      let _this = this
      let settimer1 = setTimeout(() => {
        this.wordAni3 = true
      }, 100);
      let settimer2 = setTimeout(() => {
        this.wordAni4 = true
        this.coreAni4 = true
      }, 200);
      let settimer3 = setTimeout(() => {
        this.itemAni1_2 = true
      }, 400);
      let settimer4 = setTimeout(() => {
        this.cwordAni1_2 = true
        this.addBallNext('1_2')
        this.lineAni()
      }, 600);
      let settimer5 = setTimeout(() => {
        this.cwordAni2_2 = true
        this.addBallNext('2_2')
        this.lineAni2()
      }, 800);
      let settimer6 = setTimeout(() => {
        this.itemAni2_2 = true
      }, 1000);
      let settimer7 = setTimeout(() => {
        this.cwordAni3_2 = true
        this.addBallNext('3_2')
      }, 1300);
      let settimer8 = setTimeout(() => {
        this.cwordAni4_2 = true
        this.addBallNext('4_2')
      }, 1600);
      let settimer9 = setTimeout(() => {
        this.cwordAni5_2 = true
        this.addBallNext('5_2')
      }, 1900);
      let settimer10 = setTimeout(() => {
        this.cwordAni6_2 = true
        this.addBallNext('6_2')
      }, 2200);
      let settimer11 = setTimeout(() => {
        this.itemAni3_2 = true
      }, 2500);
      let settimer12 = setTimeout(() => {
        this.cwordAni7_2 = true
        this.addBallNext('7_2')
      }, 2800);
      let settimer13 = setTimeout(() => {
        this.cwordAni8_2 = true
        this.addBallNext('8_2')
      }, 3100);
      let settimer14 = setTimeout(() => {
        this.itemAni4_2 = true
        this.coreAni1 = true
      }, 3400);
      let settimer15 = setTimeout(() => {
        this.cwordAni9_2 = true
        this.addBallNext('9_2')
        this.coreAni2 = true
      }, 3700);
      let settimer16 = setTimeout(() => {
        this.cwordAni10_2 = true
        this.addBallNext('10_2')
        this.iconAni3 = false
        this.iconAni4 = true
      }, 4000);
      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
        clearTimeout(settimer8)
        settimer8 = null;
        clearTimeout(settimer9)
        settimer9 = null;
        clearTimeout(settimer10)
        settimer10 = null;
        clearTimeout(settimer11)
        settimer11 = null;
        clearTimeout(settimer12)
        settimer12 = null;
        clearTimeout(settimer13)
        settimer13 = null;
        clearTimeout(settimer14)
        settimer14 = null;
        clearTimeout(settimer15)
        settimer15 = null;
        clearTimeout(settimer16)
        settimer16 = null;
      })
    },
    lineAni() {
      this.timerpercent = setInterval(() => {
        this.percent += 1
        if (this.percent >= 100) {
          clearInterval(this.timerpercent)
        }
      }, 20);
    },
    lineAni2() {
      this.timerpercent2 = setInterval(() => {
        this.percent2 += 1
        if (this.percent2 >= 100) {
          clearInterval(this.timerpercent2)
        }
      }, 20);
    },
    endFun(index) {
      // this.$emit('endFun', index)
    },
    resetFun() {
      if (this.timerpercent) {
        clearInterval(this.timerpercent)
      }
      if (this.timerpercent2) {
        clearInterval(this.timerpercent2)
      }
      if (this.timer1) {
        clearInterval(this.timer1)
      }
      if (this.timer2) {
        clearInterval(this.timer2)
      }
      if (this.time1Timer) {
        clearInterval(this.time1Timer)
      }
      if (this.time2Timer) {
        clearInterval(this.time2Timer)
      }
      if (this.pointAnitimer1) {
        clearInterval(this.pointAnitimer1)
      }
      if (this.pointAnitimer2) {
        clearInterval(this.pointAnitimer2)
      }
    },
  },
  beforeDestroy() {
    this.resetFun()
  },
}
</script>

<style lang="scss">
.pageWork0_3 {
  .leftStep {
    // margin-left: 20px;
  }

  .rightLine {
    padding-top: 40px;
  }

  .lineCore {
    // opacity: 0;
    margin: 10px 0;

    .text {
      font-size: 16px;
      line-height: 16px;
    }

    .line {
      width: 450px;
      height: 10px;
      background: rgba(17, 17, 17, 1);
      border-radius: 5px;
      margin: 0 auto;

      .inner {
        background-image: url('~@/assets/images/page4/step4/percent.png');
        width: 0%;
        height: 10px;
        border-radius: 5px;
      }



      &.on {
        width: 400px;
      }
    }

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .rightBox {
    width: 1200px;
    box-sizing: border-box;
    margin-right: 20px;
  }

  .centerCore {
    background-color: rgba($color: #26262A, $alpha: 0.15);
    width: 578px;
    height: 380px;
    margin-bottom: 36px;
    opacity: 0;

    .map {
      margin: 0 auto;
      width: 480px;
    }

    .title {
      font-size: 18px;
      padding: 13px;
      margin-bottom: 20px;
    }

    .item {
      margin-bottom: 60px;
      opacity: 0;

      .iconBox {
        width: 130px;
        // height: 138px;
        box-sizing: border-box;
        // padding: 0 42px;
        padding-top: 23px;

        .icon {
          width: 61px;
          height: 61px;
          margin: 0 auto;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .word {
          font-size: 18px;
          color: #ffffff;
          text-align: center;
          padding-top: 20px;
        }

        &.on {
          padding-top: 43px;
        }
      }

      .arrow {
        width: 41px;
        height: 14px;
        margin-top: 60px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .pointList {
        width: 430px;
        // height: 88px;
        border-radius: 4px;
        box-sizing: border-box;
        // padding: 10px 20px;
        position: relative;

        .codeBox {
          // opacity: 1;
          // position: absolute;
          z-index: 1;
          top: 10px;
          left: 20px;
        }

        .point {
          width: 100%;
          // height: 72px;

          .son {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: rgba($color: #7FB41C, $alpha: 0.4);
            margin: 10px 7px;

            &.on {
              background-color: rgba($color: #3174F3, $alpha: 1);
            }

            &.s2 {
              &.on {
                background-color: rgba($color: #00C2FC, $alpha: 1);
              }
            }
          }
        }

        .percentList {
          margin-top: 5px;
          // padding: 0 7px;

          .word {
            p {
              font-size: 16px;
              color: #ffffff;
              padding: 0;
              margin: 10px 0;
            }
          }

          .line {
            background-color: #1A191C;
            border-radius: 3px;

            .inner {
              background-image: url('~@/assets/images/page4/step1/percent.png');
              border-radius: 3px;
              // background-size: cover;
              width: 80%;
              height: 6px;
            }
          }
        }

        &.on {
          padding: 0;
        }
      }

      &.ani {
        animation: imgOpacity 1s linear 1 forwards;
      }

      &.on {
        padding-top: 20px;
      }
    }

    .wordCore {
      padding-top: 65px;
      text-align: center;
      text-align: center;

      .t1 {
        font-size: 80px;
        line-height: 80px;
        color: #ffffff;
        width: 250px;
        padding-top: 30px;

        p {
          font-size: 20px;
          margin: 0;
        }
      }

      .line {
        width: 30px;
        height: 3px;
        background: #1B81DF;
        margin: 0 auto;
        margin-top: 20px;
      }

      .t2 {
        font-size: 18px;
        margin-top: 10px;
      }

      .linec {
        background-image: url('~@/assets/images/1920/work2/line.png');
        background-size: cover;
        width: 1px;
        height: 188px;
      }

      &.ani {
        animation: imgOpacity 1s linear 1 forwards;
      }
    }

    &.left {
      height: 380px;

      .title {
        margin-bottom: 5px;
      }
    }

    &.long {
      height: 793px;
    }

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .svg {
    width: 100%;
    height: 100%;
  }

  .smallItemList {
    box-sizing: border-box;
    padding: 0;
    // position: absolute;
    top: 0;
    left: 0;
    z-index: 1;

    .smallItem {
      width: 202px;
      height: 34px;
      background: rgba($color: #26262A, $alpha: 0.5);
      border: 1px solid #26A6E0;
      border-radius: 4px;
      font-size: 16px;
      text-align: center;
      line-height: 32px;
      opacity: 0;
      margin-top: 20px;
      margin-bottom: 10px;
      position: relative;

      .svg {
        position: absolute;
        top: 1px;
        left: 1px;
        z-index: -1;
        width: 200px;
        height: 32px;
      }

      &.trans {
        transform: translateY(30px);
      }

      &.on {
        animation: imgOpacity 1s linear 1 forwards;
      }
    }

    &.pad {
      padding-top: 35px;
    }
  }
}

@keyframes imgOpacity {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.maplenged{
  .icon{
    margin-right: 10px;
  }
}
</style>
