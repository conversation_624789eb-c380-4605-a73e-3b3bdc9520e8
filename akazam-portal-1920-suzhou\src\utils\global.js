import { tree } from "d3"

let timerArraySet = [] // 无限延时 setInterval
let timerArrayOut = [] // 单次延时 setTimeout
let echartsDomArray = [] // echarts dom
let isAuto = true  // 作业自动演示

function relPx(value) {
    const scale = document.documentElement.clientWidth / 1920
    // 用这个比例计算出适配当前容器大小的尺寸
    return value * scale
}

export default {
    timerArraySet,
    timerArrayOut,
    echartsDomArray,
    relPx,
    isAuto
}