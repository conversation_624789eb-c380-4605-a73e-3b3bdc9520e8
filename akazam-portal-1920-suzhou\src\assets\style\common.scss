body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Microsoft YaHei, RTWSY, Helvetica Neue, Helvetica, PingFang SC,
    Hiragino Sans GB, Arial, sans-serif;
  background-color: #111;
}

.pageCommon {
  // background-color: #1A191C;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  padding: 0 30px;
  &.hide {
    position: fixed !important;
    top: -9999px;
    left: -9999px;
  }
}

/*弹性盒子*/
.flex-box {
  display: flex;
  flex-wrap: wrap;
}

/*弹性盒子：两边对齐*/
.flex-box-between {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

/*弹性盒子：居中对齐*/
.flex-box-center {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

/*弹性盒子：右对齐对齐*/
.flex-box-end {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
}

::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

::-webkit-scrollbar-track,
::-webkit-scrollbar-thumb {
  border-radius: 999px;
  border: 5px solid transparent;
}

::-webkit-scrollbar-track {
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2) inset;
}

::-webkit-scrollbar-thumb {
  min-height: 20px;
  background-clip: content-box;
  box-shadow: 0 0 0 5px rgba(0, 0, 0, 0.2) inset;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

.footer {
  height: 140px;
  box-sizing: border-box;
  overflow: hidden;
}

#canvasId {
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
}

.leftStep {
  // margin-top: 70px;
  margin-left: 0;

  .stepList {
    position: absolute;
    top: -30px;
    right: 0;

    .item {
      background-image: url("~@/assets/images/1920/work/step1/item.png");
      background-size: contain;
      width: 160px;
      height: 40px;
      line-height: 40px;
      font-size: 20px;
      color: rgba(102, 102, 102, 1);
      text-align: center;
      cursor: pointer;

      .icon {
        background-image: url("~@/assets/images/1920/work/step1/icon.png");
        background-size: cover;
        width: 16px;
        height: 16px;
        margin-top: 15px;
        margin-right: 10px;
      }

      &.on {
        background-image: url("~@/assets/images/1920/work/step1/item_on.png");
        color: #00e3fb;
      }
    }

    .arraw {
      background-image: url("~@/assets/images/1920/work/step1/arrow.png");
      background-size: cover;
      width: 23px;
      height: 29px;
      margin: 0 28px;

      &.al {
        background-image: url("~@/assets/images/1920/work/step1/arrow_on.png");
      }
    }
    &.work2 {
      right: 300px;
    }
  }

  .wordList {
    margin-left: 15px;

    .wordListItem {
      padding-bottom: 10px;
    }

    p {
      margin: 0;
      font-size: 18px;
      color: #ffffff;
      margin-bottom: 20px;
      opacity: 0;

      img {
        width: 18px;
        height: 18px;
        display: block;
        margin-top: 3px;
        margin-right: 10px;
        margin-left: 20px;
      }

      i {
        display: block;
        font-size: 18px;
        margin-right: 10px;
        margin-left: 20px;
        margin-top: 2px;
      }

      &.ani {
        animation: imgOpacity 1s linear 1 forwards;
      }
    }

    .upload {
      width: 213px;
      height: 131px;
      opacity: 0;

      &.ani {
        animation: imgOpacity 1s linear 1 forwards;
      }

      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.leftStep2 {
  // margin-top: 70px;
  margin-left: 0;

  .stepList {
    position: absolute;
    top: -30px;
    right: 0;

    .item {
      background-image: url("~@/assets/images/1920/work3/nav2.png");
      background-size: contain;
      width: 310px;
      height: 40px;
      line-height: 40px;
      font-size: 20px;
      color: rgba(102, 102, 102, 1);
      text-align: center;
      cursor: pointer;

      .icon {
        background-image: url("~@/assets/images/1920/work/step1/icon.png");
        background-size: cover;
        width: 16px;
        height: 16px;
        margin-top: 15px;
        margin-right: 10px;
      }

      &.on {
        background-image: url("~@/assets/images/1920/work3/nav.png");
        color: #00e3fb;
      }
    }

    .arraw {
      background-image: url("~@/assets/images/1920/work/step1/arrow.png");
      background-size: cover;
      width: 23px;
      height: 29px;
      margin: 0 60px;

      &.al {
        background-image: url("~@/assets/images/1920/work/step1/arrow_on.png");
      }
    }
    &.work2 {
      right: 300px;
    }
  }

  .wordList {
    margin-left: 15px;

    .wordListItem {
      padding-bottom: 10px;
    }

    p {
      margin: 0;
      font-size: 18px;
      color: #ffffff;
      margin-bottom: 20px;
      opacity: 0;

      img {
        width: 18px;
        height: 18px;
        display: block;
        margin-top: 3px;
        margin-right: 10px;
        margin-left: 20px;
      }

      i {
        display: block;
        font-size: 18px;
        margin-right: 10px;
        margin-left: 20px;
        margin-top: 2px;
      }

      &.ani {
        animation: imgOpacity 1s linear 1 forwards;
      }
    }

    .upload {
      width: 213px;
      height: 131px;
      opacity: 0;

      &.ani {
        animation: imgOpacity 1s linear 1 forwards;
      }

      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.mainCenterAuto {
  margin: 0 auto;
}
