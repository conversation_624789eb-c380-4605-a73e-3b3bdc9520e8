<template>
  <div id="app">
    <keep-alive>
      <router-view v-if="$route.meta.keepAlive"></router-view>
      <!--这里是会被缓存的组件-->
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive"></router-view>
    <!--这里是不会被缓存的组件-->
    <!-- <keep-alive include="page0">
      <router-view />
    </keep-alive> -->
    <!-- <router-view /> -->
  </div>
</template>

<script>
// import './assets/js/gameCanvas-3.0'
import { getStore, setStore } from "@/common/util";

export default {
  data() {
    return {

    };
  },
  methods: {

  },
  created() {
    let list = JSON.parse(JSON.stringify(this.$router.options.routes))
    let arr = list.filter(item => item.path != '*')
    setStore('navList', arr)
  }
};
</script>

<style lang="scss">
#app {
  // background-color: #1A191C;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #ffffff;
  max-width: 1920px;
  margin: 0 auto;
  overflow: hidden;
}
</style>
