<template>
  <div>
    <div class="pageCommon page0" :class="showPage8 ? 'hide' : ''">
      <!-- 头部-s -->
      <header-common
        :icon="0"
        :name="'全球算力规模'"
        @indexClick="indexClickFun"
      ></header-common>
      <!-- 背景-s -->
      <!-- <bg-common></bg-common> -->
      <div
        class="earthCore flex-box"
        :class="!showLeftCore ? 'leftWordAni2-bak' : ''"
      >
        <div
          class="rightEarth"
          :class="[
            !showLeftCore ? 'showLeftCore' : '',
            !showGif ? 'showGif' : '',
          ]"
        >
          <div class="opacityBox">
            <div class="ddearthFather">
              <div
                class="ddearth"
                ref="earth"
                :class="startOpacity ? 'opacity' : ''"
              ></div>
            </div>
            <div class="opaitem"></div>
            <div
              class="posiBox"
              v-for="(item, index) in posiList"
              :key="'zs-' + index"
              :class="[`posi6`, posiIndex == index ? 'posiAni' : '']"
            >
              <div class="top flex-box">
                <div class="item flex-box">
                  <div class="icon">
                    <img src="~@/assets/images/page0/five-1.png" alt="" />
                  </div>
                  <div class="text">算力指数排名： {{ item.n1 }}</div>
                </div>
                <div class="item flex-box">
                  <div class="icon">
                    <img src="~@/assets/images/page0/five-2.png" alt="" />
                  </div>
                  <div class="text">算力指数： {{ item.n2 }}</div>
                </div>
                <!-- <div class="w1">算力总规模： {{ item.n1 }} EFlops</div>
              <div class="w2">总占比：{{ item.n2 }}%</div> -->
              </div>
              <div class="down flex-box-center">
                <div class="item">
                  <div class="label">计算能力指数</div>
                  <div class="value">{{ item.n3 }}</div>
                </div>
                <div class="line"></div>
                <div class="item">
                  <div class="label">计算效率指数</div>
                  <div class="value">{{ item.n4 }}</div>
                </div>
                <div class="line"></div>
                <div class="item">
                  <div class="label">应用水平指数</div>
                  <div class="value">{{ item.n5 }}</div>
                </div>
                <div class="line"></div>
                <div class="item">
                  <div class="label">基础设施支持指数</div>
                  <div class="value">{{ item.n6 }}</div>
                </div>
              </div>
            </div>
            <!-- <div class="mapChangeButton" v-if="!showLeftCore" @click="open2d">
              <img src="~@/assets/images/1920/mapicon.png" alt="">
            </div> -->
            <!-- <div class="video">
            <video autoplay mouted controls src="~@/assets/video/earth.mp4"></video>
          </div> -->
            <div class="gif" v-if="showGif">
              <img src="~@/assets/video/PNG1.gif" alt="" />
            </div>
            <div class="timeLine" :class="startOpacity ? 'opacity' : ''">
              <div class="now"></div>
              <div class="timelineBox">
                <div
                  class="timelineBoxInner"
                  ref="timelineBoxInner"
                  :class="lineAni ? 'ani' : ''"
                >
                  <div class="line flex-box">
                    <div
                      class="item"
                      v-for="(item2, index2) in itemList"
                      :key="'list4-' + index2"
                    ></div>
                    <div
                      class="flex-box"
                      v-for="(item, index) in yearList"
                      :key="'list1-' + index"
                    >
                      <div
                        class="item"
                        v-for="(item2, index2) in itemList"
                        :key="'list5-' + index2"
                      ></div>
                      <div class="item on"></div>
                      <div
                        class="item"
                        v-for="(item2, index2) in itemList"
                        :key="'list2-' + index2"
                      ></div>
                    </div>
                    <div
                      class="item"
                      v-for="(item2, index2) in itemList"
                      :key="'list3-' + index2"
                    ></div>
                  </div>
                  <div class="year flex-box">
                    <div
                      class="item"
                      v-for="(item, index) in yearList"
                      :key="'list6-' + index"
                    >
                      {{ item }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="right flex-box-end" v-if="!showGif">
              <p class="bottomRight" :class="startOpacity ? 'opacity' : ''">
                数据来源: 中国算力发展指数白皮书(2023年)、Data Center Map
                、2021-2022/2022-2023全球计算力指数评估报告
              </p>
            </div>
          </div>
        </div>
        <div
          class="leftWord"
          v-if="showLeftCore"
          :class="[
            leftWordAni ? 'ani' : '',
            !showLeftCore ? 'leftWordAni1' : '',
          ]"
        >
          <div class="flex-box">
            <div class="item">
              <div class="big">{{ eflops }}</div>
              <div class="small">算力指数</div>
            </div>
            <div class="item">
              <div class="big">{{ eflopsPercent }}<span></span></div>
              <div class="small">算力指数排名</div>
            </div>
          </div>
          <div class="name">{{ eflopsName }}</div>
        </div>
        <!-- 2d地图 -->
        <div class="rightMapPosi" v-show="show2dMap">
          <div class="close" @click="show2dMap = false"></div>
          <div
            class="ddmap"
            ref="map"
            :class="startOpacity ? 'opacity' : ''"
          ></div>
          <div
            class="usachina"
            :class="[`posi${index}`]"
            v-for="(item, index) in usachinaList"
            :key="'sl-' + index"
          >
            <div class="top flex-box-center">
              <div class="item">
                <div class="label">算力排名</div>
                <div class="value flex-box-center">
                  <div class="rank">{{ item.t1 }}</div>
                  <div class="icon" v-if="index === 0">
                    <img src="~@/assets/images/page0/usa.png" alt="" />
                  </div>
                  <div class="icon" v-if="index === 1">
                    <img src="~@/assets/images/page0/china.png" alt="" />
                  </div>
                </div>
              </div>
              <div class="item">
                <div class="label">算力指数</div>
                <div class="value">{{ item.t2 }}</div>
              </div>
              <div class="item">
                <div class="label">计算能力指数</div>
                <div class="value">{{ item.t3 }}</div>
              </div>
              <div class="item">
                <div class="label">计算效率指数</div>
                <div class="value">{{ item.t4 }}</div>
              </div>
              <div class="item">
                <div class="label">应用水平指数</div>
                <div class="value">{{ item.t5 }}</div>
              </div>
              <div class="item">
                <div class="label">基础设施支持指数</div>
                <div class="value">{{ item.t6 }}</div>
              </div>
            </div>
            <div class="down flex-box">
              <div class="item flex-box-between">
                <div class="le flex-box">
                  <div class="icon">
                    <img src="~@/assets/images/page0/icon1.png" alt="" />
                  </div>
                  <div class="word">全球算力份额占比：</div>
                </div>
                <div class="rt">{{ item.d1 }}%</div>
              </div>
              <div class="item flex-box-between">
                <div class="le flex-box">
                  <div class="icon">
                    <img src="~@/assets/images/page0/icon2.png" alt="" />
                  </div>
                  <div class="word">全球基础算力份额占比：</div>
                </div>
                <div class="rt">{{ item.d2 }}%</div>
              </div>
              <div class="item flex-box-between">
                <div class="le flex-box">
                  <div class="icon">
                    <img src="~@/assets/images/page0/icon3.png" alt="" />
                  </div>
                  <div class="word">全球智能算力份额占比：</div>
                </div>
                <div class="rt">{{ item.d3 }}%</div>
              </div>
              <div class="item flex-box-between">
                <div class="le flex-box">
                  <div class="icon">
                    <img src="~@/assets/images/page0/icon4.png" alt="" />
                  </div>
                  <div class="word">全球超算算力份额占比：</div>
                </div>
                <div class="rt">{{ item.d4 }}%</div>
              </div>
            </div>
          </div>
          <div
            class="posiBox"
            v-for="(item, index) in posiList"
            :key="'zs-' + index"
            :class="[`posi${index + 1}`, posiIndex == index ? 'posiAni' : '']"
          >
            <div class="top flex-box">
              <div class="item flex-box">
                <div class="icon">
                  <img src="~@/assets/images/page0/five-1.png" alt="" />
                </div>
                <div class="text">算力指数排名： {{ item.n1 }}</div>
              </div>
              <div class="item flex-box">
                <div class="icon">
                  <img src="~@/assets/images/page0/five-2.png" alt="" />
                </div>
                <div class="text">算力指数： {{ item.n2 }}</div>
              </div>
              <!-- <div class="w1">算力总规模： {{ item.n1 }} EFlops</div>
              <div class="w2">总占比：{{ item.n2 }}%</div> -->
            </div>
            <div class="down flex-box-center">
              <div class="item">
                <div class="label">计算能力指数</div>
                <div class="value">{{ item.n3 }}</div>
              </div>
              <div class="line"></div>
              <div class="item">
                <div class="label">计算效率指数</div>
                <div class="value">{{ item.n4 }}</div>
              </div>
              <div class="line"></div>
              <div class="item">
                <div class="label">应用水平指数</div>
                <div class="value">{{ item.n5 }}</div>
              </div>
              <div class="line"></div>
              <div class="item">
                <div class="label">基础设施支持指数</div>
                <div class="value">{{ item.n6 }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="rightChart" :class="!showLeftCore ? 'leftWordAni3' : ''">
          <div class="ecCore ecCore1">
            <div class="title">2022年全球算力规模</div>
            <div class="array flex-box-between">
              <div class="wordCore">
                <div class="value">906</div>
                <div class="unit">EFlpos</div>
                <div class="iconBox flex-box-center">
                  <div class="text">增速47%</div>
                  <div class="icon">
                    <img src="~@/assets/images/page0/up.png" alt="" />
                  </div>
                </div>
                <div class="line"></div>
                <div class="label">总算力规模</div>
              </div>
              <div class="wordCore">
                <div class="radar" :class="radarIndex == 0 ? 'on' : ''">
                  <div class="num">440</div>
                  <div class="unit">EFlpos</div>
                </div>
                <div class="line"></div>
                <div class="label">通用算力</div>
              </div>
              <div class="wordCore">
                <div class="radar" :class="radarIndex == 1 ? 'on' : ''">
                  <div class="num">450</div>
                  <div class="unit">EFlpos</div>
                </div>
                <div class="line"></div>
                <div class="label">智算算力</div>
              </div>
              <div class="wordCore">
                <div class="radar" :class="radarIndex == 2 ? 'on' : ''">
                  <div class="num">16</div>
                  <div class="unit">EFlpos</div>
                </div>
                <div class="line"></div>
                <div class="label">超算算力</div>
              </div>
            </div>
            <div class="right flex-box-end">
              <p class="bottomRight">
                数据来源: 中国算力发展指数白皮书(2023年)
              </p>
            </div>
          </div>
          <div class="ecCore ecCore2">
            <div class="title">2022年全球算力规模分布</div>
            <div class="flex-box-between binRightCoreBox">
              <div>
                <div class="binRightCore flex-box">
                  <div class="ecBinBox">
                    <div ref="bin" class="ecBin"></div>
                    <div class="bg"></div>
                  </div>
                </div>
                <div class="rightBottomTitle" @click="open2d">
                  算力规模分布情况
                </div>
              </div>
              <div>
                <div ref="bar" class="ecBar"></div>
              </div>
            </div>
            <div class="flex-box-end">
              <div class="bottomRight">
                数据来源: 中国综合算力评价白皮书(2023 年)
              </div>
            </div>
          </div>
          <div class="ecCore ecCore3">
            <div class="title">全球数据中心能耗趋势</div>
            <div>
              <div ref="line" class="ecLine"></div>
              <div class="flex-box-end">
                <div class="bottomRight">
                  数据来源: UPTIME INSTITUTE GLOBAL SURVEY OF IT AND DATA CENTER
                  MANAGERS 2007-2021 (n=566)
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="pageCommon page8" :class="!showPage8 ? 'hide' : ''">
      <!-- 头部-s -->
      <header-common
        :icon="0"
        :name="'全国算力'"
        @indexClick="indexClickFun2"
      ></header-common>
      <!-- 头部-e -->
      <div class="main flex-box-between">
        <div class="ddmapBox">
          <!-- <div class="mapChangeButton" @click="changePage(0)">
            <img src="~@/assets/images/1920/page0/change.png" alt="">
          </div> -->
          <div
            class="map"
            :class="nameShow || (rowIndex && sonIndex) || showYs ? '' : 'hide'"
            v-show="!nameShow"
          >
            <div class="scaleBox">
              <div class="ddmap" ref="map_8"></div>
              <div class="bottomTabs flex-box-center" v-show="!showYs">
                <div
                  class="item"
                  @click="leftBottomTabIndex_8Change(1)"
                  :class="leftBottomTabIndex_8 === 1 ? 'on' : ''"
                >
                  在用算力规模分布
                </div>
                <div
                  class="item"
                  @click="leftBottomTabIndex_8Change(2)"
                  :class="leftBottomTabIndex_8 === 2 ? 'on' : ''"
                >
                  算力排名前10强市
                </div>
                <div
                  class="item"
                  @click="leftBottomTabIndex_8Change(3)"
                  :class="leftBottomTabIndex_8 === 3 ? 'on' : ''"
                >
                  在建算力规模分布
                </div>
              </div>
              <div class="leftLenged" v-show="!showYs">
                <div class="item flex-box" v-show="leftBottomTabIndex_8 === 1">
                  <div class="color c1"></div>
                  <div class="text">在用13EFLOPS+</div>
                </div>
                <div class="item flex-box" v-show="leftBottomTabIndex_8 === 1">
                  <div class="color c2"></div>
                  <div class="text">在用5EFLOPS+</div>
                </div>
                <div class="item flex-box" v-show="leftBottomTabIndex_8 === 3">
                  <div class="color c3"></div>
                  <div class="text">在建18EFLOPS+</div>
                </div>
                <div class="item flex-box" v-show="leftBottomTabIndex_8 === 3">
                  <div class="color c4"></div>
                  <div class="text">在建3-8EFLOPS</div>
                </div>
              </div>
              <div
                class="top10"
                :class="rowIndex && (sonIndex || sonIndex === 0) ? 'op3' : ''"
                v-show="leftBottomTabIndex_8 === 2 && !showYs"
              >
                <div class="item top1">
                  <div class="text">北京</div>
                </div>
                <div class="item top2">
                  <div class="text">上海</div>
                </div>
                <div class="item top3">
                  <div class="text">广州</div>
                </div>
                <div class="item top4"></div>
                <div class="item top5"></div>
                <div class="item top6"></div>
                <div class="item top7"></div>
                <div class="item top8"></div>
                <div class="item top9"></div>
                <div class="item top10"></div>
              </div>
              <div class="showYs" :class="showYs ? 'op3' : ''">
                <div
                  class="posi posi1 flex-box"
                  @click="groupFunRight(1, 0)"
                  :class="
                    (rowIndex === 1 && sonIndex === 0) ||
                    (!rowIndex && !sonIndex)
                      ? ''
                      : 'op3'
                  "
                >
                  <div class="greenBox"></div>
                  <div class="name s3">京津冀枢纽</div>
                </div>
                <div
                  class="dialogPosi posi1"
                  v-show="rowIndex === 1 && sonIndex === 0"
                >
                  <div class="greenPosi"></div>
                  <div class="infoPosi">
                    <div class="infomap flex-box-center">
                      <div class="infomapitem">
                        <div class="title">张家口集群起步区</div>
                        <div class="bg bg6">
                          <div class="bgPoint p1">
                            <div class="point"></div>
                            <div class="text">怀来县</div>
                          </div>
                          <div class="bgPoint p2">
                            <div class="point"></div>
                            <div class="text">张北县</div>
                          </div>
                          <div class="bgPoint p3">
                            <div class="point"></div>
                            <div class="text">宣化区</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="pueCore">
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon"></div>
                          在用PUE：
                        </div>
                        <div class="w5">北京市-1.55</div>
                        <div class="w5">天津市-1.53</div>
                        <div class="w5">河北省-1.44</div>
                      </div>
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon2"></div>
                          在建PUE：
                        </div>
                        <div class="w5">北京市-1.25</div>
                        <div class="w5">天津市-1.3</div>
                        <div class="w5">河北省-1.31</div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="puedownCore">
                      <div class="row flex-box-between">
                        <div class="w3 flex-box">
                          <div class="w3icon1"></div>
                          <div>
                            <div>已建5G基站数量(个)</div>
                            <div>10000+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon2"></div>
                          <div>
                            <div>运营数据中心（个）</div>
                            <div>13+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon3"></div>
                          <div>
                            <div>机架规模(万架)</div>
                            <div>10+</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="posi flex-box posi2"
                  @click="groupFunRight(1, 1)"
                  :class="
                    (rowIndex === 1 && sonIndex === 1) ||
                    (!rowIndex && !sonIndex)
                      ? ''
                      : 'op3'
                  "
                >
                  <div class="name s2">成渝枢纽</div>
                  <div class="greenBox greenBox2"></div>
                </div>
                <div
                  class="dialogPosi posi2"
                  v-show="rowIndex === 1 && sonIndex === 1"
                >
                  <div class="greenPosi"></div>
                  <!-- 天府集群：成都市双流区、郫都区、简阳市  重庆集群：为重庆市两江新区水土新城、西部（重庆）科学城璧山片区、重庆经济技术开发区 -->
                  <div class="infoPosi">
                    <div class="infomap flex-box-center">
                      <div class="line"></div>
                      <div class="infomapitem">
                        <div class="title">天府集群起步区</div>
                        <div class="bg bg3">
                          <div class="bgPoint p1">
                            <div class="point"></div>
                            <div class="text">双流区</div>
                          </div>
                          <div class="bgPoint p2">
                            <div class="point"></div>
                            <div class="text">郫都区</div>
                          </div>
                          <div class="bgPoint p3">
                            <div class="point"></div>
                            <div class="text">简阳市</div>
                          </div>
                        </div>
                      </div>
                      <div class="infomapitem">
                        <div class="title">重庆集群起步区</div>
                        <div class="bg bg4">
                          <div class="bgPoint p1">
                            <div class="point"></div>
                            <div class="text">两江新区</div>
                          </div>
                          <div class="bgPoint p2">
                            <div class="point"></div>
                            <div class="text">璧山区</div>
                          </div>
                          <div class="bgPoint p3">
                            <div class="point"></div>
                            <div class="text">经开区</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="pueCore">
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon"></div>
                          在用PUE：
                        </div>
                        <div class="w5">四川省-1.62</div>
                        <div class="w5">重庆市-1.54</div>
                      </div>
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon2"></div>
                          在建PUE：
                        </div>
                        <div class="w5">四川省-1.33</div>
                        <div class="w5">重庆市-1.32</div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="puedownCore">
                      <div class="row flex-box-between">
                        <div class="w3 flex-box">
                          <div class="w3icon1"></div>
                          <div>
                            <div>已建5G基站数量(个)</div>
                            <div>80000+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon2"></div>
                          <div>
                            <div>运营数据中心（个）</div>
                            <div>10+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon3"></div>
                          <div>
                            <div>机架规模(万架)</div>
                            <div>12+</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="posi flex-box posi3"
                  @click="groupFunRight(1, 3)"
                  :class="
                    (rowIndex === 1 && sonIndex === 3) ||
                    (!rowIndex && !sonIndex)
                      ? ''
                      : 'op3'
                  "
                >
                  <div class="greenBox"></div>
                  <div class="name s3">粤港澳枢纽</div>
                </div>
                <div
                  class="dialogPosi posi3"
                  v-show="rowIndex === 1 && sonIndex === 3"
                >
                  <div class="greenPosi g2"></div>
                  <div class="infoPosi">
                    <div class="infomap flex-box-center">
                      <div class="infomapitem">
                        <div class="title">韶关集群起步区</div>
                        <!-- 韶关集群：于韶关高新区，详细分布在浈江工业园、曲江白土开发区以及莞韶城 -->
                        <div class="bg bg5">
                          <div class="bgPoint p1">
                            <div class="point"></div>
                            <div class="text">高新区</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="pueCore">
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon"></div>
                          在用PUE：
                        </div>
                        <div class="w5">广东省-1.59</div>
                      </div>
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon2"></div>
                          在建PUE：
                        </div>
                        <div class="w5">广东省-1.33</div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="puedownCore">
                      <div class="row flex-box-between">
                        <div class="w3 flex-box">
                          <div class="w3icon1"></div>
                          <div>
                            <div>已建5G基站数量(个)</div>
                            <div>5000+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon2"></div>
                          <div>
                            <div>运营数据中心（个）</div>
                            <div>5+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon3"></div>
                          <div>
                            <div>机架规模(万架)</div>
                            <div>5+</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="posi flex-box posi4"
                  @click="groupFunRight(1, 2)"
                  :class="
                    (rowIndex === 1 && sonIndex === 2) ||
                    (!rowIndex && !sonIndex)
                      ? ''
                      : 'op3'
                  "
                >
                  <div class="greenBox greenBox3"></div>
                  <div class="name s4">长三角枢纽</div>
                </div>
                <div
                  class="dialogPosi posi4"
                  v-show="rowIndex === 1 && sonIndex === 2"
                >
                  <div class="greenPosi"></div>
                  <div class="infoPosi">
                    <div class="infomap flex-box-center">
                      <div class="line"></div>
                      <div class="infomapitem">
                        <div class="title">
                          长三角生态绿色一体化发展示范区集群起步区
                        </div>
                        <div class="bg bg1">
                          <div class="bgPoint p1">
                            <div class="point"></div>
                            <div class="text">青浦区</div>
                          </div>
                          <div class="bgPoint p2 high">
                            <div class="point"></div>
                            <div class="text">吴江区</div>
                          </div>
                          <div class="bgPoint p3">
                            <div class="point"></div>
                            <div class="text">嘉善县</div>
                          </div>
                        </div>
                      </div>
                      <div class="infomapitem">
                        <div class="title">芜湖数据中心集群起步区</div>
                        <div class="bg bg2">
                          <div class="bgPoint p1">
                            <div class="point"></div>
                            <div class="text">鸠江区</div>
                          </div>
                          <div class="bgPoint p2">
                            <div class="point"></div>
                            <div class="text">弋江区</div>
                          </div>
                          <div class="bgPoint p3">
                            <div class="point"></div>
                            <div class="text">无为市</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="pueCore">
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon"></div>
                          在用PUE：
                        </div>
                        <div class="w5">江苏省-1.52</div>
                        <div class="w5">浙江省-1.55</div>
                        <div class="w5">安徽省-1.48</div>
                        <div class="w5">上海市-1.58</div>
                      </div>
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon2"></div>
                          在建PUE：
                        </div>
                        <div class="w5">江苏省-1.37</div>
                        <div class="w5">浙江省-1.35</div>
                        <div class="w5">安徽省-1.35</div>
                        <div class="w5">上海市-1.31</div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="puedownCore">
                      <div class="row flex-box-between">
                        <div class="w3 flex-box">
                          <div class="w3icon1"></div>
                          <div>
                            <div>已建5G基站数量(个)</div>
                            <div>10000+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon2"></div>
                          <div>
                            <div>运营数据中心（个）</div>
                            <div>10+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon2"></div>
                          <div>
                            <div>机架规模(万架)</div>
                            <div>10+</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="posi flex-box posi5"
                  @click="groupFunRight(2, 0)"
                  :class="
                    (rowIndex === 2 && sonIndex === 0) ||
                    (!rowIndex && !sonIndex)
                      ? ''
                      : 'op3'
                  "
                >
                  <div class="name yellow s1">宁夏枢纽</div>
                  <div class="greenBox yellowBox"></div>
                </div>
                <div
                  class="dialogPosi posi5"
                  v-show="rowIndex === 2 && sonIndex === 0"
                >
                  <div class="greenPosi on"></div>
                  <div class="infoPosi">
                    <div class="infomap flex-box-center">
                      <div class="infomapitem">
                        <div class="title">中卫集群起步区</div>
                        <div class="bg bg7">
                          <div class="bgPoint p1">
                            <div class="point"></div>
                            <div class="text">工业园</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="pueCore">
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon"></div>
                          在用PUE：
                        </div>
                        <div class="w5">宁夏回族自治区-1.43</div>
                      </div>
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon2"></div>
                          在建PUE：
                        </div>
                        <div class="w5">宁夏回族自治区-1.2</div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="puedownCore">
                      <div class="row flex-box-between">
                        <div class="w3 flex-box">
                          <div class="w3icon1"></div>
                          <div>
                            <div>已建5G基站数量(个)</div>
                            <div>5000+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon2"></div>
                          <div>
                            <div>运营数据中心（个）</div>
                            <div>6+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon3"></div>
                          <div>
                            <div>机架规模(万架)</div>
                            <div>2+</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="posi flex-box posi6"
                  @click="groupFunRight(2, 1)"
                  :class="
                    (rowIndex === 2 && sonIndex === 1) ||
                    (!rowIndex && !sonIndex)
                      ? ''
                      : 'op3'
                  "
                >
                  <div class="name yellow s1">甘肃枢纽</div>
                  <div class="greenBox yellowBox"></div>
                </div>
                <div
                  class="dialogPosi posi6"
                  v-show="rowIndex === 2 && sonIndex === 1"
                >
                  <div class="greenPosi on"></div>
                  <div class="infoPosi">
                    <div class="infomap flex-box-center">
                      <div class="infomapitem">
                        <div class="title">庆阳集群起步区</div>
                        <div class="bg bg8">
                          <div class="bgPoint p1">
                            <div class="point"></div>
                            <div class="text">西峰区</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="pueCore">
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon"></div>
                          在用PUE：
                        </div>
                        <div class="w5">甘肃省-1.53</div>
                      </div>
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon2"></div>
                          在建PUE：
                        </div>
                        <div class="w5">甘肃省-1.37</div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="puedownCore">
                      <div class="row flex-box-between">
                        <div class="w3 flex-box">
                          <div class="w3icon1"></div>
                          <div>
                            <div>已建5G基站数量(个)</div>
                            <div>5000+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon2"></div>
                          <div>
                            <div>运营数据中心（个）</div>
                            <div>17+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon3"></div>
                          <div>
                            <div>机架规模(万架)</div>
                            <div>15+</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="posi flex-box posi7"
                  @click="groupFunRight(2, 2)"
                  :class="
                    (rowIndex === 2 && sonIndex === 2) ||
                    (!rowIndex && !sonIndex)
                      ? ''
                      : 'op3'
                  "
                >
                  <div class="name yellow s1">内蒙古枢纽</div>
                  <div class="greenBox yellowBox"></div>
                </div>
                <div
                  class="dialogPosi posi7"
                  v-show="rowIndex === 2 && sonIndex === 2"
                >
                  <div class="greenPosi on"></div>
                  <div class="infoPosi">
                    <div class="infomap flex-box-center">
                      <div class="infomapitem">
                        <div class="title">和林格尔集群起步区</div>
                        <div class="bg bg9">
                          <div class="bgPoint p1">
                            <div class="point"></div>
                            <div class="text">和林格尔县</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="pueCore">
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon"></div>
                          在用PUE：
                        </div>
                        <div class="w5">内蒙古自治区-1.53</div>
                      </div>
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon2"></div>
                          在建PUE：
                        </div>
                        <div class="w5">内蒙古自治区-1.23</div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="puedownCore">
                      <div class="row flex-box-between">
                        <div class="w3 flex-box">
                          <div class="w3icon1"></div>
                          <div>
                            <div>已建5G基站数量(个)</div>
                            <div>5000+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon2"></div>
                          <div>
                            <div>运营数据中心（个）</div>
                            <div>5+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon3"></div>
                          <div>
                            <div>机架规模(万架)</div>
                            <div>10+</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="posi flex-box posi8"
                  @click="groupFunRight(2, 3)"
                  :class="
                    (rowIndex === 2 && sonIndex === 3) ||
                    (!rowIndex && !sonIndex)
                      ? ''
                      : 'op3'
                  "
                >
                  <div class="name yellow s1">贵州枢纽</div>
                  <div class="greenBox yellowBox"></div>
                </div>
                <div
                  class="dialogPosi posi8"
                  v-show="rowIndex === 2 && sonIndex === 3"
                >
                  <div class="greenPosi on y2"></div>
                  <div class="infoPosi">
                    <div class="infomap flex-box-center">
                      <div class="infomapitem">
                        <div class="title">贵安集群起步区</div>
                        <div class="bg bg10">
                          <div class="bgPoint p1">
                            <div class="point"></div>
                            <div class="text">贵安新区</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="pueCore">
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon"></div>
                          在用PUE：
                        </div>
                        <div class="w5">贵州省-1.51</div>
                      </div>
                      <div class="row flex-box">
                        <div class="w5 flex-box">
                          <div class="w5icon2"></div>
                          在建PUE：
                        </div>
                        <div class="w5">贵州省-1.34</div>
                      </div>
                    </div>
                    <div class="pueLine"></div>
                    <div class="puedownCore">
                      <div class="row flex-box-between">
                        <div class="w3 flex-box">
                          <div class="w3icon1"></div>
                          <div>
                            <div>已建5G基站数量(个)</div>
                            <div>10000+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon2"></div>
                          <div>
                            <div>运营数据中心（个）</div>
                            <div>8+</div>
                          </div>
                        </div>
                        <div class="w3 flex-box">
                          <div class="w3icon3"></div>
                          <div>
                            <div>机架规模(万架)</div>
                            <div>5+</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="line line1"
                  :class="
                    rowIndex && (sonIndex || sonIndex === 0) ? 'op3' : 'lineopa'
                  "
                ></div>
                <div
                  class="line line2"
                  :class="
                    rowIndex && (sonIndex || sonIndex === 0) ? 'op3' : 'lineopa'
                  "
                ></div>
                <div
                  class="line line3"
                  :class="
                    rowIndex && (sonIndex || sonIndex === 0) ? 'op3' : 'lineopa'
                  "
                ></div>
                <div
                  class="line line4"
                  :class="
                    rowIndex && (sonIndex || sonIndex === 0) ? 'op3' : 'lineopa'
                  "
                ></div>
                <div
                  class="line line5"
                  :class="
                    rowIndex && (sonIndex || sonIndex === 0) ? 'op3' : 'lineopa'
                  "
                ></div>
                <div
                  class="line line6"
                  :class="
                    rowIndex && (sonIndex || sonIndex === 0) ? 'op3' : 'lineopa'
                  "
                ></div>
                <div
                  class="line line7"
                  :class="
                    rowIndex && (sonIndex || sonIndex === 0) ? 'op3' : 'lineopa'
                  "
                ></div>
                <div
                  class="line line8"
                  :class="
                    rowIndex && (sonIndex || sonIndex === 0) ? 'op3' : 'lineopa'
                  "
                ></div>
                <div
                  class="line line9"
                  :class="
                    rowIndex && (sonIndex || sonIndex === 0) ? 'op3' : 'lineopa'
                  "
                ></div>
              </div>
              <div class="ysPointList" v-show="showYs">
                <div class="ysLenged flex-box-between">
                  <div class="item c_1 flex-box">
                    <div class="icon"></div>
                    <div class="ne">华为云</div>
                  </div>
                  <div class="item c_2 flex-box">
                    <div class="icon"></div>
                    <div class="ne">百度云</div>
                  </div>
                  <div class="item c_3 flex-box">
                    <div class="icon"></div>
                    <div class="ne">阿里云</div>
                  </div>
                  <div class="item c_4 flex-box">
                    <div class="icon"></div>
                    <div class="ne">字节跳动</div>
                  </div>
                  <div class="item c_5 flex-box">
                    <div class="icon"></div>
                    <div class="ne">Ucloud</div>
                  </div>
                  <div class="item c_6 flex-box">
                    <div class="icon"></div>
                    <div class="ne">腾讯云</div>
                  </div>
                </div>
                <div class="ys c_1 ys1">
                  <div class="icon"></div>
                  <div class="ne">张掖</div>
                </div>
                <div class="ys c_1 ys2">
                  <div class="icon"></div>
                  <div class="ne">贵安</div>
                </div>
                <div class="ys c_1 ys3">
                  <div class="icon"></div>
                  <div class="ne">延安</div>
                </div>
                <div class="ys c_1 ys4">
                  <div class="icon"></div>
                  <div class="ne">乌兰察布</div>
                </div>
                <div class="ys c_1 ys5">
                  <div class="icon"></div>
                  <div class="ne">永州</div>
                </div>
                <div class="ys c_2 ys6">
                  <div class="icon"></div>
                  <div class="ne">阳泉</div>
                </div>
                <div class="ys c_2 ys7">
                  <div class="icon"></div>
                  <div class="ne">徐水</div>
                </div>
                <div class="ys c_2 ys8">
                  <div class="icon"></div>
                  <div class="ne">定兴</div>
                </div>
                <div class="ys c_3 ys9">
                  <div class="icon"></div>
                  <div class="ne">成都</div>
                </div>
                <div class="ys c_3 ys10">
                  <div class="icon"></div>
                  <div class="ne"></div>
                </div>
                <div class="ys c_3 ys11">
                  <div class="icon"></div>
                  <div class="ne">张北</div>
                </div>
                <div class="ys c_3 ys12">
                  <div class="icon"></div>
                  <div class="ne">河源</div>
                </div>
                <div class="ys c_3 ys13">
                  <div class="icon"></div>
                  <div class="ne">杭州</div>
                </div>
                <div class="ys c_3 ys14">
                  <div class="icon"></div>
                  <div class="ne">南通</div>
                </div>
                <div class="ys c_4 ys15">
                  <div class="icon"></div>
                  <div class="ne"></div>
                </div>
                <div class="ys c_5 ys16">
                  <div class="icon"></div>
                  <div class="ne"></div>
                </div>
                <div class="ys c_6 ys17">
                  <div class="icon"></div>
                  <div class="ne">重庆</div>
                </div>
                <div class="ys c_6 ys18">
                  <div class="icon"></div>
                  <div class="ne">怀来</div>
                </div>
                <div class="ys c_6 ys19">
                  <div class="icon"></div>
                  <div class="ne">韶关</div>
                </div>
                <div class="ys c_6 ys20">
                  <div class="icon"></div>
                  <div class="ne">福州</div>
                </div>
                <div class="ys c_6 ys21">
                  <div class="icon"></div>
                  <div class="ne">青浦</div>
                </div>
              </div>
            </div>
            <div class="flex-box-end">
              <div class="bottomRight">
                数据来源:中国算力发展指数白皮书(2023年)、公开数据统计
              </div>
            </div>
          </div>
          <div class="namemap" v-show="nameShow">
            <div class="mplist">
              <div class="stem flex-box">
                <div class="color c1"></div>
                <div class="text">2：内蒙和贵州</div>
              </div>
              <div class="stem flex-box">
                <div class="color c2"></div>
                <div class="text">4：京津冀、长三角、粤港澳、成渝四大区域</div>
              </div>
              <div class="stem flex-box">
                <div class="icon"></div>
                <div class="text">31：31省份的重点城市布局区域云</div>
              </div>
            </div>
            <div class="bottomRight2">
              数据来源:中国算力发展指数白皮书(2023年)、公开数据统计
            </div>
          </div>
        </div>
        <div class="newCenterBox">
          <div class="bgCore flex-box-between">
            <div>
              <div class="titleBox flex-box-between">
                <div class="title">算力规模</div>
              </div>
              <div class="ecCore ecCore1">
                <div class="ballCore">
                  <div class="cbg"></div>
                  <div class="c1">
                    <div class="size4">
                      120<span class="size2">EFlops</span>
                    </div>
                    <div class="size2">基础算力</div>
                  </div>
                  <div class="cc">
                    <div class="size1">
                      302<span class="size2">EFlops</span>
                    </div>
                    <div class="size3">总规模</div>
                  </div>
                  <div class="flex-box-between">
                    <div class="c3">
                      <div class="size4">
                        178.5<span class="size2">EFlops</span>
                      </div>
                      <div class="size2">智算算力</div>
                    </div>
                    <div class="c3">
                      <div class="size4">
                        3.9<span class="size2">EFlops</span>
                      </div>
                      <div class="size2">超算算力</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div class="titleBox flex-box-between">
                <div class="title" v-show="rightTopIndex == 1">
                  综合算力指数TOP10省份
                </div>
                <div class="title" v-show="rightTopIndex == 2">
                  算力指数TOP10省份
                </div>
                <div class="title" v-show="rightTopIndex == 3">
                  存力指数TOP10省份
                </div>
                <div class="title" v-show="rightTopIndex == 4">
                  运力指数TOP10省份
                </div>
                <div class="title" v-show="rightTopIndex == 5">
                  环境指数TOP10省份
                </div>
              </div>
              <div class="tabs flex-box-between">
                <div
                  class="item"
                  :class="rightTopIndex == 1 ? 'on' : ''"
                  @click="rightTopIndexChange(1)"
                >
                  综合
                </div>
                <div
                  class="item"
                  :class="rightTopIndex == 2 ? 'on' : ''"
                  @click="rightTopIndexChange(2)"
                >
                  算力
                </div>
                <div
                  class="item"
                  :class="rightTopIndex == 3 ? 'on' : ''"
                  @click="rightTopIndexChange(3)"
                >
                  存力
                </div>
                <div
                  class="item"
                  :class="rightTopIndex == 4 ? 'on' : ''"
                  @click="rightTopIndexChange(4)"
                >
                  运力
                </div>
                <div
                  class="item"
                  :class="rightTopIndex == 5 ? 'on' : ''"
                  @click="rightTopIndexChange(5)"
                >
                  环境
                </div>
              </div>
              <div v-show="rightTopIndex == 1">
                <div class="ec2" ref="ec2"></div>
              </div>
              <div class="rightEcharts flex-box-between">
                <div class="item" v-show="rightTopIndex == 2">
                  <div class="ecNew_1" ref="ecNew_1"></div>
                  <div class="tabs flex-box-center">
                    <div
                      class="son"
                      :class="tabMiniIndex1 === 1 ? 'on' : ''"
                      @click="tabMiniIndexClick(1, 1)"
                    >
                      算力指数
                    </div>
                    <div
                      class="son"
                      :class="tabMiniIndex1 === 2 ? 'on' : ''"
                      @click="tabMiniIndexClick(1, 2)"
                    >
                      算力规模分指数
                    </div>
                    <div
                      class="son"
                      :class="tabMiniIndex1 === 3 ? 'on' : ''"
                      @click="tabMiniIndexClick(1, 3)"
                    >
                      算力质效分指数
                    </div>
                  </div>
                </div>
                <div class="item" v-show="rightTopIndex == 3">
                  <div class="ecNew_2" ref="ecNew_2"></div>
                  <div class="tabs flex-box-center">
                    <div
                      class="son"
                      :class="tabMiniIndex2 === 1 ? 'on' : ''"
                      @click="tabMiniIndexClick(2, 1)"
                    >
                      存力指数
                    </div>
                    <div
                      class="son"
                      :class="tabMiniIndex2 === 2 ? 'on' : ''"
                      @click="tabMiniIndexClick(2, 2)"
                    >
                      存力规模分指数
                    </div>
                    <div
                      class="son"
                      :class="tabMiniIndex2 === 3 ? 'on' : ''"
                      @click="tabMiniIndexClick(2, 3)"
                    >
                      存力质效分指数
                    </div>
                  </div>
                </div>
                <div class="item" v-show="rightTopIndex == 4">
                  <div class="ecNew_3" ref="ecNew_3"></div>
                  <div class="tabs flex-box-center">
                    <div
                      class="son"
                      :class="tabMiniIndex3 === 1 ? 'on' : ''"
                      @click="tabMiniIndexClick(3, 1)"
                    >
                      运力指数
                    </div>
                    <div
                      class="son"
                      :class="tabMiniIndex3 === 2 ? 'on' : ''"
                      @click="tabMiniIndexClick(3, 2)"
                    >
                      运力规模分指数
                    </div>
                    <div
                      class="son"
                      :class="tabMiniIndex3 === 3 ? 'on' : ''"
                      @click="tabMiniIndexClick(3, 3)"
                    >
                      运力质效分指数
                    </div>
                  </div>
                </div>
                <div class="item" v-show="rightTopIndex == 5">
                  <div class="ecNew_4" ref="ecNew_4"></div>
                  <div class="tabs flex-box-center">
                    <div
                      class="son"
                      :class="tabMiniIndex4 === 1 ? 'on' : ''"
                      @click="tabMiniIndexClick(4, 1)"
                    >
                      环境指数
                    </div>
                    <div
                      class="son"
                      :class="tabMiniIndex4 === 2 ? 'on' : ''"
                      @click="tabMiniIndexClick(4, 2)"
                    >
                      资源环境分指数
                    </div>
                    <div
                      class="son"
                      :class="tabMiniIndex4 === 3 ? 'on' : ''"
                      @click="tabMiniIndexClick(4, 3)"
                    >
                      市场环境分指数
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex-box-end">
                <div class="bottomRight">
                  数据来源: 中国综合算力评价白皮书(2023年)
                </div>
              </div>
            </div>
          </div>
          <div class="bgCore" v-show="rightBottomShowIndex === 1">
            <div class="titleBox flex-box-between">
              <div class="title">八大枢纽算力规模</div>
              <div class="tabss flex-box-end">
                <div class="st on"></div>
                <div class="st" @click="rightBottomShowIndex = 2"></div>
              </div>
            </div>
            <div class="groupBox">
              <div class="list flex-box">
                <div
                  class="item"
                  :class="
                    rowIndex2 === 2 || (rowIndex2 === 1 && sonIndex2 != index)
                      ? 'on'
                      : ''
                  "
                  v-for="(item, index) in list"
                  :key="index"
                  @click="groupFun(1, index)"
                >
                  <div class="w1">
                    {{ leftTabIndex === 1 ? item.w1 : item.w4 }}
                  </div>
                  <div class="w2">{{ item.w2 }}</div>
                  <div class="w3">{{ item.w3 }}</div>
                  <div
                    class="itemPosi"
                    v-show="
                      rowIndex2 === 1 &&
                      sonIndex2 === index &&
                      leftTabIndex != 2
                    "
                  >
                    <div
                      class="ss"
                      :class="[`s${sindex}`, sonIndex2 === 3 ? 's3' : '']"
                      v-for="(son, sindex) in item.children"
                      :key="sindex"
                      @click.stop="dxsonIndexFun(sindex)"
                    >
                      <div class="n1">
                        {{ leftTabIndex === 1 ? son.n1 : son.n2 }}
                      </div>
                      <div class="n2">{{ item.w2 }}</div>
                      <div class="n3">{{ son.n3 }}</div>
                    </div>
                  </div>
                  <div
                    class="itemPosi2"
                    v-show="
                      rowIndex2 === 1 &&
                      sonIndex2 === index &&
                      leftTabIndex != 2
                    "
                  >
                    <div
                      class="ss"
                      :class="[
                        `s${sindex}`,
                        sonIndex2 === 3 ? 's3' : '',
                        dxsonIndex == sindex ? 'show' : '',
                      ]"
                      v-for="(son, sindex) in item.children"
                      :key="sindex"
                    >
                      <div class="n1">{{ son.dxn }}</div>
                      <div class="n2">{{ son.dxw }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="list list2 flex-box">
                <div
                  class="item"
                  :class="
                    rowIndex2 === 1 || (rowIndex2 === 2 && sonIndex2 != index)
                      ? 'on'
                      : ''
                  "
                  v-for="(item, index) in list2"
                  :key="index"
                  @click="groupFun(2, index)"
                >
                  <div class="w1">
                    {{ leftTabIndex === 1 ? item.w1 : item.w4 }}
                  </div>
                  <div class="w2">{{ item.w2 }}</div>
                  <div class="w3">{{ item.w3 }}</div>
                  <div
                    class="itemPosi"
                    v-show="
                      rowIndex2 === 2 &&
                      sonIndex2 === index &&
                      leftTabIndex != 2
                    "
                  >
                    <div
                      class="ss"
                      :class="[
                        `s${sindex}`,
                        sonIndex2 === 3 || sonIndex2 === 2 ? 's3' : '',
                      ]"
                      v-for="(son, sindex) in item.children"
                      :key="sindex"
                      @click.stop="dxsonIndexFun(sindex)"
                    >
                      <div class="n1">
                        {{ leftTabIndex === 1 ? son.n1 : son.n2 }}
                      </div>
                      <div class="n2">{{ item.w2 }}</div>
                      <div class="n3">{{ son.n3 }}</div>
                    </div>
                  </div>
                  <div
                    class="itemPosi2"
                    v-show="
                      rowIndex2 === 2 &&
                      sonIndex2 === index &&
                      leftTabIndex != 2
                    "
                  >
                    <div
                      class="ss"
                      :class="[
                        `s${sindex}`,
                        sonIndex2 === 3 || sonIndex2 === 2 ? 's3' : '',
                        dxsonIndex == sindex ? 'show' : '',
                      ]"
                      v-for="(son, sindex) in item.children"
                      :key="sindex"
                    >
                      <div class="n1">{{ son.dxn }}</div>
                      <div class="n2">{{ son.dxw }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="tabs flex-box-center on">
              <div
                class="item"
                :class="leftTabIndex == 1 ? 'on' : ''"
                @click="leftTabIndexFun(1)"
              >
                在用算力
              </div>
              <div
                class="item"
                :class="leftTabIndex == 2 ? 'on' : ''"
                @click="leftTabIndexFun(2)"
              >
                在建算力
              </div>
            </div>
            <div class="flex-box-end">
              <div class="bottomRight">数据来源: 中国信通院</div>
            </div>
          </div>
          <div class="bgCore" v-show="rightBottomShowIndex === 2">
            <div class="titleBox flex-box-between">
              <div class="title">中国电信算力规模分布</div>
              <div class="tabss flex-box-end">
                <div class="st" @click="rightBottomShowIndex = 1"></div>
                <div class="st on"></div>
              </div>
            </div>
            <div class="groupBox">
              <div class="list flex-box">
                <div
                  class="item"
                  :class="
                    rowIndex2 === 2 || (rowIndex2 === 1 && sonIndex2 != index)
                      ? 'on'
                      : ''
                  "
                  v-for="(item, index) in listdx"
                  :key="index"
                  @click="groupFun(1, index)"
                >
                  <div class="w1">{{ item.w1 }}</div>
                  <div class="w2">{{ item.w2 }}</div>
                  <div class="w3">{{ item.w3 }}</div>
                  <div
                    class="itemPosi"
                    v-show="
                      rowIndex2 === 1 &&
                      sonIndex2 === index &&
                      leftTabIndex != 2
                    "
                  >
                    <div
                      class="ss"
                      :class="[
                        `s${sindex}`,
                        sonIndex2 === 3 ? 's3' : '',
                        sonIndex2 === 4 ? 's3' : '',
                      ]"
                      v-for="(son, sindex) in item.children"
                      :key="sindex"
                    >
                      <div class="n1">{{ son.n1 }}</div>
                      <div class="n2">{{ item.w2 }}</div>
                      <div class="n3">{{ son.n3 }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="list list2 flex-box">
                <div
                  class="item"
                  :class="
                    rowIndex2 === 1 || (rowIndex2 === 2 && sonIndex2 != index)
                      ? 'on'
                      : ''
                  "
                  v-for="(item, index) in list2dx"
                  :key="index"
                  @click="groupFun(2, index)"
                >
                  <div class="w1">{{ item.w1 }}</div>
                  <div class="w2">{{ item.w2 }}</div>
                  <div class="w3">{{ item.w3 }}</div>
                  <div
                    class="itemPosi"
                    v-show="
                      rowIndex2 === 2 &&
                      sonIndex2 === index &&
                      leftTabIndex != 2
                    "
                  >
                    <div
                      class="ss"
                      :class="[`s${sindex}`, sonIndex2 === 3 ? 's3' : '']"
                      v-for="(son, sindex) in item.children"
                      :key="sindex"
                    >
                      <div class="n1">{{ son.n1 }}</div>
                      <div class="n2">{{ item.w2 }}</div>
                      <div class="n3">{{ son.n3 }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- <div class="tabs flex-box-center on">
              <div class="item" :class="leftTabIndex == 1 ? 'on' : ''" @click="leftTabIndexFun(1)">在用算力</div>
              <div class="item" :class="leftTabIndex == 2 ? 'on' : ''" @click="leftTabIndexFun(2)">在建算力</div>
            </div>
            <div class="flex-box-end">
              <div class="bottomRight">数据来源: 中国信通院</div>
            </div> -->
          </div>
          <!-- <div class="bgCore" v-show="rightBottomShowIndex === 2">
            <div class="titleBox flex-box-between">
              <div class="title">八大枢纽资源配置</div>
              <div class="tabss flex-box-end">
                <div class="st" @click="rightBottomShowIndex = 1, showYs = false"></div>
                <div class="st on"></div>
              </div>
            </div>
            <div class="threeList flex-box-between">
              <div class="itemCore">
                <div class="item item1">
                  <div class="slgmDialog" v-show="slgmShow">
                    <div class="stitle">算力规模</div>
                    <div class="itemlist flex-box-between">
                      <div class="sitem" @click="itemlistShow = !itemlistShow">
                        <div class="w1">195.23</div>
                        <div class="w2">PFlops</div>
                        <div class="w3">跨区域算力 <br>(天翼云在4+4枢纽) </div>
                        <div v-show="itemlistShow">
                          <div class="sonListItem sw1">
                            <div class="ww1">2.41</div>
                            <div class="ww2">PFlops</div>
                            <div class="ww3">京津冀</div>
                          </div>
                          <div class="sonListItem sw2">
                            <div class="ww1">38.43</div>
                            <div class="ww2">PFlops</div>
                            <div class="ww3">长三角</div>
                          </div>
                          <div class="sonListItem sw3">
                            <div class="ww1">0.48</div>
                            <div class="ww2">PFlops</div>
                            <div class="ww3">粤港澳</div>
                          </div>
                          <div class="sonListItem sw4">
                            <div class="ww1">43.36</div>
                            <div class="ww2">PFlops</div>
                            <div class="ww3">成渝</div>
                          </div>
                          <div class="sonListItem sw5">
                            <div class="ww1">58.18</div>
                            <div class="ww2">PFlops</div>
                            <div class="ww3">内蒙古</div>
                          </div>
                          <div class="sonListItem sw6">
                            <div class="ww1">0.48</div>
                            <div class="ww2">PFlops</div>
                            <div class="ww3">甘肃</div>
                          </div>
                          <div class="sonListItem sw7">
                            <div class="ww1">47.75</div>
                            <div class="ww2">PFlops</div>
                            <div class="ww3">贵州</div>
                          </div>
                          <div class="sonListItem sw8">
                            <div class="ww1">4.15</div>
                            <div class="ww2">PFlops</div>
                            <div class="ww3">宁夏</div>
                          </div>
                        </div>
                      </div>
                      <div class="sitem">
                        <div class="w1">60.1</div>
                        <div class="w2">PFlops</div>
                        <div class="w3">核心云算力(长三角)</div>
                      </div>
                      <div class="sitem">
                        <div class="w1">12.32</div>
                        <div class="w2">PFlops</div>
                        <div class="w3">边缘云算力(长三角)</div>
                      </div>
                    </div>
                  </div>
                  <div class="tabss flex-box-end">
                    <div class="st on"></div>
                    <div class="st"></div>
                    <div class="st"></div>
                  </div>
                  <div class="son flex-box-between first" @click="slgmFun" :class="slgmShow ? 'on' : ''">
                    <div class="flex-box">
                      <div class="icon"><img src="~@/assets/images/page7/icon1.png" alt=""></div>
                      <div class="value flex-box-between">
                        <div>算力规模：</div>
                      </div>
                    </div>
                    <div class="value flex-box-between">
                      <div>3.1EFLOPS</div>
                    </div>
                  </div>
                  <div class="son flex-box-between">
                    <div class="flex-box">
                      <div class="icon"><img src="~@/assets/images/page7/icon2.png" alt=""></div>
                      <div class="value flex-box-between">
                        <div>机架数(万架)：</div>
                      </div>
                    </div>
                    <div class="value flex-box-between">
                      <div>40+</div>
                    </div>
                  </div>
                  <div class="son flex-box-between">
                    <div class="flex-box">
                      <div class="icon"><img src="~@/assets/images/page7/icon3.png" alt=""></div>
                      <div class="value flex-box-between">
                        <div>DCI：</div>
                      </div>
                    </div>
                    <div class="value">
                      <p>32万公里光缆网</p>
                      <p>骨干网带宽>300T</p>
                    </div>
                  </div>
                  <div class="son flex-box-between">
                    <div class="flex-box">
                      <div class="icon"><img src="~@/assets/images/page7/icon4.png" alt=""></div>
                      <div class="value flex-box-between">
                        <div>绿色(枢纽节点)：</div>
                      </div>
                    </div>
                    <div class="value">
                      <p>新建PUE&lt;1.3</p>
                      <p>南方PUE&lt;1.25</p>
                      <p>北方PUE&lt;1.2</p>
                    </div>
                  </div>
                  <div class="name" @click="nameShowFun" :class="nameShow ? 'on' : ''">2+4+31+X+O</div>
                  <div class="nameline"></div>
                </div>
              </div>

              <div class="itemCore" @click="showYsFun">
                <div class="item item2">
                  <div class="flex-box-center">
                    <div class="yuan y1">
                      <div class="icon"><img src="~@/assets/images/page7/icon-1.png" alt=""></div>
                      <div class="text">百度云</div>
                    </div>
                  </div>
                  <div class="flex-box-between">
                    <div class="yuan y2">
                      <div class="icon"><img src="~@/assets/images/page7/icon-2.png" alt=""></div>
                      <div class="text">腾讯云</div>
                    </div>
                    <div class="yuan y3">
                      <div class="icon"><img src="~@/assets/images/page7/icon-3.png" alt=""></div>
                      <div class="text">华为云</div>
                    </div>
                    <div class="yuan y4">
                      <div class="icon"><img src="~@/assets/images/page7/icon-4.png" alt=""></div>
                      <div class="text">字节跳动</div>
                    </div>
                  </div>
                  <div class="flex-box-center">
                    <div class="yuan y5">
                      <div class="icon"><img src="~@/assets/images/page7/icon-5.png" alt=""></div>
                      <div class="text">Ucloud</div>
                    </div>
                    <div class="yuan y6">
                      <div class="icon"><img src="~@/assets/images/page7/icon-6.png" alt=""></div>
                      <div class="text">阿里云</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex-box-end">
              <div class="bottomRight">数据来源: 公开数据整理 </div>
            </div>
          </div> -->
        </div>
        <div class="centerCore" :class="!centerPosiCoreShow ? 'on' : ''">
          <div class="rightCore" v-show="showRightEcharts">
            <div
              class="close"
              @click="(rightEchartsShow = false), (showRightEcharts = false)"
            ></div>
            <div class="titleBox flex-box-between">
              <div class="title">数据中心机架规模</div>
            </div>
            <div class="ec4" ref="ec4"></div>
            <div class="titleBox flex-box-between">
              <div class="title">数据中心的单机柜平均功能/PUE</div>
            </div>
            <div class="two flex-box-center">
              <div class="ec5" ref="ec5"></div>
              <div class="ec6" ref="ec6"></div>
            </div>
            <div class="flex-box-end">
              <div class="bottomRight">
                数据来源: 中国算力发展指数白皮书(2023年)
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// page0
import headerCommon from "../components/header/Index";
import bgCommon from "../components/bg/Index";
import * as echarts from "echarts";
import "echarts-gl";
import worldMap from "@/assets/json/world.json";
import d2Json from "@/assets/json/2d.json";
import d3Json from "@/assets/json/3d.json";
import d2JsonChina from "@/assets/json/2d_china.json";
import d3JsonChina from "@/assets/json/3d_china.json";

// page8
import chinaMap from "@/assets/json/chinaMap.json";
import chinaJson from "@/assets/json/chinaMap2d.json";

export default {
  components: {
    headerCommon,
    bgCommon,
  },
  data() {
    return {
      yearIndex: 1,
      posiList: [
        { n1: 2, n2: 71, n3: 75, n4: 60, n5: 72, n6: 68 },
        { n1: 4, n2: 56, n3: 50, n4: 60, n5: 68, n6: 62 },
        { n1: 6, n2: 53, n3: 47, n4: 58, n5: 65, n6: 61 },
        { n1: 1, n2: 82, n3: 86, n4: 70, n5: 82, n6: 84 },
        { n1: 3, n2: 58, n3: 53, n4: 57, n5: 70, n6: 66 },
      ],
      usachinaList: [
        {
          t1: 1,
          t2: 82,
          t3: 86,
          t4: 70,
          t5: 82,
          t6: 84,
          d1: 34,
          d2: 35,
          d3: 31,
          d4: 47,
        },
        {
          t1: 2,
          t2: 31,
          t3: 75,
          t4: 60,
          t5: 72,
          t6: 68,
          d1: 33,
          d2: 25,
          d3: 39,
          d4: 25,
        },
      ],
      lineAni: false,
      yearList: [2021, 2022],
      itemList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      chartsEarth: null,
      chartsMap: null,
      myChartLine: null,
      myChartBin: null,
      myChartbar: null,
      texture: null,
      eflops: 0,
      eflopsPercent: 0,
      eflopsName: "中国",
      showLeftCore: true,
      leftWordAni: false,
      showGif: false,
      timer: null,
      startOpacity: false,
      list2D: [],
      radarIndex: 0,
      radarIndexTimer: false,
      posiIndex: null,
      listArr: [],
      listArr3D: [],
      setClear13: null,
      setIntClear1: null,
      show2dMap: false,

      // page8
      myChartec2: null,
      myChartec4: null,
      myChartec5: null,
      myChartec6: null,
      myChartecNew_1: null,
      myChartecNew_2: null,
      myChartecNew_3: null,
      myChartecNew_4: null,
      tabMiniIndex1: 1,
      tabMiniIndex2: 1,
      tabMiniIndex3: 1,
      tabMiniIndex4: 1,
      ecNew_1ZoomTimer: null,
      ecNew_2ZoomTimer: null,
      ecNew_3ZoomTimer: null,
      ecNew_4ZoomTimer: null,
      rightEchartsShow: false,
      list: [
        {
          w1: "23.1",
          w2: "EFlops",
          w3: "京津冀枢纽",
          w4: 29.2,
          children: [
            { n1: "9.6", n2: 3.9, n3: "北京", dxn: 2634, dxw: "PFlops" },
            { n1: "1.5", n2: 5.0, n3: "天津", dxn: 157, dxw: "PFlops" },
            { n1: "12.0", n2: 20.2, n3: "河北", dxn: 63, dxw: "PFlops" },
          ],
        },
        {
          w1: "5.4",
          w2: "EFlops",
          w3: "成渝枢纽",
          w4: 6.6,
          children: [
            { n1: "3.2", n2: 2.6, n3: "四川省", dxn: 214, dxw: "PFlops" },
            { n1: "2.1", n2: 4.0, n3: "重庆市", dxn: 330, dxw: "PFlops" },
          ],
        },
        {
          w1: "36",
          w2: "EFlops",
          w3: "长三角枢纽",
          w4: 38.2,
          children: [
            { n1: "13.2", n2: 19.0, n3: "江苏省", dxn: 819, dxw: "PFlops" },
            { n1: "7.8", n2: 9.9, n3: "浙江省", dxn: 624, dxw: "PFlops" },
            { n1: "1.2", n2: 1.6, n3: "安徽省", dxn: 114, dxw: "PFlops" },
            { n1: "13.9", n2: 7.6, n3: "上海市", dxn: 3291, dxw: "PFlops" },
          ],
        },
        {
          w1: "13.7",
          w2: "EFlops",
          w3: "粤港澳枢纽",
          w4: 4.4,
          children: [
            { n1: "13.7", n2: 4.4, n3: "广东省", dxn: 619, dxw: "PFlops" },
          ],
        },
      ],
      list2: [
        {
          w1: "2.5",
          w2: "EFlops",
          w3: "宁夏枢纽",
          w4: 2.6,
          children: [
            {
              n1: "2.5",
              n2: 2.6,
              n3: "宁夏回族自治区",
              dxn: 1258,
              dxw: "PFlops",
            },
          ],
        },
        {
          w1: "3.4",
          w2: "EFlops",
          w3: "甘肃枢纽",
          w4: 0.3,
          children: [
            { n1: "3.4", n2: 0.3, n3: "甘肃省", dxn: 253, dxw: "PFlops" },
          ],
        },
        {
          w1: "5.4",
          w2: "EFlops",
          w3: "内蒙古枢纽",
          w4: 14.4,
          children: [
            {
              n1: "5.4",
              n2: 14.4,
              n3: "内蒙古自治区",
              dxn: 929,
              dxw: "PFlops",
            },
          ],
        },
        {
          w1: "8.7",
          w2: "EFlops",
          w3: "贵州枢纽",
          w4: 1.6,
          children: [
            { n1: "8.7", n2: 1.6, n3: "贵州省", dxn: 883, dxw: "PFlops" },
          ],
        },
      ],
      listdx: [
        {
          w1: 2854,
          w2: "PFlops",
          w3: "京津冀枢纽",
          w4: 2854,
          children: [
            { n1: 2634, n2: 2634, n3: "北京" },
            { n1: 157, n2: 157, n3: "天津" },
            { n1: 63, n2: 63, n3: "河北" },
          ],
        },
        {
          w1: 544,
          w2: "PFlops",
          w3: "成渝枢纽",
          w4: 544,
          children: [
            { n1: 214, n2: 214, n3: "四川省" },
            { n1: 330, n2: 330, n3: "重庆市" },
          ],
        },
        {
          w1: 4848,
          w2: "PFlops",
          w3: "长三角枢纽",
          w4: 4848,
          children: [
            { n1: 819, n2: 819, n3: "江苏省" },
            { n1: 624, n2: 624, n3: "浙江省" },
            { n1: 114, n2: 114, n3: "安徽省" },
            { n1: 3291, n2: 3291, n3: "上海市" },
          ],
        },
        {
          w1: 619,
          w2: "PFlops",
          w3: "粤港澳枢纽",
          w4: 619,
          children: [{ n1: 619, n2: 619, n3: "广东省" }],
        },
        {
          w1: 312,
          w2: "PFlops",
          w3: "其他地区",
          w4: 312,
          children: [{ n1: 312, n2: 312, n3: "其他地区" }],
        },
      ],
      list2dx: [
        {
          w1: 1258,
          w2: "PFlops",
          w3: "宁夏枢纽",
          w4: 1258,
          children: [{ n1: 1258, n2: 1258, n3: "宁夏回族自治区" }],
        },
        {
          w1: 253,
          w2: "PFlops",
          w3: "甘肃枢纽",
          w4: 253,
          children: [{ n1: 253, n2: 253, n3: "甘肃省" }],
        },
        {
          w1: 929,
          w2: "PFlops",
          w3: "内蒙古枢纽",
          w4: 929,
          children: [{ n1: 929, n2: 929, n3: "内蒙古自治区" }],
        },
        {
          w1: 883,
          w2: "PFlops",
          w3: "贵州枢纽",
          w4: 883,
          children: [{ n1: 883, n2: 883, n3: "贵州省" }],
        },
      ],
      leftTabIndex: 1,
      rowIndex: null,
      sonIndex: null,
      rowIndex2: null,
      sonIndex2: null,
      nameShow: false,
      slgmShow: false,
      showYs: false,
      itemlistShow: false,
      centerPosiCoreShow: false,
      showRightEcharts: false,
      chartsMap_8: false,

      showPage8: false,
      leftBottomTabIndex_8: 2,
      rightBottomShowIndex: 1,
      rightTopIndex: 1,
      setIntClearpage8: null,
      dxsonIndex: null,
    };
  },
  filters: {},
  mounted() {
    this.showGif = true;
    let setClear1 = setTimeout(() => {
      this.showGif = false;
      this.startOpacity = true;
    }, 3800);
    this.GLOBAL.timerArrayOut.push(setClear1);
    this.initMapDom();
    // if (this.$route.query && this.$route.query.isSkip) {
    //   this.startOpacity = true
    //   this.initMapDom()
    //   this.aniEndFun()
    // } else {
    //   this.showGif = true
    //   let setClear1 = setTimeout(() => {
    //     this.showGif = false
    //     this.startOpacity = true
    //   }, 3800);
    //   this.GLOBAL.timerArrayOut.push(setClear1)
    //   this.initMapDom()
    // }

    // 跳过旋转
    // this.aniEndFun()
    // this.changePage(8)
  },
  created() {
    let listArr = [];
    listArr.push(
      [d2JsonChina.list.slice(0, 51200).concat(d2Json.list.slice(0, 108000))],
      [d2JsonChina.list.concat(d2Json.list)]
    );
    // listArr.push(
    //   [chinaJson.list.slice(0, 7000)],
    //   [chinaJson.list.slice(0, 7000).concat(japanJson.list.slice(0, 400))],
    //   [chinaJson.list.slice(0, 7000).concat(japanJson.list.slice(0, 400)).concat(usaJson.list.slice(0, 4000))],
    //   [chinaJson.list.slice(0, 7000).concat(japanJson.list.slice(0, 400)).concat(usaJson.list.slice(0, 4000)).concat(englandJson.list.slice(0, 600))],
    //   [chinaJson.list.slice(0, 7000).concat(japanJson.list.slice(0, 400)).concat(usaJson.list.slice(0, 4000)).concat(englandJson.list.slice(0, 600)).concat(germanyJson.list.slice(0, 600))],
    //   [chinaJson.list.slice(0, 14000).concat(japanJson.list.slice(0, 400)).concat(usaJson.list.slice(0, 4000)).concat(englandJson.list.slice(0, 600)).concat(germanyJson.list.slice(0, 600))],
    //   [chinaJson.list.slice(0, 14000).concat(japanJson.list.slice(0, 800)).concat(usaJson.list.slice(0, 4000)).concat(englandJson.list.slice(0, 600)).concat(germanyJson.list.slice(0, 600))],
    //   [chinaJson.list.slice(0, 14000).concat(japanJson.list.slice(0, 800)).concat(usaJson.list.slice(0, 8000)).concat(englandJson.list.slice(0, 600)).concat(germanyJson.list.slice(0, 600))],
    //   [chinaJson.list.slice(0, 14000).concat(japanJson.list.slice(0, 800)).concat(usaJson.list.slice(0, 8000)).concat(englandJson.list.slice(0, 1200)).concat(germanyJson.list.slice(0, 600))],
    //   [chinaJson.list.slice(0, 14000).concat(japanJson.list.slice(0, 800)).concat(usaJson.list.slice(0, 8000)).concat(englandJson.list.slice(0, 1200)).concat(germanyJson.list.slice(0, 1200))],
    //   [chinaJson.list.slice(0, 21000).concat(japanJson.list.slice(0, 800)).concat(usaJson.list.slice(0, 8000)).concat(englandJson.list.slice(0, 1200)).concat(germanyJson.list.slice(0, 1200))],
    //   [chinaJson.list.slice(0, 21000).concat(japanJson.list.slice(0, 1200)).concat(usaJson.list.slice(0, 8000)).concat(englandJson.list.slice(0, 1200)).concat(germanyJson.list.slice(0, 1200))],
    //   [chinaJson.list.slice(0, 21000).concat(japanJson.list.slice(0, 1200)).concat(usaJson.list.slice(0, 12000)).concat(englandJson.list.slice(0, 1200)).concat(germanyJson.list.slice(0, 1200))],
    //   [chinaJson.list.slice(0, 21000).concat(japanJson.list.slice(0, 1200)).concat(usaJson.list.slice(0, 12000)).concat(englandJson.list.slice(0, 1800)).concat(germanyJson.list.slice(0, 1200))],
    //   [chinaJson.list.slice(0, 21000).concat(japanJson.list.slice(0, 1200)).concat(usaJson.list.slice(0, 12000)).concat(englandJson.list.slice(0, 1800)).concat(germanyJson.list.slice(0, 1800))],
    //   [chinaJson.list.slice(0, 28000).concat(japanJson.list.slice(0, 1200)).concat(usaJson.list.slice(0, 12000)).concat(englandJson.list.slice(0, 1800)).concat(germanyJson.list.slice(0, 1800))],
    //   [chinaJson.list.slice(0, 28000).concat(japanJson.list.slice(0, 1600)).concat(usaJson.list.slice(0, 12000)).concat(englandJson.list.slice(0, 1800)).concat(germanyJson.list.slice(0, 1800))],
    //   [chinaJson.list.slice(0, 28000).concat(japanJson.list.slice(0, 1600)).concat(usaJson.list.slice(0, 16000)).concat(englandJson.list.slice(0, 1800)).concat(germanyJson.list.slice(0, 1800))],
    //   [chinaJson.list.slice(0, 28000).concat(japanJson.list.slice(0, 1600)).concat(usaJson.list.slice(0, 16000)).concat(englandJson.list.slice(0, 2400)).concat(germanyJson.list.slice(0, 1800))],
    //   [chinaJson.list.slice(0, 28000).concat(japanJson.list.slice(0, 1600)).concat(usaJson.list.slice(0, 16000)).concat(englandJson.list.slice(0, 2400)).concat(germanyJson.list.slice(0, 2400))],
    //   [chinaJson.list.concat(japanJson.list.slice(0, 1600)).concat(usaJson.list.slice(0, 16000)).concat(englandJson.list.slice(0, 2400)).concat(germanyJson.list.slice(0, 2400))],
    //   [chinaJson.list.concat(japanJson.list).concat(usaJson.list.slice(0, 16000)).concat(englandJson.list.slice(0, 2400)).concat(germanyJson.list.slice(0, 2400))],
    //   [chinaJson.list.concat(japanJson.list).concat(usaJson.list).concat(englandJson.list.slice(0, 2400)).concat(germanyJson.list.slice(0, 2400))],
    //   [chinaJson.list.concat(japanJson.list).concat(usaJson.list).concat(englandJson.list).concat(germanyJson.list.slice(0, 2400))],
    //   [chinaJson.list.concat(japanJson.list).concat(usaJson.list).concat(englandJson.list).concat(germanyJson.list)],
    // )
    let listArr3D = [];
    listArr3D.push(
      [d3JsonChina.list.slice(0, 25600).concat(d3Json.list.slice(0, 54000))],
      [d3JsonChina.list.concat(d3Json.list)]
    );
    // listArr3D.push(
    //   [chinaJson3d.list.slice(0, 3500)],
    //   [chinaJson3d.list.slice(0, 3500).concat(japanJson3d.list.slice(0, 200))],
    //   [chinaJson3d.list.slice(0, 3500).concat(japanJson3d.list.slice(0, 200)).concat(usaJson3d.list.slice(0, 2000))],
    //   [chinaJson3d.list.slice(0, 3500).concat(japanJson3d.list.slice(0, 200)).concat(usaJson3d.list.slice(0, 2000)).concat(englandJson3d.list.slice(0, 300))],
    //   [chinaJson3d.list.slice(0, 3500).concat(japanJson3d.list.slice(0, 200)).concat(usaJson3d.list.slice(0, 2000)).concat(englandJson3d.list.slice(0, 300)).concat(germanyJson3d.list.slice(0, 200))],
    //   [chinaJson3d.list.slice(0, 7000).concat(japanJson3d.list.slice(0, 200)).concat(usaJson3d.list.slice(0, 2000)).concat(englandJson3d.list.slice(0, 300)).concat(germanyJson3d.list.slice(0, 200))],
    //   [chinaJson3d.list.slice(0, 7000).concat(japanJson3d.list.slice(0, 400)).concat(usaJson3d.list.slice(0, 2000)).concat(englandJson3d.list.slice(0, 300)).concat(germanyJson3d.list.slice(0, 200))],
    //   [chinaJson3d.list.slice(0, 7000).concat(japanJson3d.list.slice(0, 400)).concat(usaJson3d.list.slice(0, 4000)).concat(englandJson3d.list.slice(0, 300)).concat(germanyJson3d.list.slice(0, 200))],
    //   [chinaJson3d.list.slice(0, 7000).concat(japanJson3d.list.slice(0, 400)).concat(usaJson3d.list.slice(0, 4000)).concat(englandJson3d.list.slice(0, 600)).concat(germanyJson3d.list.slice(0, 200))],
    //   [chinaJson3d.list.slice(0, 7000).concat(japanJson3d.list.slice(0, 400)).concat(usaJson3d.list.slice(0, 4000)).concat(englandJson3d.list.slice(0, 600)).concat(germanyJson3d.list.slice(0, 400))],
    //   [chinaJson3d.list.slice(0, 10500).concat(japanJson3d.list.slice(0, 400)).concat(usaJson3d.list.slice(0, 4000)).concat(englandJson3d.list.slice(0, 600)).concat(germanyJson3d.list.slice(0, 400))],
    //   [chinaJson3d.list.slice(0, 10500).concat(japanJson3d.list.slice(0, 600)).concat(usaJson3d.list.slice(0, 4000)).concat(englandJson3d.list.slice(0, 600)).concat(germanyJson3d.list.slice(0, 400))],
    //   [chinaJson3d.list.slice(0, 10500).concat(japanJson3d.list.slice(0, 600)).concat(usaJson3d.list.slice(0, 6000)).concat(englandJson3d.list.slice(0, 600)).concat(germanyJson3d.list.slice(0, 400))],
    //   [chinaJson3d.list.slice(0, 10500).concat(japanJson3d.list.slice(0, 600)).concat(usaJson3d.list.slice(0, 6000)).concat(englandJson3d.list.slice(0, 900)).concat(germanyJson3d.list.slice(0, 400))],
    //   [chinaJson3d.list.slice(0, 10500).concat(japanJson3d.list.slice(0, 600)).concat(usaJson3d.list.slice(0, 6000)).concat(englandJson3d.list.slice(0, 900)).concat(germanyJson3d.list.slice(0, 600))],
    //   [chinaJson3d.list.slice(0, 14000).concat(japanJson3d.list.slice(0, 600)).concat(usaJson3d.list.slice(0, 6000)).concat(englandJson3d.list.slice(0, 900)).concat(germanyJson3d.list.slice(0, 600))],
    //   [chinaJson3d.list.slice(0, 14000).concat(japanJson3d.list.slice(0, 800)).concat(usaJson3d.list.slice(0, 6000)).concat(englandJson3d.list.slice(0, 900)).concat(germanyJson3d.list.slice(0, 600))],
    //   [chinaJson3d.list.slice(0, 14000).concat(japanJson3d.list.slice(0, 800)).concat(usaJson3d.list.slice(0, 8000)).concat(englandJson3d.list.slice(0, 900)).concat(germanyJson3d.list.slice(0, 600))],
    //   [chinaJson3d.list.slice(0, 14000).concat(japanJson3d.list.slice(0, 800)).concat(usaJson3d.list.slice(0, 8000)).concat(englandJson3d.list.slice(0, 1200)).concat(germanyJson3d.list.slice(0, 600))],
    //   [chinaJson3d.list.slice(0, 14000).concat(japanJson3d.list.slice(0, 800)).concat(usaJson3d.list.slice(0, 8000)).concat(englandJson3d.list.slice(0, 1200)).concat(germanyJson3d.list.slice(0, 800))],
    //   [chinaJson3d.list.concat(japanJson3d.list.slice(0, 800)).concat(usaJson3d.list.slice(0, 8000)).concat(englandJson3d.list.slice(0, 1200)).concat(germanyJson3d.list.slice(0, 800))],
    //   [chinaJson3d.list.concat(japanJson3d.list).concat(usaJson3d.list.slice(0, 8000)).concat(englandJson3d.list.slice(0, 1200)).concat(germanyJson3d.list.slice(0, 800))],
    //   [chinaJson3d.list.concat(japanJson3d.list).concat(usaJson3d.list).concat(englandJson3d.list.slice(0, 1200)).concat(germanyJson3d.list.slice(0, 800))],
    //   [chinaJson3d.list.concat(japanJson3d.list).concat(usaJson3d.list).concat(englandJson3d.list).concat(germanyJson3d.list.slice(0, 800))],
    //   [chinaJson3d.list.concat(japanJson3d.list).concat(usaJson3d.list).concat(englandJson3d.list).concat(germanyJson3d.list)],
    // )
    this.listArr = listArr;
    this.listArr3D = listArr3D;
  },
  methods: {
    dxsonIndexFun(index) {
      if (this.dxsonIndex != index) {
        this.dxsonIndex = index;
      }
    },
    indexClickFun() {
      this.aniEndFun();
    },
    indexClickFun2() {
      this.changePage(0);
    },
    open2d() {
      this.show2dMap = true;
    },
    initMapDom() {
      // 内存泄漏 无dom 不执行
      // 查找内存泄漏
      // if (!this.$refs.earth || !this.$refs.map) { return false }
      this.chartsEarth = echarts.init(this.$refs.earth, null, {
        width: this.GLOBAL.relPx(700),
        height: this.GLOBAL.relPx(700),
      });
      this.GLOBAL.echartsDomArray.push(this.chartsEarth);
      this.chartsMap = echarts.init(this.$refs.map, null, {
        width: this.GLOBAL.relPx(1400),
        height: this.GLOBAL.relPx(700),
      });
      this.GLOBAL.echartsDomArray.push(this.chartsMap);
      echarts.registerMap("world", { geoJSON: worldMap });
      this.initEarth();
      this.initMap();
      let setClear2 = setTimeout(() => {
        this.addPointAll();
        this.posiAniFun();
        this.posiAniFunTimer();
      }, 2000);
      this.GLOBAL.timerArrayOut.push(setClear2);
    },
    posiAniFunTimer() {
      this.setIntClear1 = setInterval(() => {
        this.posiAniFun();
      }, 15000);
      this.GLOBAL.timerArraySet.push(this.setIntClear1);
    },
    posiAniFun() {
      let _this = this;
      this.posiIndex = 0;
      let setClear3 = setTimeout(() => {
        this.posiIndex = 1;
      }, 3000);
      this.GLOBAL.timerArrayOut.push(setClear3);
      let setClear4 = setTimeout(() => {
        this.posiIndex = 2;
      }, 5000);
      this.GLOBAL.timerArrayOut.push(setClear4);
      let setClear5 = setTimeout(() => {
        this.posiIndex = 3;
      }, 8000);
      this.GLOBAL.timerArrayOut.push(setClear5);
      let setClear6 = setTimeout(() => {
        this.posiIndex = 4;
        // clearInterval(_this.setIntClear1)
      }, 12500);
      this.GLOBAL.timerArrayOut.push(setClear6);
    },
    aniEndFun() {
      this.showLeftCore = false;
      clearInterval(this.setClear13);
      let setClear7 = setTimeout(() => {
        let setIntClear2 = setInterval(() => {
          this.radarIndex += 1;
          if (this.radarIndex > 2) {
            this.radarIndex = 0;
          }
        }, 1500);
        this.GLOBAL.timerArraySet.push(setIntClear2);
      }, 1000);
      this.GLOBAL.timerArrayOut.push(setClear7);
      let setClear8 = setTimeout(() => {
        this.ecBinFun();
        this.ecBarFun();
        this.ecLineFun();
      }, 1200);
      this.GLOBAL.timerArrayOut.push(setClear8);
    },
    ecLineFun() {
      let option = {
        textStyle: {
          fontFamily: "Microsoft YaHei",
        },
        label: {
          show: true, // 在折线拐点上显示数据
        },
        legend: {
          show: true,
          right: "5%",
          textStyle: {
            color: "#ffffff",
          },
        },
        grid: {
          bottom: "15%",
          right: "2%",
          top: "15%",
          left: "2%",
        },
        xAxis: {
          type: "category",
          data: [
            "2007",
            "2011",
            "2014",
            "2018",
            "2019",
            "2020",
            "2021",
            "2022",
            "2023",
            "2024",
            "2025",
          ],
          splitLine: {
            lineStyle: {
              opacity: 0.4,
            },
          },
          axisLabel: {
            textStyle: {
              color: "#f5f5f5",
            },
          },
        },
        yAxis: {
          type: "value",
          splitNumber: 3,
          axisLabel: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              type: "dashed",
              opacity: 0.2,
            },
          },

          min: 0,
          max: 2.6,
        },
        series: [
          {
            name: "DCIE",
            data: [0.4, 0.51, 0.61, 0.63, 0.6, 0.63, 0.64, 0.71, "-", "-", "-"],
            type: "line",
            symbol: "triangle",
            symbolSize: this.GLOBAL.relPx(12),
            lineStyle: {
              color: "#FF9500",
              width: 1,
              type: "solid",
            },
            itemStyle: {
              color: "#FF9500",
            },
            label: {
              show: true,
              position: "bottom",
              formatter: "{c}",
              textStyle: {
                color: "#ffffff",
              },
            },
          },
          {
            name: "DCIE",
            data: ["-", "-", "-", "-", "-", "-", "-", 0.71, 0.77, 0.83, 0.91],
            type: "line",
            symbol: "triangle",
            symbolSize: this.GLOBAL.relPx(12),
            lineStyle: {
              color: "#FF9500",
              width: 1,
              type: "dashed",
            },
            itemStyle: {
              color: "#FF9500",
            },
            label: {
              show: true,
              position: "bottom",
              formatter: "{c}",
              textStyle: {
                color: "#ffffff",
              },
            },
          },
          {
            name: "PUE",
            data: [2.5, 1.98, 1.65, 1.58, 1.67, 1.59, 1.57, 1.4, "-", "-", "-"],
            type: "line",
            symbol: "triangle",
            symbolSize: this.GLOBAL.relPx(12),
            lineStyle: {
              color: "#00E4FF",
              width: 1,
              type: "solid",
            },
            itemStyle: {
              color: "#00E4FF",
            },
            label: {
              show: true,
              position: "top",
              formatter: "{c}",
              textStyle: {
                color: "#ffffff",
              },
            },
          },
          {
            name: "PUE",
            data: ["-", "-", "-", "-", "-", "-", "-", 1.4, 1.3, 1.2, 1.1],
            type: "line",
            symbol: "triangle",
            symbolSize: this.GLOBAL.relPx(12),
            lineStyle: {
              color: "#00E4FF",
              width: 1,
              type: "dashed",
            },
            itemStyle: {
              color: "#00E4FF",
            },
            label: {
              show: true,
              position: "top",
              formatter: "{c}",
              textStyle: {
                color: "#ffffff",
              },
            },
          },
        ],
      };
      // 内存泄漏 无dom 不执行
      // 查找内存泄漏
      // if (!this.$refs.line) { return false }
      this.myChartLine = echarts.init(this.$refs.line, null, {
        width: this.GLOBAL.relPx(900),
        height: this.GLOBAL.relPx(200),
      });
      this.GLOBAL.echartsDomArray.push(this.myChartLine);
      this.myChartLine.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.myChartLine.setOption(option);
    },
    ecBinFun() {
      let _this = this;
      let option = {
        textStyle: {
          fontFamily: "Microsoft YaHei",
        },
        backgroundColor: "",
        grid: {
          bottom: "5%",
          right: "5%",
          top: "5%",
          left: "5%",
        },
        series: [
          {
            name: "Access From",
            type: "pie",
            radius: "100%",
            center: ["50%", "50%"],
            startAngle: 150,
            data: [
              { value: 28, name: "中国", name2: "33%" },
              { value: 23, name: "欧洲", name2: "17%" },
              { value: 10, name: "日本", name2: "4%" },
              { value: 18, name: "其他", name2: "12%" },
              { value: 30, name: "美国", name2: "34%" },
            ],
            roseType: "radius",
            label: {
              color: "rgba(255, 255, 255, 1)",
              fontSize: 14,
              position: "inside",
              // distance: 50,
              offset: [0, 0],
              formatter: (params) => {
                return `${params.name}\n${params.data.name2}`;
              },
              fontFamily: "Microsoft YaHei",
            },
            labelLine: {
              show: false,
              lineStyle: {
                color: "rgba(255, 255, 255, 1)",
              },
              smooth: 0.2,
              length: 10,
              length2: 20,
            },
            itemStyle: {
              // color: ['#46C7E7','#063239', '#EA9919', '#3BCCA5'],
              color: function (colors) {
                var colorList = [
                  "rgba(70, 199, 231, 0.8)",
                  "rgba(76,50,57, 0.8)",
                  "rgba(6,150,57, 0.8)",
                  "rgba(234,153,25, 0.8)",
                  "rgba(59, 204, 165,0.8)",
                ];
                return colorList[colors.dataIndex];
              },
              shadowBlur: 200,
              opacity: 0.8,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
            animationType: "scale",
            animationEasing: "elasticOut",
            animationDelay: function (idx) {
              return Math.random() * 200;
            },
          },
        ],
      };
      // 内存泄漏 无dom 不执行
      if (!this.$refs.bin) {
        return false;
      }
      this.myChartBin = echarts.init(this.$refs.bin, null, {
        width: this.GLOBAL.relPx(200),
        height: this.GLOBAL.relPx(165),
      });
      this.GLOBAL.echartsDomArray.push(this.myChartBin);
      this.myChartBin.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.myChartBin.setOption(option);
      this.myChartBin.off("click");
      this.myChartBin.on("click", function (params) {
        if (params.name == "中国") {
          _this.changePage(8);
        }
      });
    },
    ecBarFun() {
      let option = {
        textStyle: {
          fontFamily: "Microsoft YaHei",
        },
        backgroundColor: "",
        color: ["#3cefff"],
        grid: {
          bottom: "10%",
          right: "2%",
          top: "15%",
          left: "5%",
        },
        legend: {
          show: true,
          top: 0,
          right: "5%",
          textStyle: {
            color: "#cccccc",
            fontSize: 12,
          },
        },
        padding: 0,
        xAxis: [
          {
            type: "category",
            data: [
              "美国",
              "中国",
              "日本",
              "德国",
              "英国",
              "加拿大",
              "法国",
              "韩国",
              "印度",
              "意大利",
            ],
            axisTick: {
              alignWithLabel: true,
            },
            nameTextStyle: {
              color: "#A9A9AA",
            },
            axisLine: {
              lineStyle: {
                color: "#A9A9AA",
              },
            },
            axisLabel: {
              textStyle: {
                color: "#f5f5f5",
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            splitNumber: 3,
            axisLabel: {
              textStyle: {
                color: "#f5f5f5",
              },

              formatter: "{value}",
            },
            splitLine: {
              lineStyle: {
                type: "dashed",
                opacity: 0.2,
              },
            },
            axisLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "算力(EFlops)",
            color: ["#3cefff"],
            type: "pictorialBar",
            symbolSize: [12, 6],
            symbolOffset: [0, -3],
            symbolPosition: "end",
            z: 12,
            label: {
              show: false,
              position: "top",
              formatter: "{c}",
              textStyle: {
                color: "#ccc",
              },
            },
            data: [320, 300, 40, 36, 30, 26, 22, 20, 18, 12],
          },
          {
            name: "算力(EFlops)",
            color: ["#3cefff"],
            type: "pictorialBar",
            symbolSize: [12, 6],
            symbolOffset: [0, 3],
            z: 12,
            data: [320, 300, 40, 36, 30, 26, 22, 20, 18, 12],
          },
          {
            name: "算力(EFlops)",
            type: "bar",
            color: ["#3cefff"],
            itemStyle: {
              opacity: 0.7,
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(0,214,223,0)",
                  },
                  {
                    offset: 1,
                    color: "rgba(0,214,223,0.7)",
                  },
                ]),
                shadowBlur: 10,
                shadowColor: "#333",
              },
            },
            barWidth: "12",
            data: [320, 300, 40, 36, 30, 26, 22, 20, 18, 12],
          },
        ],
      };
      // 内存泄漏 无dom 不执行
      if (!this.$refs.bar) {
        return false;
      }
      this.myChartbar = echarts.init(this.$refs.bar, null, {
        width: this.GLOBAL.relPx(580),
        height: this.GLOBAL.relPx(200),
      });
      this.GLOBAL.echartsDomArray.push(this.myChartbar);
      this.myChartbar.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.myChartbar.setOption(option);
    },
    addPointAll() {
      // 内存泄漏
      // 查找内存泄漏
      if (!this.chartsEarth) {
        return false;
      }
      let i = 0;
      let setIntClear3 = setInterval(() => {
        i += 1;
        // this.eflopsPercent = i
        if (i >= 15) {
          clearInterval(setIntClear3);
          let setClear9 = setTimeout(() => {
            this.aniEndFun();
          }, 100);
          this.GLOBAL.timerArrayOut.push(setClear9);
        }
      }, 1000);
      this.GLOBAL.timerArraySet.push(setIntClear3);

      let _this = this;
      let option = this.chartsEarth.getOption();
      // 内存泄漏
      // 查找内存泄漏
      // if (!option || !option.globe || option.globe.length === 0) {
      //   return false
      // }
      option.globe[0].viewControl.autoRotate = true;
      this.chartsEarth.setOption(option);
      let setClear10 = setTimeout(() => {
        // 循环25次 appendData 导致自转变卡顿
        // for (let i = 14; i < 25; i++) {
        //   setTimeout(() => {
        //     _this.chartsMap.appendData({
        //       seriesIndex: 0,
        //       data: Float32Array.from(this.listArr[i][0])
        //     });
        //     _this.chartsEarth.appendData({
        //       seriesIndex: 0,
        //       data: this.listArr3D[i][0]
        //     });
        //   }, 3000 * (i - 14));
        // }
        // 卡顿-只加一次
        // for (let i = 0; i < 2; i++) {
        //   let setClear11 = setTimeout(() => {
        //     _this.chartsMap.appendData({
        //       seriesIndex: 0,
        //       data: Float32Array.from(this.listArr[i][0])
        //     });
        //     _this.chartsEarth.appendData({
        //       seriesIndex: 0,
        //       data: this.listArr3D[i][0]
        //     });
        //   }, 15000 * i);
        //   this.GLOBAL.timerArrayOut.push(setClear11)
        // }
        _this.chartsMap.appendData({
          seriesIndex: 0,
          data: Float32Array.from(this.listArr[1][0]),
        });
        _this.chartsEarth.appendData({
          seriesIndex: 0,
          data: this.listArr3D[1][0],
        });
        for (let i = 5; i < 11; i++) {
          let time = 0;
          if (i == 0) {
            time = 0;
          } else if (i == 1) {
            time = 2000;
          } else if (i == 2) {
            time = 2700;
          } else if (i == 3) {
            time = 4200;
          } else if (i == 4) {
            time = 6500;
          } else if (i == 5) {
            time = 0;
          } else if (i == 6) {
            time = 3000;
          } else if (i == 7) {
            time = 5000;
          } else if (i == 8) {
            time = 8000;
          } else if (i == 9) {
            time = 13000;
          } else if (i == 10) {
            time = 15000;
          }
          let setClear12 = setTimeout(() => {
            _this.leftWordAniFun(i);
          }, time);
          this.GLOBAL.timerArrayOut.push(setClear12);
        }
        this.lineAni = true;
      }, 100);
      this.GLOBAL.timerArrayOut.push(setClear10);
    },
    leftWordAniFun(index) {
      let arr = [
        { eflops: 46, eflopsPercent: 10, name: "CHINA", cname: "中国" },
        { eflops: 31, eflopsPercent: 21, name: "GERMANY", cname: "德国" },
        { eflops: 20, eflopsPercent: 12, name: "ENGLAND", cname: "英国" },
        { eflops: 48, eflopsPercent: 5, name: "USA", cname: "美国" },
        { eflops: 15, eflopsPercent: 6, name: "JAPAN", cname: "日本" },
        { eflops: 71, eflopsPercent: 2, name: "CHINA", cname: "中国" },
        { eflops: 56, eflopsPercent: 4, name: "GERMANY", cname: "德国" },
        { eflops: 53, eflopsPercent: 6, name: "ENGLAND", cname: "英国" },
        { eflops: 82, eflopsPercent: 1, name: "USA", cname: "美国" },
        { eflops: 58, eflopsPercent: 3, name: "JAPAN", cname: "日本" },
        { eflops: 71, eflopsPercent: 2, name: "CHINA", cname: "中国" },
      ];
      this.eflops = arr[index].eflops;
      this.eflopsPercent = arr[index].eflopsPercent;
      this.eflopsName = arr[index].cname;
      this.leftWordAni = false;
      this.setClear13 = setTimeout(() => {
        this.leftWordAni = true;
      }, 100);
      this.GLOBAL.timerArrayOut.push(this.setClear13);
    },
    initMap() {
      let option = {
        backgroundColor: "",
        title: {
          left: "center",
          textStyle: {
            color: "#fff",
          },
        },
        geo: {
          map: "world",
          roam: false,
          zoom: 1.2,
          label: {
            emphasis: {
              show: false,
            },
          },
          silent: true,
          itemStyle: {
            areaColor: "rgba(51, 51, 51, 0.9)",
            borderColor: "#111",
            emphasis: {
              areaColor: "#2a333d",
            },
          },
        },
        series: [
          {
            name: "弱",
            type: "scatterGL",
            progressive: 1e6,
            coordinateSystem: "geo",
            symbolSize: 1,
            // zoomScale: 0.002,
            // blendMode: 'lighter',
            // large: true,
            itemStyle: {
              color: "#ffdb60",
              // opacity: 0.2
            },
            // postEffect: {
            //   enable: true
            // },
            silent: true,
            dimensions: ["lng", "lat"],
            data: new Float32Array(),
          },
        ],
      };
      this.chartsMap.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.chartsMap.setOption(option);
    },
    initEarth() {
      // 使用 echarts 绘制世界地图的实例作为纹理
      var canvas = document.createElement("canvas");
      // 内存泄漏 无dom 不执行
      // 查找内存泄漏
      // if (!canvas) { return false }
      this.texture = echarts.init(canvas, null, {
        width: this.GLOBAL.relPx(4096),
        height: this.GLOBAL.relPx(2048),
      });
      this.GLOBAL.echartsDomArray.push(this.texture);
      this.texture.setOption({
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        series: [
          {
            type: "map",
            map: "world",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            silent: true, //图形是否不响应和触发鼠标事件
            boundingCoords: [
              [-180, 90],
              [180, -90],
            ],
            label: {
              show: true,
              color: "#ffffff",
              fontSize: 32,
              fontFamily: "Microsoft YaHei",
              // textBorderColor: '#f00000',
              // textBorderWidth: 5,
              // fontWeight: 'bold'
            },
            emphasis: {
              itemStyle: {
                color: "#2038cc",
              },
            },
            itemStyle: {
              areaColor: "rgba(51, 51, 51, 0.9)",
              borderWidth: 1.5,
              borderColor: "#111",
              emphasis: {
                color: "#ff0000",
                areaColor: "#2a333d",
              },
            },
          },
        ],
      });

      var option = {
        globe: {
          show: true,
          baseTexture: this.texture,
          shading: "color",
          atmosphere: {
            show: true,
            glowPower: 4,
            innerGlowPower: 2,
          },
          //视角控制
          viewControl: {
            autoRotate: false, //自动旋转
            autoRotateDirection: "ccw", // 逆时针
            autoRotateSpeed: 24,
            autoRotateAfterStill: 0.1, //鼠标停止后多久恢复旋转(为0时暂停后不恢复旋转)
            damping: 0,
            // projection: 'orthographic',
            rotateSensitivity: 0, //鼠标旋转灵敏度，设置为0后无法旋转。
            zoomSensitivity: 0, //鼠标缩放灵敏度
            alpha: 0, //视角绕 x 轴，即上下旋转的角度
            beta: 180, //视角绕 y 轴，即左右旋转的角度。
            center: [0, 0, 0],
            targetCoord: [116.46, 39.92], //定位到哪里
          },
        },
        series: [
          {
            name: "弱",
            type: "scatter3D",
            coordinateSystem: "globe",
            // symbol: 'none',
            symbolSize: 1,
            silent: true,
            itemStyle: {
              color: "#ffdb60",
              // opacity: 0.2
            },
            // progressive: 1e6,
            // zoomScale: 0.002,
            // blendMode: 'lighter',
            // large: true,
            // postEffect: {
            //   enable: true
            // },
            // silent: true,
            // dimensions: ['lng', 'lat'],
            data: [],
          },
        ],
      };
      this.chartsEarth.clear();
      this.chartsEarth.setOption(option);
    },
    resetFun() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      if (this.radarIndexTimer) {
        clearInterval(this.radarIndexTimer);
      }
      if (this.posiTimer) {
        clearInterval(this.posiTimer);
      }
      if (this.chartsEarth) {
        this.chartsEarth.dispose();
      }
      if (this.chartsMap) {
        this.chartsMap.dispose();
      }
      if (this.myChartLine) {
        this.myChartLine.dispose();
      }
      if (this.myChartBin) {
        this.myChartBin.dispose();
      }
      if (this.myChartbar) {
        this.myChartbar.dispose();
      }
      if (this.texture) {
        this.texture.dispose();
      }
      if (this.listArr) {
        this.listArr = [];
      }
      if (this.listArr3D) {
        this.listArr3D = [];
      }
    },

    // page8 *********
    changePage(val) {
      if (val == 8) {
        clearInterval(this.ecNew_1ZoomTimer);
        clearInterval(this.ecNew_2ZoomTimer);
        clearInterval(this.ecNew_3ZoomTimer);
        clearInterval(this.ecNew_4ZoomTimer);
        clearInterval(this.ec2ZoomTimer);
        this.showPage8 = true;
        // page8
        this.initMap_8();
        this.ec2Fun();
        // this.ec4Fun()
        // this.ec5Fun()
        // this.ec6Fun()
        this.ecNewInit();
      } else {
        this.showPage8 = false;
      }
    },
    centerPosiCoreShowFun() {
      this.centerPosiCoreShow = !this.centerPosiCoreShow;
    },
    leftTabIndexFun(index) {
      if (this.rowIndex && this.sonIndex) {
        return false;
      }
      if (this.leftTabIndex != index) {
        this.leftTabIndex = index;
      }
    },
    showYsFun() {
      this.showYs = !this.showYs;
      this.rowIndex = null;
      this.sonIndex = null;
      this.nameShow = false;
    },
    slgmFun() {
      this.slgmShow = !this.slgmShow;
      // this.nameShow = false
    },
    nameShowFun() {
      this.nameShow = !this.nameShow;
      this.rowIndex = null;
      this.sonIndex = null;
    },
    groupFun(row, son) {
      this.dxsonIndex = null;
      if (this.leftTabIndex == 2) {
        return false;
      }
      if (row == this.rowIndex2 && son == this.sonIndex2) {
        this.rowIndex2 = null;
        this.sonIndex2 = null;
      } else {
        this.rowIndex2 = row;
        this.sonIndex2 = son;
      }
      // this.showYs = false
      // this.nameShow = false
    },
    groupFunRight(row, son) {
      if (row == this.rowIndex && son == this.sonIndex) {
        this.rowIndex = null;
        this.sonIndex = null;
      } else {
        this.rowIndex = row;
        this.sonIndex = son;
      }
      this.showYs = false;
      this.nameShow = false;
    },
    tabMiniIndexClick(index1, index2) {
      if (index1 === 1) {
        if (this.tabMiniIndex1 != index2) {
          clearInterval(this.ecNew_1ZoomTimer);
          this.tabMiniIndex1 = index2;
          this.ecNew_1Fun();
        }
      }
      if (index1 === 2) {
        clearInterval(this.ecNew_2ZoomTimer);
        this.tabMiniIndex2 = index2;
        this.ecNew_2Fun();
      }
      if (index1 === 3) {
        clearInterval(this.ecNew_3ZoomTimer);
        this.tabMiniIndex3 = index2;
        this.ecNew_3Fun();
      }
      if (index1 === 4) {
        clearInterval(this.ecNew_4ZoomTimer);
        this.tabMiniIndex4 = index2;
        this.ecNew_4Fun();
      }
    },
    rightEchartsShowFun() {
      this.rightEchartsShow = !this.rightEchartsShow;
      this.showRightEcharts = true;
      clearInterval(this.ecNew_1ZoomTimer);
      clearInterval(this.ecNew_2ZoomTimer);
      clearInterval(this.ecNew_3ZoomTimer);
      clearInterval(this.ecNew_4ZoomTimer);
      if (this.rightEchartsShow) {
        this.tabMiniIndex1 = 1;
        this.tabMiniIndex2 = 1;
        this.tabMiniIndex3 = 1;
        this.tabMiniIndex4 = 1;
        this.ecNewInit();
      }
    },
    rightEchartsShowFun2() {
      this.showRightEcharts = true;
    },
    ecNewInit() {
      this.ecNew_1Fun();
      this.ecNew_2Fun();
      this.ecNew_3Fun();
      this.ecNew_4Fun();
    },
    ecNew_4Fun() {
      // mock数据
      const dataArr = {
        hjxdata1: [
          "内蒙",
          "宁夏",
          "上海",
          "山东",
          "河北",
          "北京",
          "黑龙江",
          "甘肃",
          "江苏",
          "四川",
        ],
        hjydata1: [
          70.46, 68.19, 67.46, 60.31, 58.07, 56.86, 55.28, 54.82, 54.56, 54.3,
        ],
        hjxdata2: [
          "宁夏",
          "内蒙",
          "青海",
          "黑龙江",
          "甘肃",
          "上海",
          "吉林",
          "北京",
          "河北",
          "山东",
        ],
        hjydata2: [91.3, 90.0, 73.8, 73.7, 71.8, 68.8, 67.7, 64.7, 63.2, 63.2],
        hjxdata3: [
          "上海",
          "广东",
          "山东",
          "江苏",
          "河北",
          "浙江",
          "北京",
          "安徽",
          "河南",
          "四川",
        ],
        hjydata3: [66.6, 59.5, 55.0, 52.8, 47.4, 42.3, 41.1, 40.8, 37.4, 34.5],
      };
      let mockData = [];
      let mockDataCity = [];
      if (this.tabMiniIndex4 === 1) {
        mockDataCity = dataArr.hjxdata1;
        mockData = dataArr.hjydata1;
      } else if (this.tabMiniIndex4 === 2) {
        mockDataCity = dataArr.hjxdata2;
        mockData = dataArr.hjydata2;
      } else if (this.tabMiniIndex4 === 3) {
        mockDataCity = dataArr.hjxdata3;
        mockData = dataArr.hjydata3;
      }

      // tooltip
      const option = {
        xAxis: {
          type: "category",
          boundaryGap: false,
          axisTick: { show: true },
          axisLine: { lineStyle: { color: "rgba(255,255,255, .2)" } },
          axisLabel: {
            textStyle: { fontSize: this.GLOBAL.relPx(12), color: "#f5f5f5" },
          },
          data: mockDataCity,
        },
        yAxis: {
          type: "value",
          splitNumber: 4,
          axisTick: { show: false },
          axisLine: { show: false },
          splitLine: { lineStyle: { color: "rgba(255,255,255, .05)" } },
          axisLabel: {
            textStyle: { fontSize: this.GLOBAL.relPx(12), color: "#f5f5f5" },
          },
        },
        grid: { top: "20", left: "30", right: "20", bottom: "40" },
        series: [
          {
            data: mockData,
            type: "line",
            symbol: "triangle",
            symbolSize: this.GLOBAL.relPx(14),
            itemStyle: {
              normal: {
                color: "rgba(255,102,51,0.5)",
              },
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(255,102,51,0.2)",
                  },
                  {
                    offset: 1,
                    color: "rgba(255,102,51,0.05)",
                  },
                ]),
              },
            },
          },
        ],
        dataZoom: [
          {
            // 第一个 dataZoom 组件
            type: "inside",
            xAxisIndex: 0, // 表示这个 dataZoom 组件控制 第一个 xAxis
            startValue: 0, // 数据窗口范围的起始数值index
            endValue: 7, // 数据窗口范围的结束数值index
          },
        ],
      };

      // 内存泄漏 无dom 不执行
      if (!this.myChartecNew_4) {
        this.myChartecNew_4 = echarts.init(this.$refs.ecNew_4, null, {
          width: this.GLOBAL.relPx(450),
          height: this.GLOBAL.relPx(300),
        });
      }
      this.GLOBAL.echartsDomArray.push(this.myChartecNew_4);
      this.myChartecNew_4.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.myChartecNew_4.setOption(option);
      this.ecNew_4ZoomTimer = setInterval(() => {
        if (option.dataZoom[0].endValue == mockData.length - 1) {
          option.dataZoom[0].endValue = 7;
          option.dataZoom[0].startValue = 0;
        } else {
          option.dataZoom[0].endValue = option.dataZoom[0].endValue + 1;
          option.dataZoom[0].startValue = option.dataZoom[0].startValue + 1;
        }
        this.myChartecNew_4.setOption(option);
      }, 2000);
      this.GLOBAL.timerArraySet.push(this.ecNew_4ZoomTimer);
    },
    ecNew_3Fun() {
      // mock数据
      const dataArr = {
        ylxdata1: [
          "上海",
          "广东",
          "江苏",
          "浙江",
          "四川",
          "山东",
          "北京",
          "天津",
          "河北",
          "河南",
        ],
        ylydata1: [
          68.02, 66.26, 65.82, 65.12, 64.16, 64.13, 64.84, 62.73, 62.76, 61.37,
        ],
        ylxdata2: [
          "广东",
          "上海",
          "江苏",
          "浙江",
          "四川",
          "山东",
          "北京",
          "安徽",
          "天津",
          "河北",
        ],
        ylydata2: [
          64.69, 62.91, 57.63, 56.89, 53.78, 45.14, 35.96, 34.46, 33.14, 32.56,
        ],
        ylxdata3: [
          "江苏",
          "上海",
          "浙江",
          "山东",
          "北京",
          "广东",
          "天津",
          "四川",
          "甘肃",
          "湖北",
        ],
        ylydata3: [
          68.67, 65.65, 65.25, 65.1, 64.83, 64.22, 63.18, 62.9, 62.14, 61.09,
        ],
      };
      let mockData = [];
      let mockDataCity = [];
      if (this.tabMiniIndex2 === 1) {
        mockDataCity = dataArr.ylxdata1;
        mockData = dataArr.ylydata1;
      } else if (this.tabMiniIndex2 === 2) {
        mockDataCity = dataArr.ylxdata2;
        mockData = dataArr.ylydata2;
      } else if (this.tabMiniIndex2 === 3) {
        mockDataCity = dataArr.ylxdata3;
        mockData = dataArr.ylydata3;
      }
      // tooltip
      const tooltip = {
        show: false,
        trigger: "axis",
        textStyle: { fontSize: "100%" },
        formatter: (params) => {
          let rander = params
            .map((item) =>
              item.seriesType !== "pictorialBar"
                ? `<div>${item.seriesName}: ${
                    item.seriesType !== "line" ? item.value : item.value + "%"
                  }</div>`
                : ""
            )
            .join("");
          return `
            <div>${params[0].axisValue}</div>
            ${rander}
        `;
        },
      };
      const legend = {
        show: false,
        data: ["超算算力", "智算算力", "通用算力", "算力增速"],
        textStyle: { fontSize: this.GLOBAL.relPx(14), color: "#fff" },
        itemWidth: this.GLOBAL.relPx(25),
        itemHeight: this.GLOBAL.relPx(15),
        itemGap: 15,
        bottom: "5%",
        selectedMode: false,
      };
      const grid = { top: "0", left: "0", right: "0", bottom: "20" };
      // xAxis
      const xAxis = {
        axisTick: { show: true },
        axisLine: { lineStyle: { color: "rgba(255,255,255, .2)" } },
        axisLabel: {
          textStyle: { fontSize: this.GLOBAL.relPx(12), color: "#f5f5f5" },
        },
        data: mockDataCity,
      };

      // yAxis
      const yAxis = [
        {
          splitNumber: 4,
          axisTick: { show: false },
          axisLine: { show: false },
          splitLine: { lineStyle: { color: "rgba(255,255,255, .05)" } },
          axisLabel: {
            textStyle: { fontSize: this.GLOBAL.relPx(12), color: "#f5f5f5" },
          },
        },
        {
          show: true,
          max: 100,
          splitLine: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            textStyle: { fontSize: this.GLOBAL.relPx(12), color: "#A9A9AA" },
            formatter: (params) => {
              return `${params}%`;
            },
          },
        },
      ];

      // series
      const series = [
        {
          name: "超算算力",
          type: "pictorialBar",
          symbolSize: [this.GLOBAL.relPx(14), this.GLOBAL.relPx(7)],
          symbolOffset: [this.GLOBAL.relPx(0), this.GLOBAL.relPx(5)],
          z: 12,
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(94, 114, 88,0.7)",
                },
                {
                  offset: 1,
                  color: "rgba(94, 114, 88,1)",
                },
              ]),
            },
          },
          data: mockData,
        },
        {
          name: "超算算力",
          type: "bar",
          barWidth: this.GLOBAL.relPx(14),
          barGap: "10%",
          label: {
            normal: {
              show: false, //开启显示
              position: "top", //柱形上方
              textStyle: {
                //数值样式
                color: "#fff",
              },
            },
          },
          itemStyle: {
            normal: {
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: "linear",
                global: false,
                colorStops: [
                  {
                    //第一节下面
                    offset: 0,
                    color: "rgba(94, 114, 88,0)",
                  },
                  {
                    offset: 1,
                    color: "rgba(94, 114, 88,0.7)",
                  },
                ],
              },
            },
          },
          data: mockData,
        },
        {
          name: "超算算力",
          type: "pictorialBar",
          symbolSize: [this.GLOBAL.relPx(14), this.GLOBAL.relPx(7)],
          symbolOffset: [this.GLOBAL.relPx(0), this.GLOBAL.relPx(5)],
          z: 12,
          symbolPosition: "end",
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(94, 114, 88,0.7)",
                },
                {
                  offset: 1,
                  color: "rgba(94, 114, 88,1)",
                },
              ]),
            },
          },
          data: mockData,
        },
      ];
      let dataZoom = [
        {
          // 第一个 dataZoom 组件
          type: "inside",
          xAxisIndex: 0, // 表示这个 dataZoom 组件控制 第一个 xAxis
          startValue: 0, // 数据窗口范围的起始数值index
          endValue: 7, // 数据窗口范围的结束数值index
        },
      ];
      let option = {
        tooltip,
        xAxis,
        yAxis,
        series,
        grid,
        legend,
        backgroundColor: "",
        dataZoom,
        textStyle: {
          fontFamily: "Microsoft YaHei",
        },
      };
      // 内存泄漏 无dom 不执行
      if (!this.myChartecNew_3) {
        this.myChartecNew_3 = echarts.init(this.$refs.ecNew_3, null, {
          width: this.GLOBAL.relPx(450),
          height: this.GLOBAL.relPx(300),
        });
      }
      this.GLOBAL.echartsDomArray.push(this.myChartecNew_3);
      this.myChartecNew_3.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.myChartecNew_3.setOption(option);
      this.ecNew_3ZoomTimer = setInterval(() => {
        if (option.dataZoom[0].endValue == mockData.length - 1) {
          option.dataZoom[0].endValue = 7;
          option.dataZoom[0].startValue = 0;
        } else {
          option.dataZoom[0].endValue = option.dataZoom[0].endValue + 1;
          option.dataZoom[0].startValue = option.dataZoom[0].startValue + 1;
        }
        this.myChartecNew_3.setOption(option);
      }, 2000);
      this.GLOBAL.timerArraySet.push(this.ecNew_3ZoomTimer);
    },
    ecNew_2Fun() {
      // mock数据
      const dataArr = {
        clxdata1: [
          "广东",
          "江苏",
          "上海",
          "河北",
          "北京",
          "浙江",
          "贵州",
          "甘肃",
          "山东",
          "四川",
        ],
        clydata1: [
          81.87, 75.65, 67.77, 64.66, 61.83, 53.78, 46.49, 38.54, 34.45, 33.97,
        ],
        clxdata2: [
          "广东",
          "江苏",
          "上海",
          "河北",
          "北京",
          "浙江",
          "贵州",
          "山东",
          "甘肃",
          "内蒙古",
        ],
        clydata2: [
          76.03, 68.58, 61.33, 58.18, 54.16, 46.25, 39.41, 32.47, 31.26, 29.2,
        ],
        clxdata3: [
          "北京",
          "广东",
          "天津",
          "上海",
          "内蒙",
          "山西",
          "江苏",
          "河北",
          "重庆",
          "湖北",
        ],
        clydata3: [
          73.84, 71.37, 63.67, 63.6, 62.82, 61.75, 59.95, 58.71, 58.13, 51.14,
        ],
      };
      let mockData = [];
      let mockDataCity = [];
      if (this.tabMiniIndex2 === 1) {
        mockDataCity = dataArr.clxdata1;
        mockData = dataArr.clydata1;
      } else if (this.tabMiniIndex2 === 2) {
        mockDataCity = dataArr.clxdata2;
        mockData = dataArr.clydata2;
      } else if (this.tabMiniIndex2 === 3) {
        mockDataCity = dataArr.clxdata3;
        mockData = dataArr.clydata3;
      }

      // tooltip
      const tooltip = {
        show: false,
        trigger: "axis",
        textStyle: { fontSize: "100%" },
        formatter: (params) => {
          let rander = params
            .map((item) =>
              item.seriesType !== "pictorialBar"
                ? `<div>${item.seriesName}: ${
                    item.seriesType !== "line" ? item.value : item.value + "%"
                  }</div>`
                : ""
            )
            .join("");
          return `
            <div>${params[0].axisValue}</div>
            ${rander}
        `;
        },
      };
      const legend = {
        show: false,
        data: ["超算算力", "智算算力", "通用算力", "算力增速"],
        textStyle: { fontSize: this.GLOBAL.relPx(14), color: "#fff" },
        itemWidth: this.GLOBAL.relPx(25),
        itemHeight: this.GLOBAL.relPx(15),
        itemGap: this.GLOBAL.relPx(15),
        bottom: "5%",
        selectedMode: false,
      };
      const grid = { top: "0", left: "0", right: "20", bottom: "20" };
      // xAxis
      const xAxis = {
        axisTick: { show: true },
        axisLine: { lineStyle: { color: "rgba(255,255,255, .2)" } },
        axisLabel: {
          textStyle: { fontSize: this.GLOBAL.relPx(12), color: "#f5f5f5" },
        },
        data: mockDataCity,
      };

      // yAxis
      const yAxis = [
        {
          splitNumber: 4,
          axisTick: { show: false },
          axisLine: { show: false },
          splitLine: { lineStyle: { color: "rgba(255,255,255, .05)" } },
          axisLabel: {
            textStyle: { fontSize: this.GLOBAL.relPx(12), color: "#f5f5f5" },
          },
        },
        {
          show: true,
          max: 100,
          splitLine: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            textStyle: { fontSize: this.GLOBAL.relPx(12), color: "#A9A9AA" },
            formatter: (params) => {
              return `${params}%`;
            },
          },
        },
      ];

      // series
      const series = [
        {
          name: "超算算力",
          type: "pictorialBar",
          symbolSize: [this.GLOBAL.relPx(14), this.GLOBAL.relPx(7)],
          symbolOffset: [this.GLOBAL.relPx(0), this.GLOBAL.relPx(5)],
          z: 12,
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(54,96,58,0.7)",
                },
                {
                  offset: 1,
                  color: "rgba(54,96,58,1)",
                },
              ]),
            },
          },
          data: mockData,
        },
        {
          name: "超算算力",
          type: "bar",
          barWidth: this.GLOBAL.relPx(14),
          barGap: "10%",
          label: {
            normal: {
              show: false, //开启显示
              position: "top", //柱形上方
              textStyle: {
                //数值样式
                color: "#fff",
              },
            },
          },
          itemStyle: {
            normal: {
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: "linear",
                global: false,
                colorStops: [
                  {
                    //第一节下面
                    offset: 0,
                    color: "rgba(54,96,58,0)",
                  },
                  {
                    offset: 1,
                    color: "rgba(54,96,58,0.7)",
                  },
                ],
              },
            },
          },
          data: mockData,
        },
        {
          name: "超算算力",
          type: "pictorialBar",
          symbolSize: [this.GLOBAL.relPx(14), this.GLOBAL.relPx(7)],
          symbolOffset: [this.GLOBAL.relPx(0), this.GLOBAL.relPx(5)],
          z: 12,
          symbolPosition: "end",
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(54,96,58,0.7)",
                },
                {
                  offset: 1,
                  color: "rgba(54,96,58,1)",
                },
              ]),
            },
          },
          data: mockData,
        },
      ];
      let dataZoom = [
        {
          // 第一个 dataZoom 组件
          type: "inside",
          xAxisIndex: 0, // 表示这个 dataZoom 组件控制 第一个 xAxis
          startValue: 0, // 数据窗口范围的起始数值index
          endValue: 7, // 数据窗口范围的结束数值index
        },
      ];
      let option = {
        tooltip,
        xAxis,
        yAxis,
        series,
        grid,
        legend,
        backgroundColor: "",
        dataZoom,
        textStyle: {
          fontFamily: "Microsoft YaHei",
        },
      };
      // 内存泄漏 无dom 不执行
      if (!this.myChartecNew_2) {
        this.myChartecNew_2 = echarts.init(this.$refs.ecNew_2, null, {
          width: this.GLOBAL.relPx(450),
          height: this.GLOBAL.relPx(300),
        });
      }
      this.GLOBAL.echartsDomArray.push(this.myChartecNew_2);
      this.myChartecNew_2.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.myChartecNew_2.setOption(option);
      this.ecNew_2ZoomTimer = setInterval(() => {
        if (option.dataZoom[0].endValue == mockData.length - 1) {
          option.dataZoom[0].endValue = 7;
          option.dataZoom[0].startValue = 0;
        } else {
          option.dataZoom[0].endValue = option.dataZoom[0].endValue + 1;
          option.dataZoom[0].startValue = option.dataZoom[0].startValue + 1;
        }
        this.myChartecNew_2.setOption(option);
      }, 2000);
      this.GLOBAL.timerArraySet.push(this.ecNew_2ZoomTimer);
    },
    ecNew_1Fun() {
      const dataArr = {
        slxdata1: [
          "河北",
          "广东",
          "江苏",
          "上海",
          "北京",
          "内蒙",
          "山西",
          "宁夏",
          "湖北",
          "贵州",
        ],
        slydata1: [
          67.02, 66.26, 65.82, 65.12, 49.16, 46.13, 42.84, 41.73, 39.76, 37.37,
        ],
        slxdata2: [
          "江苏",
          "河北",
          "上海",
          "广东",
          "北京",
          "山东",
          "山西",
          "贵州",
          "浙江",
          "内蒙",
        ],
        slydata2: [
          78.69, 75.91, 73.63, 62.89, 43.78, 37.14, 36.96, 33.46, 32.14, 28.56,
        ],
        slxdata3: [
          "广东",
          "河北",
          "上海",
          "宁夏",
          "内蒙",
          "江苏",
          "北京",
          "甘肃",
          "湖北",
          "安徽",
        ],
        slydata3: [
          68.67, 62.65, 61.85, 59.9, 57.03, 56.62, 54.18, 50.9, 48.14, 42.09,
        ],
      };
      let mockData = [];
      let mockDataCity = [];
      if (this.tabMiniIndex1 === 1) {
        mockDataCity = dataArr.slxdata1;
        mockData = dataArr.slydata1;
      } else if (this.tabMiniIndex1 === 2) {
        mockDataCity = dataArr.slxdata2;
        mockData = dataArr.slydata2;
      } else if (this.tabMiniIndex1 === 3) {
        mockDataCity = dataArr.slxdata3;
        mockData = dataArr.slydata3;
      }

      let data = mockData;

      let option = {
        textStyle: {
          fontFamily: "Microsoft YaHei",
        },
        color: ["#74d1fd", "#009ae4", "#0071c1"],
        // 设置图表的位置
        grid: {
          x: 0, // 左间距
          y: this.GLOBAL.relPx(0), // 上间距
          x2: this.GLOBAL.relPx(20), // 右间距
          y2: this.GLOBAL.relPx(20), // 下间距
          containLabel: true, // grid 区域是否包含坐标轴的刻度标签, 常用于『防止标签溢出』的场景
        },
        // 提示框组件
        tooltip: {
          show: false,
          trigger: "axis", // 触发类型, axis: 坐标轴触发
          axisPointer: {
            // 指示器类型  'line' 直线指示器 'shadow' 阴影指示器 'none' 无指示器 'cross' 十字准星指示器。
            // 其实是种简写，表示启用两个正交的轴的 axisPointer。
            type: "none",
          },
          textStyle: {
            color: "#cdd3ee", // 文字颜色
          },
          // 提示框浮层内容格式器，支持字符串模板和回调函数两种形式
          // 折线（区域）图、柱状（条形）图、K线图 : {a}（系列名称），{b}（类目值），{c}（数值）, {d}（无）
          formatter: "{b}<br />{a0}: {c0}万件<br />{a1}: {c1}万件",
        },
        // 图例组件
        legend: {
          show: false,
          textStyle: {
            // 文本样式
            fontSize: 16,
            color: "#cdd3ee",
          },
          top: this.GLOBAL.relPx(20), // 定位
          data: ["衣服", "鞋子"], // 图例的数据数组
        },
        // X轴
        xAxis: {
          type: "value", // 坐标轴类型,   'value' 数值轴，适用于连续数据
          // 坐标轴刻度
          axisTick: {
            show: false, // 是否显示坐标轴刻度 默认显示
          },
          // 坐标轴轴线
          axisLine: {
            // 是否显示坐标轴轴线 默认显示
            show: true, // 是否显示坐标轴轴线 默认显示
            lineStyle: {
              // color: '#4C4C4C',
            },
          },
          // 坐标轴在图表区域中的分隔线
          splitLine: {
            show: true, // 是否显示分隔线。默认数值轴显示
            lineStyle: {
              color: "#4C4C4C",
              type: "dashed",
              opacity: 0.4,
            },
          },
          // 坐标轴刻度标签
          axisLabel: {
            show: true, // 是否显示刻度标签 默认显示
            color: "f5f5f5",
            lineStyle: {
              color: "#4C4C4C",
              type: "dashed",
              opacity: 0.4,
            },
          },
        },
        yAxis: [
          // 左侧Y轴
          {
            type: "category", // 坐标轴类型,  'category' 类目轴，适用于离散的类目数据，为该类型时必须通过 data 设置类目数据
            // 坐标轴刻度
            axisTick: {
              show: false, // 是否显示坐标轴刻度 默认显示
            },
            inverse: true, // 倒序
            // 坐标轴轴线
            axisLine: {
              // 是否显示坐标轴轴线 默认显示
              show: false, // 是否显示坐标轴轴线 默认显示
              lineStyle: {
                // 坐标轴线线的颜色
                color: "#cdd3ee",
              },
            },
            // 坐标轴在图表区域中的分隔线
            splitLine: {
              show: false, // 是否显示分隔线。默认数值轴显示
            },
            // 坐标轴刻度标签
            axisLabel: {
              show: true, // 是否显示刻度标签 默认显示
              fontSize: 12, // 文字的字体大小
              color: "#f5f5f5", // 刻度标签文字的颜色
              // 使用字符串模板，模板变量为刻度默认标签 {value}
              formatter: "{value}",
            },
            data: mockDataCity,
          },
          // 右侧Y轴
          // {
          //   type: 'category', // 坐标轴类型
          //   // 坐标轴轴线
          //   axisLine: {
          //     show: false
          //   },
          //   // 坐标轴刻度
          //   axisTick: {
          //     show: false
          //   },
          //   // 坐标轴刻度标签
          //   axisLabel: {
          //     show: true, // 是否显示刻度标签 默认显示
          //     fontSize: 12, // 文字的字体大小
          //     color: '#A9A9AA', // 刻度标签文字的颜色
          //     // 使用字符串模板，模板变量为刻度默认标签 {value}
          //     formatter: '{value}'
          //   },
          //   data: data
          // }
        ],
        // 系列列表
        series: [
          {
            name: "超算算力",
            type: "pictorialBar",
            symbolSize: [this.GLOBAL.relPx(14), this.GLOBAL.relPx(8)],
            symbolOffset: [this.GLOBAL.relPx(-7), this.GLOBAL.relPx(0)],
            symbolRotate: 90,
            z: 12,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(0, 214, 223,0.3)",
                  },
                  {
                    offset: 1,
                    color: "rgba(0, 214, 223,0.6)",
                  },
                ]),
              },
            },
            data: data,
          },
          {
            name: "超算算力",
            type: "bar",
            barWidth: this.GLOBAL.relPx(14),
            barGap: "10%",
            label: {
              normal: {
                show: false, //开启显示
                position: "right", //柱形上方
                textStyle: {
                  //数值样式
                  color: "#fff",
                },
              },
            },
            itemStyle: {
              normal: {
                color: {
                  x: 1,
                  y: 0,
                  x2: 0,
                  y2: 0,
                  type: "linear",
                  global: false,
                  colorStops: [
                    {
                      //第一节下面
                      offset: 0,
                      color: "rgba(33, 117, 152,0)",
                    },
                    {
                      offset: 1,
                      color: "rgba(33, 117, 152,0.7)",
                    },
                  ],
                },
              },
            },
            data: data,
          },
          {
            name: "超算算力",
            type: "pictorialBar",
            symbolSize: [this.GLOBAL.relPx(14), this.GLOBAL.relPx(8)],
            symbolOffset: [this.GLOBAL.relPx(-7), this.GLOBAL.relPx(0)],
            symbolRotate: 90,
            z: 12,
            symbolPosition: "end",
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(0, 214, 223,0.3)",
                  },
                  {
                    offset: 1,
                    color: "rgba(0, 214, 223,0.6)",
                  },
                ]),
              },
            },
            data: data,
          },
        ],
        // 区域缩放
        dataZoom: [
          {
            // 第一个 dataZoom 组件
            type: "inside",
            yAxisIndex: 0, // 表示这个 dataZoom 组件控制 第一个 xAxis
            startValue: 0, // 数据窗口范围的起始数值index
            endValue: 7, // 数据窗口范围的结束数值index
          },
        ],
      };
      if (!this.myChartecNew_1) {
        this.myChartecNew_1 = echarts.init(this.$refs.ecNew_1, null, {
          width: this.GLOBAL.relPx(450),
          height: this.GLOBAL.relPx(300),
        });
      }
      this.GLOBAL.echartsDomArray.push(this.myChartecNew_1);
      this.myChartecNew_1.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.myChartecNew_1.setOption(option);
      this.ecNew_1ZoomTimer = setInterval(() => {
        if (option.dataZoom[0].endValue == data.length - 1) {
          option.dataZoom[0].endValue = 7;
          option.dataZoom[0].startValue = 0;
        } else {
          option.dataZoom[0].endValue = option.dataZoom[0].endValue + 1;
          option.dataZoom[0].startValue = option.dataZoom[0].startValue + 1;
        }
        this.myChartecNew_1.setOption(option);
      }, 2000);
      this.GLOBAL.timerArraySet.push(this.ecNew_1ZoomTimer);
    },
    ec5Fun() {
      let option = {
        textStyle: {
          fontFamily: "Microsoft YaHei",
        },
        color: ["#84C330", "#DF5E3F", "#E2B048", "#2FA6D8", "#195EB5"],
        title: [
          {
            text: "单机柜平均功率分布",
            x: "center",
            bottom: this.GLOBAL.relPx(60),
            textStyle: {
              color: "rgba(255,255,255,0.6)",
              fontSize: this.GLOBAL.relPx(12),
              fontWeight: "normal",
            },
          },
          {
            text: "2022年",
            x: "center",
            y: "40%",
            // bottom: '10%',
            textStyle: {
              color: "rgba(255,255,255,0.9)",
              fontSize: this.GLOBAL.relPx(18),
              fontWeight: "normal",
            },
          },
        ],
        grid: {
          x: 0, // 左间距
          y: 0, // 上间距
          x2: 0, // 右间距
          y2: 0, // 下间距
          containLabel: true, // grid 区域是否包含坐标轴的刻度标签, 常用于『防止标签溢出』的场景
        },
        tooltip: {
          show: false,
          trigger: "item",
        },
        legend: {
          width: "120%",
          bottom: "0",
          left: "center",
          textStyle: {
            color: "rgba(255,255,255,0.6)",
          },
        },
        series: [
          {
            name: "2022年",
            type: "pie",
            radius: ["40%", "50%"],
            center: ["50%", "40%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 0,
              borderColor: "#222226",
              borderWidth: this.GLOBAL.relPx(3),
            },
            label: {
              show: true,
              position: "inside",
              color: "#ffffff",
              formatter: "{d}%",
              shadowColor: "#000",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: this.GLOBAL.relPx(40),
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },

            data: [
              { value: 8, name: "<4kW" },
              { value: 40, name: "4-6kW" },
              { value: 32, name: "6-8kW" },
              { value: 8, name: "8-12kW" },
              { value: 12, name: "12-20kW" },
            ],
          },
        ],
      };
      // 内存泄漏 无dom 不执行
      if (!this.$refs.ec5) {
        return false;
      }
      this.myChartec5 = echarts.init(this.$refs.ec5, null, {
        width: this.GLOBAL.relPx(328),
        height: this.GLOBAL.relPx(390),
      });
      this.GLOBAL.echartsDomArray.push(this.myChartec5);
      this.myChartec5.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.myChartec5.setOption(option);
    },
    ec6Fun() {
      let data = [
        1.426, 1.428, 1.453, 1.456, 1.459, 1.468, 1.475, 1.481, 1.479, 1.483,
      ];
      let option = {
        title: {
          text: "PUE较低TOP10省份",
          x: "center",
          bottom: "0%",
          textStyle: {
            color: "rgba(255,255,255,0.6)",
            fontSize: this.GLOBAL.relPx(12),
            fontWeight: "normal",
          },
        },
        textStyle: {
          fontFamily: "Microsoft YaHei",
        },
        color: ["#74d1fd", "#009ae4", "#0071c1"],
        // 设置图表的位置
        grid: {
          x: this.GLOBAL.relPx(40), // 左间距
          y: this.GLOBAL.relPx(30), // 上间距
          x2: this.GLOBAL.relPx(30), // 右间距
          y2: this.GLOBAL.relPx(30), // 下间距
          containLabel: true, // grid 区域是否包含坐标轴的刻度标签, 常用于『防止标签溢出』的场景
        },
        // 提示框组件
        tooltip: {
          show: false,
          trigger: "axis", // 触发类型, axis: 坐标轴触发
          axisPointer: {
            // 指示器类型  'line' 直线指示器 'shadow' 阴影指示器 'none' 无指示器 'cross' 十字准星指示器。
            // 其实是种简写，表示启用两个正交的轴的 axisPointer。
            type: "none",
          },
          textStyle: {
            color: "#cdd3ee", // 文字颜色
          },
          // 提示框浮层内容格式器，支持字符串模板和回调函数两种形式
          // 折线（区域）图、柱状（条形）图、K线图 : {a}（系列名称），{b}（类目值），{c}（数值）, {d}（无）
          formatter: "{b}<br />{a0}: {c0}万件<br />{a1}: {c1}万件",
        },
        // X轴
        xAxis: {
          type: "value", // 坐标轴类型,   'value' 数值轴，适用于连续数据
          // 坐标轴刻度
          axisTick: {
            show: false, // 是否显示坐标轴刻度 默认显示
          },
          // 坐标轴轴线
          axisLine: {
            // 是否显示坐标轴轴线 默认显示
            show: true, // 是否显示坐标轴轴线 默认显示
            lineStyle: {
              // color: '#4C4C4C',
            },
          },
          // 坐标轴在图表区域中的分隔线
          splitLine: {
            show: true, // 是否显示分隔线。默认数值轴显示
            lineStyle: {
              color: "#4C4C4C",
              type: "dashed",
              opacity: 0.4,
            },
          },
          // 坐标轴刻度标签
          axisLabel: {
            show: true, // 是否显示刻度标签 默认显示
            lineStyle: {
              color: "#4C4C4C",
              type: "dashed",
              opacity: 0.4,
            },
          },
        },
        yAxis: [
          // 左侧Y轴
          {
            type: "category", // 坐标轴类型,  'category' 类目轴，适用于离散的类目数据，为该类型时必须通过 data 设置类目数据
            // 坐标轴刻度
            axisTick: {
              show: false, // 是否显示坐标轴刻度 默认显示
            },
            // 坐标轴轴线
            axisLine: {
              // 是否显示坐标轴轴线 默认显示
              show: false, // 是否显示坐标轴轴线 默认显示
              lineStyle: {
                // 坐标轴线线的颜色
                color: "#cdd3ee",
              },
            },
            // 坐标轴在图表区域中的分隔线
            splitLine: {
              show: false, // 是否显示分隔线。默认数值轴显示
            },
            // 坐标轴刻度标签
            axisLabel: {
              show: true, // 是否显示刻度标签 默认显示
              fontSize: this.GLOBAL.relPx(12), // 文字的字体大小
              color: "#A9A9AA", // 刻度标签文字的颜色
              // 使用字符串模板，模板变量为刻度默认标签 {value}
              formatter: "{value}",
            },
            data: [
              "内蒙古",
              "宁夏",
              "北京",
              "辽宁",
              "青海",
              "广西",
              "江西",
              "山西",
              "上海",
              "安徽",
            ], // 类目数据，在类目轴（type: 'category'）中有效
          },
          // 右侧Y轴
          // {
          //   type: 'category', // 坐标轴类型
          //   // 坐标轴轴线
          //   axisLine: {
          //     show: false
          //   },
          //   // 坐标轴刻度
          //   axisTick: {
          //     show: false
          //   },
          //   // 坐标轴刻度标签
          //   axisLabel: {
          //     show: true, // 是否显示刻度标签 默认显示
          //     fontSize: 12, // 文字的字体大小
          //     color: '#A9A9AA', // 刻度标签文字的颜色
          //     // 使用字符串模板，模板变量为刻度默认标签 {value}
          //     formatter: '{value}'
          //   },
          //   data: data
          // }
        ],
        // 系列列表
        series: [
          {
            name: "超算算力",
            type: "pictorialBar",
            symbolSize: [this.GLOBAL.relPx(20), this.GLOBAL.relPx(8)],
            symbolOffset: [this.GLOBAL.relPx(-10), this.GLOBAL.relPx(0)],
            symbolRotate: 90,
            z: 12,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(0, 214, 223,0.3)",
                  },
                  {
                    offset: 1,
                    color: "rgba(0, 214, 223,0.6)",
                  },
                ]),
              },
            },
            data: data,
          },
          {
            name: "超算算力",
            type: "bar",
            barWidth: this.GLOBAL.relPx(20),
            barGap: "10%",
            label: {
              normal: {
                show: true, //开启显示
                position: "right", //柱形上方
                textStyle: {
                  //数值样式
                  color: "#fff",
                },
              },
            },
            itemStyle: {
              normal: {
                color: {
                  x: 1,
                  y: 0,
                  x2: 0,
                  y2: 0,
                  type: "linear",
                  global: false,
                  colorStops: [
                    {
                      //第一节下面
                      offset: 0,
                      color: "rgba(33, 117, 152,0)",
                    },
                    {
                      offset: 1,
                      color: "rgba(33, 117, 152,0.7)",
                    },
                  ],
                },
              },
            },
            data: data,
          },
          {
            name: "超算算力",
            type: "pictorialBar",
            symbolSize: [this.GLOBAL.relPx(20), this.GLOBAL.relPx(8)],
            symbolOffset: [this.GLOBAL.relPx(-10), this.GLOBAL.relPx(0)],
            symbolRotate: 90,
            z: 12,
            symbolPosition: "end",
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(0, 214, 223,0.3)",
                  },
                  {
                    offset: 1,
                    color: "rgba(0, 214, 223,0.6)",
                  },
                ]),
              },
            },
            data: data,
          },
        ],
        // 区域缩放
        dataZoom: [
          {
            // 第一个 dataZoom 组件
            type: "inside",
            yAxisIndex: 0, // 表示这个 dataZoom 组件控制 第一个 xAxis
            startValue: 0, // 数据窗口范围的起始数值index
            endValue: 4, // 数据窗口范围的结束数值index
          },
        ],
      };
      // 内存泄漏 无dom 不执行
      if (!this.$refs.ec6) {
        return false;
      }
      this.myChartec6 = echarts.init(this.$refs.ec6, null, {
        width: this.GLOBAL.relPx(450),
        height: this.GLOBAL.relPx(330),
      });
      this.GLOBAL.echartsDomArray.push(this.myChartec6);
      this.myChartec6.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.myChartec6.setOption(option);
      // 使用刚指定的配置项和数据显示图表。
      this.ec6ZoomTimer = setInterval(() => {
        if (option.dataZoom[0].endValue == data.length - 1) {
          option.dataZoom[0].endValue = 4;
          option.dataZoom[0].startValue = 0;
        } else {
          option.dataZoom[0].endValue = option.dataZoom[0].endValue + 1;
          option.dataZoom[0].startValue = option.dataZoom[0].startValue + 1;
        }
        this.myChartec6.setOption(option);
      }, 2000);
      this.GLOBAL.timerArraySet.push(this.ec6ZoomTimer);
    },
    ec4Fun() {
      const title = {
        text: "",
        // show: false,
        x: "center",
        bottom: "0%",
        textStyle: {
          color: " rgba(255,255,255,0.6)",
          fontSize: this.GLOBAL.relPx(12),
          fontWeight: "normal",
        },
      };
      // mock数据
      const dataArr = {
        xdata: ["2017", "2018", "2019", "2020", "2021", "2022"],
        vaccination: [166, 226, 315, 401, 520, 670],
        unvaccinated: [83, 167, 237, 309, 420, 540],
      };

      // tooltip
      const tooltip = {
        show: false,
        trigger: "axis",
        textStyle: { fontSize: "100%" },
        formatter: (params) => {
          let rander = params
            .map((item) =>
              item.seriesType !== "pictorialBar"
                ? `<div>${item.seriesName}: ${
                    item.seriesType !== "line" ? item.value : item.value + "%"
                  }</div>`
                : ""
            )
            .join("");
          return `
            <div>${params[0].axisValue}</div>
            ${rander}
        `;
        },
      };
      const legend = {
        data: ["总机架(万架)", "大型规模以上机架(万架)"],
        textStyle: {
          fontSize: this.GLOBAL.relPx(12),
          color: "rgba(255,255,255,0.6)",
        },
        itemWidth: this.GLOBAL.relPx(25),
        itemHeight: this.GLOBAL.relPx(15),
        itemGap: 15,
        bottom: "7%",
        selectedMode: false,
      };
      const grid = {
        top: this.GLOBAL.relPx(30),
        left: this.GLOBAL.relPx(30),
        right: this.GLOBAL.relPx(60),
        bottom: this.GLOBAL.relPx(80),
      };
      // xAxis
      const xAxis = {
        axisTick: { show: true },
        axisLine: { lineStyle: { color: "rgba(255,255,255, .2)" } },
        axisLabel: {
          textStyle: { fontSize: this.GLOBAL.relPx(12), color: "#A9A9AA" },
        },
        data: dataArr.xdata,
      };

      // yAxis
      const yAxis = [
        {
          splitNumber: 4,
          axisTick: { show: false },
          axisLine: { show: false },
          splitLine: { lineStyle: { color: "rgba(255,255,255, .05)" } },
          axisLabel: {
            textStyle: { fontSize: this.GLOBAL.relPx(12), color: "#A9A9AA" },
          },
        },
        {
          show: true,
          max: 100,
          splitLine: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            textStyle: { fontSize: this.GLOBAL.relPx(12), color: "#A9A9AA" },
            formatter: (params) => {
              return `${params}%`;
            },
          },
        },
      ];

      // series
      const series = [
        {
          name: "总机架(万架)",
          type: "pictorialBar",
          symbolSize: [this.GLOBAL.relPx(20), this.GLOBAL.relPx(10)],
          symbolOffset: [this.GLOBAL.relPx(-11), this.GLOBAL.relPx(5)],
          z: 12,
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(0, 228, 255,0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(0, 228, 255,0.6)",
                },
              ]),
            },
          },
          data: dataArr.vaccination,
        },
        {
          name: "总机架(万架)",
          type: "bar",
          barWidth: this.GLOBAL.relPx(20),
          barGap: "10%",
          label: {
            normal: {
              show: true, //开启显示
              position: "top", //柱形上方
              textStyle: {
                //数值样式
                color: "#fff",
              },
            },
          },
          itemStyle: {
            normal: {
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: "linear",
                global: false,
                colorStops: [
                  {
                    //第一节下面
                    offset: 0,
                    color: "rgba(0, 228, 255,0)",
                  },
                  {
                    offset: 1,
                    color: "rgba(0, 228, 255,0.4)",
                  },
                ],
              },
            },
          },
          data: dataArr.vaccination,
        },
        {
          name: "总机架(万架)",
          type: "pictorialBar",
          symbolSize: [this.GLOBAL.relPx(20), this.GLOBAL.relPx(10)],
          symbolOffset: [this.GLOBAL.relPx(-11), this.GLOBAL.relPx(5)],
          z: 12,
          symbolPosition: "end",
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(0, 228, 255,0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(0, 228, 255,0.6)",
                },
              ]),
            },
          },
          data: dataArr.vaccination,
        },

        {
          name: "大型规模以上机架(万架)",
          type: "pictorialBar",
          symbolSize: [this.GLOBAL.relPx(20), this.GLOBAL.relPx(10)],
          symbolOffset: [this.GLOBAL.relPx(11), this.GLOBAL.relPx(5)],
          z: 12,
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(255, 127, 0,0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(255, 127, 0,0.6)",
                },
              ]),
            },
          },
          data: dataArr.unvaccinated,
        },
        {
          name: "大型规模以上机架(万架)",
          type: "bar",
          barWidth: this.GLOBAL.relPx(20),
          barGap: "10%",
          label: {
            normal: {
              show: true, //开启显示
              position: "top", //柱形上方
              textStyle: {
                //数值样式
                color: "#fff",
              },
            },
          },
          itemStyle: {
            normal: {
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: "linear",
                global: false,
                colorStops: [
                  {
                    //第一节下面
                    offset: 0,
                    color: "rgba(255, 127, 0,0)",
                  },
                  {
                    offset: 1,
                    color: "rgba(255, 127, 0,0.4)",
                  },
                ],
              },
            },
          },
          data: dataArr.unvaccinated,
        },
        {
          name: "大型规模以上机架(万架)",
          type: "pictorialBar",
          symbolSize: [this.GLOBAL.relPx(20), this.GLOBAL.relPx(10)],
          symbolOffset: [this.GLOBAL.relPx(11), this.GLOBAL.relPx(5)],
          z: 12,
          symbolPosition: "end",
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(255, 127, 0,0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(255, 127, 0,0.6)",
                },
              ]),
            },
          },
          data: dataArr.unvaccinated,
        },
      ];
      let option = {
        title,
        tooltip,
        xAxis,
        yAxis,
        series,
        grid,
        legend,
        backgroundColor: "",
        textStyle: {
          fontFamily: "Microsoft YaHei",
        },
      };
      // 内存泄漏 无dom 不执行
      if (!this.$refs.ec4) {
        return false;
      }
      this.myChartec4 = echarts.init(this.$refs.ec4, null, {
        width: this.GLOBAL.relPx(900),
        height: this.GLOBAL.relPx(410),
      });
      this.GLOBAL.echartsDomArray.push(this.myChartec4);
      this.myChartec4.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.myChartec4.setOption(option);
    },
    ec2Fun() {
      let data = [
        67.51, 66.23, 66.39, 61.24, 54.49, 46.66, 43.46, 43.39, 40.31, 39.72,
      ];
      let option = {
        textStyle: {
          fontFamily: "Microsoft YaHei",
        },
        color: ["#74d1fd", "#009ae4", "#0071c1"],
        // 设置图表的位置
        grid: {
          x: 0, // 左间距
          y: 0, // 上间距
          x2: 30, // 右间距
          y2: 0, // 下间距
          containLabel: true, // grid 区域是否包含坐标轴的刻度标签, 常用于『防止标签溢出』的场景
        },
        // 提示框组件
        tooltip: {
          show: false,
          trigger: "axis", // 触发类型, axis: 坐标轴触发
          axisPointer: {
            // 指示器类型  'line' 直线指示器 'shadow' 阴影指示器 'none' 无指示器 'cross' 十字准星指示器。
            // 其实是种简写，表示启用两个正交的轴的 axisPointer。
            type: "none",
          },
          textStyle: {
            color: "#cdd3ee", // 文字颜色
          },
          // 提示框浮层内容格式器，支持字符串模板和回调函数两种形式
          // 折线（区域）图、柱状（条形）图、K线图 : {a}（系列名称），{b}（类目值），{c}（数值）, {d}（无）
          formatter: "{b}<br />{a0}: {c0}万件<br />{a1}: {c1}万件",
        },
        // 图例组件
        legend: {
          show: false,
          textStyle: {
            // 文本样式
            fontSize: this.GLOBAL.relPx(16),
            color: "#cdd3ee",
          },
          top: this.GLOBAL.relPx(20), // 定位
          data: ["衣服", "鞋子"], // 图例的数据数组
        },
        // X轴
        xAxis: {
          type: "value", // 坐标轴类型,   'value' 数值轴，适用于连续数据
          // 坐标轴刻度
          axisTick: {
            show: false, // 是否显示坐标轴刻度 默认显示
          },
          // 坐标轴轴线
          axisLine: {
            // 是否显示坐标轴轴线 默认显示
            show: true, // 是否显示坐标轴轴线 默认显示
            lineStyle: {
              // color: '#4C4C4C',
            },
          },
          // 坐标轴在图表区域中的分隔线
          splitLine: {
            show: true, // 是否显示分隔线。默认数值轴显示
            lineStyle: {
              color: "#4C4C4C",
              type: "dashed",
              opacity: 0.4,
            },
          },
          // 坐标轴刻度标签
          axisLabel: {
            show: true, // 是否显示刻度标签 默认显示
            color: "f5f5f5",
            lineStyle: {
              color: "#f5f5f5",
              type: "dashed",
              opacity: 0.4,
            },
          },
        },
        yAxis: [
          // 左侧Y轴
          {
            type: "category", // 坐标轴类型,  'category' 类目轴，适用于离散的类目数据，为该类型时必须通过 data 设置类目数据
            // 坐标轴刻度
            axisTick: {
              show: false, // 是否显示坐标轴刻度 默认显示
            },
            inverse: true,
            // 坐标轴轴线
            axisLine: {
              // 是否显示坐标轴轴线 默认显示
              show: false, // 是否显示坐标轴轴线 默认显示
              lineStyle: {
                // 坐标轴线线的颜色
                color: "#cdd3ee",
              },
            },
            // 坐标轴在图表区域中的分隔线
            splitLine: {
              show: false, // 是否显示分隔线。默认数值轴显示
            },
            // 坐标轴刻度标签
            axisLabel: {
              show: true, // 是否显示刻度标签 默认显示
              fontSize: this.GLOBAL.relPx(12), // 文字的字体大小
              color: "#f5f5f5", // 刻度标签文字的颜色
              // 使用字符串模板，模板变量为刻度默认标签 {value}
              formatter: "{value}",
            },
            data: [
              "广东",
              "江苏",
              "上海",
              "河北",
              "北京",
              "浙江",
              "内蒙古",
              "山东",
              "贵州",
              "四川",
            ],
          },
          // 右侧Y轴
          // {
          //   type: 'category', // 坐标轴类型
          //   // 坐标轴轴线
          //   axisLine: {
          //     show: false
          //   },
          //   // 坐标轴刻度
          //   axisTick: {
          //     show: false
          //   },
          //   // 坐标轴刻度标签
          //   axisLabel: {
          //     show: true, // 是否显示刻度标签 默认显示
          //     fontSize: 12, // 文字的字体大小
          //     color: '#A9A9AA', // 刻度标签文字的颜色
          //     // 使用字符串模板，模板变量为刻度默认标签 {value}
          //     formatter: '{value}'
          //   },
          //   data: data
          // }
        ],
        // 系列列表
        series: [
          {
            name: "超算算力",
            type: "pictorialBar",
            symbolSize: [this.GLOBAL.relPx(14), this.GLOBAL.relPx(8)],
            symbolOffset: [this.GLOBAL.relPx(-7), this.GLOBAL.relPx(0)],
            symbolRotate: 90,
            z: 12,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(0, 214, 223,0.3)",
                  },
                  {
                    offset: 1,
                    color: "rgba(0, 214, 223,0.6)",
                  },
                ]),
              },
            },
            data: data,
          },
          {
            name: "超算算力",
            type: "bar",
            barWidth: this.GLOBAL.relPx(14),
            barGap: "10%",
            label: {
              normal: {
                show: false, //开启显示
                position: "right", //柱形上方
                textStyle: {
                  //数值样式
                  color: "#fff",
                },
              },
            },
            itemStyle: {
              normal: {
                color: {
                  x: 1,
                  y: 0,
                  x2: 0,
                  y2: 0,
                  type: "linear",
                  global: false,
                  colorStops: [
                    {
                      //第一节下面
                      offset: 0,
                      color: "rgba(33, 117, 152,0)",
                    },
                    {
                      offset: 1,
                      color: "rgba(33, 117, 152,0.7)",
                    },
                  ],
                },
              },
            },
            data: data,
          },
          {
            name: "超算算力",
            type: "pictorialBar",
            symbolSize: [this.GLOBAL.relPx(14), this.GLOBAL.relPx(8)],
            symbolOffset: [this.GLOBAL.relPx(-7), this.GLOBAL.relPx(0)],
            symbolRotate: 90,
            z: 12,
            symbolPosition: "end",
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(0, 214, 223,0.3)",
                  },
                  {
                    offset: 1,
                    color: "rgba(0, 214, 223,0.6)",
                  },
                ]),
              },
            },
            data: data,
          },
        ],
        // 区域缩放
        dataZoom: [
          {
            // 第一个 dataZoom 组件
            type: "inside",
            yAxisIndex: 0, // 表示这个 dataZoom 组件控制 第一个 xAxis
            startValue: 0, // 数据窗口范围的起始数值index
            endValue: 7, // 数据窗口范围的结束数值index
          },
        ],
      };
      // 内存泄漏 无dom 不执行
      if (!this.$refs.ec2) {
        return false;
      }
      this.myChartec2 = echarts.init(this.$refs.ec2, null, {
        width: this.GLOBAL.relPx(450),
        height: this.GLOBAL.relPx(300),
      });
      this.GLOBAL.echartsDomArray.push(this.myChartec2);
      this.myChartec2.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.myChartec2.setOption(option);
      this.ec2ZoomTimer = setInterval(() => {
        if (option.dataZoom[0].endValue == data.length - 1) {
          option.dataZoom[0].endValue = 7;
          option.dataZoom[0].startValue = 0;
        } else {
          option.dataZoom[0].endValue = option.dataZoom[0].endValue + 1;
          option.dataZoom[0].startValue = option.dataZoom[0].startValue + 1;
        }
        this.myChartec2.setOption(option);
      }, 2000);
      this.GLOBAL.timerArraySet.push(this.ec2ZoomTimer);
    },
    initMap_8() {
      echarts.registerMap("china", { geoJSON: chinaMap });
      let provinceData = [
        {
          name: "北京",
          selected: false,
          value: 1,
          itemStyle: { normal: { areaColor: "#5E482B" } },
        },
        {
          name: "天津",
          selected: false,
          value: 2,
          itemStyle: { normal: { areaColor: "#334053" } },
        },
        { name: "上海", selected: false, value: 3 },
        { name: "重庆", selected: false, value: 4 },
        {
          name: "河北",
          selected: false,
          value: 5,
          itemStyle: { normal: { areaColor: "#5E482B" } },
        },
        { name: "河南", selected: false, value: 6 },
        { name: "云南", selected: false, value: 7 },
        { name: "辽宁", selected: false, value: 8 },
        { name: "黑龙江", selected: false, value: 9 },
        { name: "湖南", selected: false, value: 10 },
        { name: "安徽", selected: false, value: 11 },
        {
          name: "山东",
          selected: false,
          value: 12,
          itemStyle: { normal: { areaColor: "#334053" } },
        },
        { name: "新疆", selected: false, value: 13 },
        {
          name: "江苏",
          selected: false,
          value: 14,
          itemStyle: { normal: { areaColor: "#5E482B" } },
        },
        {
          name: "浙江",
          selected: false,
          value: 15,
          itemStyle: { normal: { areaColor: "#334053" } },
        },
        { name: "江西", selected: false, value: 16 },
        { name: "湖北", selected: false, value: 17 },
        { name: "广西", selected: false, value: 18 },
        { name: "甘肃", selected: false, value: 19 },
        {
          name: "山西",
          selected: false,
          value: 20,
          itemStyle: { normal: { areaColor: "#334053" } },
        },
        {
          name: "内蒙古",
          selected: true,
          value: 21,
          itemStyle: { normal: { areaColor: "#334053" } },
        },
        { name: "陕西", selected: false, value: 22 },
        { name: "吉林", selected: false, value: 23 },
        { name: "福建", selected: false, value: 24 },
        {
          name: "贵州",
          selected: false,
          value: 25,
          itemStyle: { normal: { areaColor: "#334053" } },
        },
        {
          name: "广东",
          selected: false,
          value: 26,
          itemStyle: { normal: { areaColor: "#5E482B" } },
        },
        { name: "青海", selected: false, value: 27 },
        { name: "西藏", selected: false, value: 28 },
        { name: "四川", selected: false, value: 29 },
        { name: "宁夏", selected: false, value: 30 },
        { name: "海南", selected: false, value: 31 },
        { name: "台湾", selected: false, value: 32 },
        { name: "香港", selected: false, value: 33 },
        { name: "澳门", selected: false, value: 34 },
      ];
      let provinceData2 = [
        { name: "北京", selected: false, value: 1 },
        { name: "天津", selected: false, value: 2 },
        { name: "上海", selected: false, value: 3 },
        { name: "重庆", selected: false, value: 4 },
        {
          name: "河北",
          selected: false,
          value: 5,
          itemStyle: { normal: { areaColor: "#2C5038" } },
        },
        { name: "河南", selected: false, value: 6 },
        { name: "云南", selected: false, value: 7 },
        { name: "辽宁", selected: false, value: 8 },
        { name: "黑龙江", selected: false, value: 9 },
        {
          name: "湖南",
          selected: false,
          value: 10,
          itemStyle: { normal: { areaColor: "#434C33" } },
        },
        { name: "安徽", selected: false, value: 11 },
        {
          name: "山东",
          selected: false,
          value: 12,
          itemStyle: { normal: { areaColor: "#434C33" } },
        },
        { name: "新疆", selected: false, value: 13 },
        {
          name: "江苏",
          selected: false,
          value: 14,
          itemStyle: { normal: { areaColor: "#2C5038" } },
        },
        {
          name: "浙江",
          selected: false,
          value: 15,
          itemStyle: { normal: { areaColor: "#434C33" } },
        },
        { name: "江西", selected: false, value: 16 },
        {
          name: "湖北",
          selected: false,
          value: 17,
          itemStyle: { normal: { areaColor: "#434C33" } },
        },
        { name: "广西", selected: false, value: 18 },
        {
          name: "甘肃",
          selected: false,
          value: 19,
          itemStyle: { normal: { areaColor: "#434C33" } },
        },
        {
          name: "山西",
          selected: false,
          value: 20,
          itemStyle: { normal: { areaColor: "#2C5038" } },
        },
        {
          name: "内蒙古",
          selected: true,
          value: 21,
          itemStyle: { normal: { areaColor: "#434C33" } },
        },
        { name: "陕西", selected: false, value: 22 },
        { name: "吉林", selected: false, value: 23 },
        { name: "福建", selected: false, value: 24 },
        { name: "贵州", selected: false, value: 25 },
        {
          name: "广东",
          selected: false,
          value: 26,
          itemStyle: { normal: { areaColor: "#434C33" } },
        },
        { name: "青海", selected: false, value: 27 },
        {
          name: "西藏",
          selected: false,
          value: 28,
          itemStyle: { normal: { areaColor: "#434C33" } },
        },
        {
          name: "四川",
          selected: false,
          value: 29,
          itemStyle: { normal: { areaColor: "#434C33" } },
        },
        { name: "宁夏", selected: false, value: 30 },
        { name: "海南", selected: false, value: 31 },
        { name: "台湾", selected: false, value: 32 },
        { name: "香港", selected: false, value: 33 },
        { name: "澳门", selected: false, value: 34 },
      ];
      let points = [
        {
          value: [97.01778, 34.29162],
          name: "甘肃枢纽",
          itemStyle: { color: "#1AB4F2" },
        },
        {
          value: [119.41942, 32.70068],
          name: "扬州",
          itemStyle: { color: "#FCC858" },
        },
        {
          value: [121.48054, 31.23593],
          name: "上海",
          itemStyle: { color: "#FCC858" },
        },
        {
          value: [103.04954, 31.01679],
          name: "雅安",
          itemStyle: { color: "#FCC858" },
        },
        {
          value: [106.55844, 29.569],
          name: "重庆",
          itemStyle: { color: "#FCC858" },
        },
        {
          value: [106.50192, 26.4421],
          name: "贵安",
          itemStyle: { color: "#FCC858" },
        },
        {
          value: [118.09643, 24.78541],
          name: "厦门",
          itemStyle: { color: "#FCC858" },
        },
        {
          value: [113.27143, 23.83534],
          name: "广州",
          itemStyle: { color: "#FCC858" },
        },
        {
          value: [114.18732, 22.24966],
          name: "香港",
          itemStyle: { color: "#FCC858" },
        },
      ];
      let option = {
        backgroundColor: "",
        geo: {
          map: "china",
          aspectScale: 0.72, //长宽比
          center: [102.51888, 35.8485],
          zoom: 1.7,
          roam: false,
          label: {
            show: false,
          },
          emphasis: {
            disabled: false,
            itemStyle: {
              normal: {
                areaColor: {
                  type: "radial",
                  x: 0.3,
                  y: 0.3,
                  r: 0.8,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#464646", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#464646", // 100% 处的颜色
                    },
                  ],
                  globalCoord: true, // 缺省为 false
                },
                shadowColor: "#464646",
                shadowOffsetX: 5,
                shadowOffsetY: 5,
              },
            },
          },
          itemStyle: {
            normal: {
              areaColor: {
                type: "radial",
                x: 0.3,
                y: 0.3,
                r: 0.8,
                colorStops: [
                  {
                    offset: 0,
                    color: "#464646", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#464646", // 100% 处的颜色
                  },
                ],
                globalCoord: true, // 缺省为 false
              },
              shadowColor: "#464646",
              shadowOffsetX: 5,
              shadowOffsetY: 5,
            },
          },
          regions: [
            {
              name: "南海诸岛",
              itemStyle: {
                areaColor: "rgba(0, 10, 52, 1)",
                borderColor: "rgba(0, 10, 52, 1)",
                normal: {
                  opacity: 0,
                  label: {
                    show: false,
                    color: "#009cc9",
                  },
                },
              },
            },
          ],
        },
        series: [
          {
            type: "map",
            roam: false,
            center: [103.41888, 35.7485],
            label: {
              show: true,
              textStyle: {
                color: "#ccc",
              },
            },
            // selectedMode: false,
            selectedMode: "multiple",
            emphasis: {
              disabled: true,
            },
            select: {
              disabled: true,
              itemStyle: {
                borderColor: "#787879",
                borderWidth: 1,
                areaColor: "#334053",
              },
              label: {
                color: "#ccc",
              },
            },

            itemStyle: {
              borderColor: "#787879",
              borderWidth: 1,
              areaColor: "#333",
            },
            zoom: 1.7,
            //     roam: false,
            map: "china", //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
            data:
              this.leftBottomTabIndex_8 === 2
                ? []
                : this.leftBottomTabIndex_8 === 1
                ? provinceData
                : provinceData2,
          },
          // {
          //   type: 'effectScatter',
          //   coordinateSystem: 'geo',
          //   showEffectOn: 'render',
          //   zlevel: 1,
          //   rippleEffect: {
          //     number: 1,
          //     period: 1,
          //     scale: 3,
          //     brushType: 'fill'
          //   },
          //   hoverAnimation: false,
          //   label: {
          //     show: true,
          //     formatter: '{b}',
          //     position: 'right',
          //     offset: [5, 2],
          //     color: '#ffffff'
          //   },
          //   itemStyle: {
          //     normal: {
          //       color: '#1DE9B6',
          //       shadowBlur: 2,
          //       shadowColor: '#333'
          //     }
          //   },
          //   symbolSize: 8,
          //   data: points
          // },
          //地图线的动画效果
          // {
          //   type: 'lines',
          //   zlevel: 2,
          //   effect: {
          //     show: true,
          //     period: 2, //箭头指向速度，值越小速度越快
          //     trailLength: 0.4, //特效尾迹长度[0,1]值越大，尾迹越长重
          //     symbol: 'arrow', //箭头图标
          //     symbolSize: 5, //图标大小
          //   },
          //   lineStyle: {
          //     normal: {
          //       color: '#1DE9B6',
          //       width: 1, //线条宽度
          //       opacity: 0.1, //尾迹线条透明度
          //       curveness: .3 //尾迹线条曲直度
          //     }
          //   },
          //   data: [
          //     { coords: [[118.8062, 31.9208], [119.4543, 25.9222]], lineStyle: { color: '#4ab2e5' } }
          //   ]
          // }
        ],
      };
      if (!this.mapChart) {
        this.chartsMap_8 = echarts.init(this.$refs.map_8, null, {
          width: this.GLOBAL.relPx(865),
          height: this.GLOBAL.relPx(668),
        });
        this.GLOBAL.echartsDomArray.push(this.chartsMap_8);
      }
      this.chartsMap_8.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.chartsMap_8.setOption(option);
    },
    leftBottomTabIndex_8Change(index) {
      if (this.leftBottomTabIndex_8 != index) {
        this.leftBottomTabIndex_8 = index;
        this.initMap_8();
      }
      if (this.showYs) {
        this.showYs = false;
      }
    },
    rightTopIndexChange(index) {
      if (this.rightTopIndex != index) {
        this.rightTopIndex = index;
      }
    },
  },
  beforeDestroy() {
    this.resetFun();
  },
};
</script>

<style lang="scss" scoped>
.page0 {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  .ddearthFather {
    // transform: rotateZ(-23.5deg);
  }

  .leftWord {
    width: 900px;
    text-align: center;
    box-sizing: border-box;
    opacity: 0;
    padding-top: 200px;
    margin-left: 100px;

    // padding-left: 100px;
    .name {
      font-size: 44px;
      line-height: 44px;
      padding-top: 70px;
      text-align: center;
      color: rgba(255, 255, 255, 0.8);
    }

    .item {
      width: 50%;
      font-family: Arial;

      .big {
        font-size: 160px;
        line-height: 160px;

        span {
          font-size: 40px;
          line-height: 40px;
        }
      }

      .small {
        font-size: 32px;
        line-height: 32px;
        color: rgba(255, 255, 255, 0.8);
        padding-top: 60px;
      }
    }

    &.ani {
      animation: imgOpacityFun2 3.5s linear 1;
    }

    &.leftWordAni1 {
      animation: leftWordAni1 2s linear 1 forwards;
    }
  }

  .opacityBox {
    width: 700px;
    height: 700px;
    position: relative;
    margin: 0 auto;
    margin-top: 50px;

    .bottomRight {
      font-size: 10px;
      color: #a9a9aa;
      position: absolute;
      z-index: 1;
      bottom: -125px;
      right: 40px;
      text-align: center;

      &.opacity {
        animation: imgOpacity 1s linear 1 forwards;
      }
    }

    .ddearth {
      width: 100%;
      height: 100%;
      opacity: 0;

      &.opacity {
        animation: imgOpacity 1s linear 1 forwards;
      }
    }

    .mapChangeButton {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #26262a;
      position: absolute;
      top: 0;
      right: 0;
      z-index: 3;
      cursor: pointer;

      img {
        display: block;
        width: 100%;
        height: 100%;
        margin: 0 auto;
      }
    }

    .opaitem {
      width: 100%;
      height: 100%;
      opacity: 0;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
    }

    .video {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
    }

    .gif {
      width: 904px;
      height: 946px;
      position: absolute;
      top: -108px;
      left: -125px;
      z-index: 2;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .timelineBox {
      margin: 0 auto;
      width: 720px;
      overflow: hidden;
      overflow-x: scroll;
    }

    .timelineBoxInner {
      transform: translateX(-62px);

      &.ani {
        animation: transformXAni 15s linear 1 forwards;
      }
    }

    .timeLine {
      position: absolute;
      bottom: -100px;
      left: 50%;
      z-index: 2;
      width: 1600px;
      margin-left: -800px;
      opacity: 0;

      .now {
        background-image: url("~@/assets/images/page0/now.png");
        background-size: cover;
        width: 8px;
        height: 19px;
        margin: 0 auto;
      }

      .line {
        width: 16000px;
        // overflow-x: scroll;
        height: 17px;
        margin-top: 15px;

        .item {
          width: 1px;
          height: 10px;
          background-color: #ffffff;
          opacity: 0.3;
          margin-right: 20px;

          &.on {
            height: 17px;
            opacity: 0.7;
            transform: translateY(-7px);
          }
        }
      }

      .year {
        width: 16000px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.3);
        box-sizing: border-box;
        padding-left: 405px;

        .item {
          margin-right: 412px;

          &.on {
            color: #ffffff;
            font-size: 14px;
          }
        }
      }

      &.opacity {
        animation: imgOpacity 1s linear 1 forwards;
      }
    }
  }

  .ddmap {
    width: 1400px;
    height: 700px;
    margin: 0 auto;
    margin-top: 50px;
    opacity: 0;

    &.opacity {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .rightMapPosi {
    position: relative;
    position: fixed;
    top: 125px;
    left: 30px;
    z-index: 9;
    background-color: #201f23;
    width: 1860px;
    height: 930px;
    border-radius: 8px;

    .close {
      background-image: url("~@/assets/images/page5/close.png");
      background-size: cover;
      width: 25px;
      height: 25px;
      position: absolute;
      top: 10px;
      right: 10px;
      cursor: pointer;
    }

    .bottomRight {
      font-size: 10px;
      color: #a9a9aa;
      position: absolute;
      z-index: 1;
      bottom: -100px;
      right: 0;
      opacity: 0;

      &.opacity {
        animation: imgOpacity 1s linear 1 forwards;
      }
    }

    .usachina {
      width: 845px;
      position: absolute;
      z-index: 1;
      bottom: 0px;

      .top {
        width: 100%;
        background-color: rgba($color: #26262a, $alpha: 0.5);
        height: 82px;
        box-sizing: border-box;
        text-align: center;
        padding-top: 15px;

        .item {
          width: 16.66%;
          border-left: 1px solid rgba($color: #26262a, $alpha: 0.5);
          box-sizing: border-box;

          .label {
            font-size: 14px;
          }

          .value {
            font-size: 26px;
            padding-top: 5px;

            .icon {
              width: 31px;
              height: 20px;
              margin-left: 10px;

              img {
                width: 100%;
                height: 100%;
              }
            }
          }
        }
      }

      .down {
        width: 100%;
        background-color: rgba($color: #26262a, $alpha: 0.5);
        height: 86px;
        box-sizing: border-box;
        text-align: center;
        margin-top: 16px;

        .item {
          width: 50%;
          height: 50%;
          box-sizing: border-box;
          padding: 10px 40px 0 40px;
          font-size: 14px;

          .icon {
            width: 18px;
            height: 18px;
            margin-right: 20px;

            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }

      &.posi0 {
        left: 0px;
      }

      &.posi1 {
        right: 0px;
      }

      &.opacity {
        animation: imgOpacity 1s linear 1 forwards;
      }
    }
  }

  .posiBox {
    width: 562px;
    position: absolute;
    z-index: 1;
    opacity: 0;

    .top {
      background-color: rgba($color: #26262a, $alpha: 0.5);
      padding: 0px 16px;
      font-size: 14px;
      color: #ffffff;
      line-height: 36px;

      .item {
        width: 50%;
        box-sizing: border-box;
      }

      .icon {
        width: 18px;
        height: 18px;
        margin-top: 4px;
        margin-right: 15px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .w1 {
        padding-left: 15px;
        width: 200px;
      }
    }

    .down {
      background-color: rgba($color: #26262a, $alpha: 0.5);
      padding: 9px 0;
      margin-top: 6px;

      .item {
        width: 24.5%;
        font-size: 14px;
        color: #ffffff;
        box-sizing: border-box;
        padding-left: 16px;
        text-align: center;

        .value {
          margin-top: 5px;
        }
      }

      .line {
        width: 1px;
        height: 42px;
        background-color: rgba($color: #ffffff, $alpha: 0.15);
        margin-top: 3px;
      }
    }

    &.posi1 {
      top: 380px;
      right: 50px;
    }

    &.posi2 {
      top: 100px;
      right: 80px;
    }

    &.posi3 {
      top: 100px;
      left: 180px;
    }

    &.posi4 {
      top: 350px;
      left: 180px;
    }

    &.posi5 {
      top: 80px;
      right: -10px;
    }

    &.posiAni {
      animation: displayAni 3.5s linear 1;
    }

    &.posi6 {
      top: 450px;
      left: 80px;
    }
  }

  .rightEarth {
    padding-top: 10px;
    width: 100%;

    &.showGif {
      width: auto;
      padding-left: 90px;

      .opacityBox {
        // margin: 0;
      }

      // padding-left: 200px;
    }

    &.showLeftCore {
      width: auto;
      padding-left: 90px;

      .opacityBox {
        // margin: 0;
      }

      // padding-left: 200px;
    }
  }

  .rightChart {
    width: 920px;
    // margin-left: 200px;
    // transform: translateX(99999px);
    // width: 0;
    // overflow: hidden;
    // opacity: 0;
    position: absolute;
    top: 0;
    right: -1200px;

    .ecCore {
      background-color: rgba($color: #26262a, $alpha: 0.35);
      box-sizing: border-box;
      padding: 10px 14px;

      .bottomRight {
        font-size: 10px;
        color: #a9a9aa;
        margin: 0;
      }
    }

    .ecCore1 {
      .title {
        font-size: 16px;
        color: #ffffff;
      }

      .array {
        .wordCore {
          width: 25%;
          text-align: center;

          .value {
            font-size: 62px;
            font-family: Microsoft YaHei;
            color: #ffffff;
            line-height: 62px;
            margin-top: 30px;
          }

          .iconBox {
            padding-top: 23px;
            margin-bottom: 25px;
            color: #3db261;

            .icon {
              width: 14px;
              height: 12px;

              img {
                width: 100%;
                height: 100%;
              }
            }
          }

          .line {
            width: 21px;
            height: 2px;
            opacity: 0.3;
            background-color: #ffffff;
            margin: 0 auto;
          }

          .label {
            font-size: 14px;
            color: #ffffff;
            line-height: 38px;
          }

          .radar {
            background-image: url("~@/assets/images/page0/radar.png");
            background-size: cover;
            width: 170px;
            height: 182px;
            box-sizing: border-box;
            padding-top: 65px;
            margin: 0 auto;

            .num {
              font-size: 37px;
              color: #ffffff;
            }

            .unit {
              font-size: 12px;
              color: #f1f1f2;
            }

            &.on {
              background-image: url("~@/assets/images/page0/radar_on.png");
            }
          }
        }
      }
    }

    .ecCore2 {
      margin-top: 20px;

      .binRightCoreBox {
        padding-left: 50px;
      }

      .title {
        font-size: 16px;
        color: #ffffff;
        padding-bottom: 10px;
      }

      .ecBinBox {
        position: relative;
        margin: 0 auto;
        margin-top: 10px;

        .bg {
          background-image: url("~@/assets/images/page0/radar-round.png");
          background-size: cover;
          background-repeat: no-repeat;
          width: 164px;
          height: 164px;
          position: absolute;
          bottom: 0;
          left: 18px;
          z-index: -1;
        }

        .line1 {
          background-image: url("~@/assets/images/page0/radar-line1.png");
          background-size: cover;
          width: 129px;
          height: 17px;
          position: absolute;
          top: 30px;
          right: -96px;
        }

        .line2 {
          background-image: url("~@/assets/images/page0/radar-line2.png");
          background-size: cover;
          width: 129px;
          height: 17px;
          position: absolute;
          bottom: 22px;
          right: -101px;
        }
      }

      .ecBin {
      }

      .right {
        margin: 30px 0 0 50px;

        .item {
          width: 104px;
          height: 37px;
          background: rgba($color: #385b61, $alpha: 0.75);
          font-size: 14px;
          text-align: center;
          line-height: 16px;

          p {
            padding: 0;
            margin: 0;
          }

          &.sty2 {
            background: rgba($color: #6a8488, $alpha: 0.75);
          }

          &.sty3 {
            background: rgba($color: #9badb0, $alpha: 0.75);
          }
        }
      }

      .binRightCore {
        // padding-right: 50px;
      }

      .rightBottomTitle {
        font-size: 14px;
        color: #ffffff;
        line-height: 38px;
        // opacity: 0.6;
        text-align: center;
        margin-top: 10px;
        cursor: pointer;
      }

      .bottomRight {
        font-size: 10px;
        color: #a9a9aa;
        line-height: 18px;
      }
    }

    .ecCore3 {
      margin-top: 20px;

      .bottomRight {
        font-size: 10px;
        color: #a9a9aa;
        line-height: 38px;
      }
    }

    &.leftWordAni3 {
      animation: leftWordAni3 1s linear 0.5s 1 forwards;
    }
  }

  .earthCore {
    position: relative;

    &.leftWordAni2 {
      animation: leftWordAni2 1s linear 1.5s 1 forwards;
    }
  }
}

.page8 {
  box-sizing: border-box;

  .main {
    // padding-right: 60px;
    // padding-left: 60px;
  }

  .mapChangeButton {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #26262a;
    position: absolute;
    top: 10px;
    right: 30px;
    z-index: 3;
    cursor: pointer;

    img {
      display: block;
      width: 100%;
      height: 100%;
      margin: 0 auto;
    }
  }

  .newCenterBox {
    width: 960px;

    .bgCore {
      background: rgba(38, 38, 42, 0.15);
      padding: 10px;
      box-sizing: border-box;
      margin-bottom: 20px;
      height: 418px;

      .rightEcharts {
        .item {
          margin-bottom: 5px;

          .title {
            font-size: 14px;
          }

          .tabs {
            margin-top: 0;

            .son {
              width: 115px;
              height: 23px;
              background-size: cover;
              font-size: 14px;
              text-align: center;
              line-height: 23px;
              cursor: pointer;

              &.on {
                background-image: url("~@/assets/images/1920/page0/tabs.png");
                color: #00e4ff;
              }
            }

            &.on {
              margin-top: -30px;

              .item {
                margin: 0 40px;
              }
            }
          }
        }
      }

      .tabss {
        .st {
          width: 8px;
          height: 8px;
          background: #cccccc;
          border-radius: 50%;
          margin-left: 10px;
          cursor: pointer;

          &.on {
            background: #00e4ff;
          }
        }
      }

      .ec2 {
        margin-bottom: 15px;
      }

      .tabs {
        margin-top: 15px;
        width: 100%;

        .item {
          width: 115px;
          height: 23px;
          background-size: cover;
          font-size: 14px;
          text-align: center;
          line-height: 23px;
          margin: 0 -10px;
          cursor: pointer;

          &.on {
            background-image: url("~@/assets/images/1920/page0/tabs.png");
            color: #00e4ff;
          }
        }

        &.on {
          margin-top: -30px;

          .item {
            margin: 0 40px;
          }
        }
      }

      .groupBox {
        box-sizing: border-box;
        margin-top: 5px;

        .list {
          // padding-right: 1100px;
          box-sizing: border-box;
          margin-left: 90px;

          .item {
            background-image: url("~@/assets/images/page7/leftItem.png");
            background-size: contain;
            background-repeat: no-repeat;
            width: 157px;
            height: 182px;
            box-sizing: border-box;
            padding-top: 53px;
            text-align: center;
            position: relative;
            cursor: pointer;
            margin-right: 7px;

            .itemPosi {
              opacity: 1;

              .ss {
                position: absolute;
                z-index: 2;
                background-image: url("~@/assets/images/page7/leftItem2.png");
                background-size: cover;
                width: 167px;
                height: 194px;
                box-sizing: border-box;
                padding-top: 57px;

                .n1 {
                  font-size: 42px;
                  line-height: 42px;
                }

                .n2 {
                  font-size: 12px;
                  line-height: 12px;
                  padding: 10px 0;
                }

                .n3 {
                  font-size: 17px;
                  line-height: 17px;
                }

                &.s0 {
                  top: 0;
                  left: 176px;
                }

                &.s1 {
                  top: 160px;
                  left: 88px;
                }

                &.s2 {
                  top: 160px;
                  left: -88px;
                }

                &.s3 {
                  top: 0;
                  left: -176px;
                }
              }
            }

            .itemPosi2 {
              opacity: 1;

              .ss {
                position: absolute;
                z-index: 2;
                background-image: url("~@/assets/images/page7/leftItemDx.png");
                background-size: cover;
                width: 167px;
                height: 194px;
                box-sizing: border-box;
                padding-top: 40px;
                opacity: 0;
                .n1 {
                  font-size: 42px;
                  line-height: 42px;
                }

                .n2 {
                  font-size: 12px;
                  line-height: 12px;
                  padding: 10px 0;
                }

                .n3 {
                  font-size: 17px;
                  line-height: 17px;
                }

                &.s0 {
                  top: 0;
                  left: 352px;
                }

                &.s1 {
                  top: 160px;
                  left: 264px;
                }

                &.s2 {
                  top: 160px;
                  left: -264px;
                }

                &.s3 {
                  top: 0;
                  left: -352px;
                }
                &.show {
                  opacity: 1;
                }
              }
            }

            .w1 {
              font-size: 42px;
              line-height: 42px;
            }

            .w2 {
              font-size: 12px;
              line-height: 12px;
              padding: 10px 0;
            }

            .w3 {
              font-size: 17px;
              line-height: 17px;
            }

            // &:hover {
            //   background-image: url('~@/assets/images/page2/list-on.png');
            // }
            &.on {
              // background-image: url('~@/assets/images/page7/leftItem03.png');
              opacity: 0.1;
            }
          }

          &.list2 {
            transform: translate(83.5px, -43px);
            margin-top: 0;
          }
        }
      }

      // .tabs {
      //   // margin-bottom: 30px;
      //   transform: translateY(-20px);
      //   margin-bottom: 30px;

      //   .item {
      //     background-size: cover;
      //     width: 199px;
      //     height: 28px;
      //     font-size: 16px;
      //     color: #ffffff;
      //     margin: 0 10px;
      //     text-align: center;
      //     line-height: 28px;
      //     cursor: pointer;

      //     &.on {
      //       background-image: url('~@/assets/images/page7/leftTabItem.png');
      //       color: #00E4FF;
      //     }
      //   }
      // }

      .threeList {
        margin-top: 25px;
        padding: 0 40px;

        .item {
          background-size: cover;
          width: 400px;
          height: 343px;
          box-sizing: border-box;
          padding: 10px 25px;
          position: relative;
          background-size: contain;
          background-repeat: no-repeat;

          .slgmDialog {
            width: 600px;
            height: 300px;
            background: rgba($color: #1a191c, $alpha: 0.8);
            border: 1px solid #172e3a;
            border-radius: 8px;
            position: absolute;
            top: 55px;
            left: 50%;
            margin-left: -300px;
            box-sizing: border-box;
            padding: 25px 20px;
            z-index: 99;

            .stitle {
              font-size: 16px;
              margin-bottom: 20px;
            }

            .itemlist {
              padding: 0 35px;

              .sitem {
                background-image: url("~@/assets/images/page7/slgm.png");
                background-size: cover;
                width: 130px;
                height: 152px;
                text-align: center;
                position: relative;

                .sonListItem {
                  position: absolute;
                  z-index: 99;
                  background-image: url("~@/assets/images/page7/page2-list-2.png");
                  background-size: cover;
                  width: 130px;
                  height: 152px;

                  .ww1 {
                    font-size: 28px;
                    line-height: 28px;
                    margin-top: 50px;
                  }

                  .ww2 {
                    font-size: 10px;
                    line-height: 10px;
                    padding: 10px 0;
                  }

                  .ww3 {
                    font-size: 13px;
                    line-height: 20px;
                  }

                  &.sw1 {
                    top: -128px;
                    left: -72px;
                  }

                  &.sw2 {
                    top: -128px;
                    right: -72px;
                  }

                  &.sw3 {
                    top: 0px;
                    left: -144px;
                  }

                  &.sw4 {
                    top: 0px;
                    right: -144px;
                  }

                  &.sw5 {
                    top: 128px;
                    left: -72px;
                  }

                  &.sw6 {
                    top: 128px;
                    right: -72px;
                  }

                  &.sw7 {
                    top: 128px;
                    left: -216px;
                  }

                  &.sw8 {
                    top: 128px;
                    right: -216px;
                  }
                }

                .w1 {
                  font-size: 28px;
                  line-height: 28px;
                  margin-top: 50px;
                }

                .w2 {
                  font-size: 10px;
                  line-height: 10px;
                  padding: 10px 0;
                }

                .w3 {
                  font-size: 14px;
                  line-height: 20px;
                }

                &:first-child {
                  cursor: pointer;
                }
              }
            }
          }

          .son {
            margin-top: 10px;

            .icon {
              width: 22px;
              height: 22px;
              margin-right: 13px;

              img {
                width: 100%;
                height: 100%;
              }
            }

            .value {
              font-size: 14px;
              line-height: 22px;
              text-align: right;

              p {
                margin-bottom: 6px;
                margin-top: 0;
              }
            }

            &.on {
              color: #00e4ff;
            }

            &.first {
              cursor: pointer;
            }
          }

          .name {
            margin-top: 20px;
            text-align: center;
            cursor: pointer;

            &.on {
              color: #00e4ff;
            }
          }

          .nameline {
            width: 25px;
            height: 2px;
            background: #00d8ff;
            opacity: 0.8;
            border-radius: 1px;
            margin: 0 auto;
            margin-top: 10px;
          }

          &.item1 {
            background-image: url("~@/assets/images/page8/item2.png");
            margin-left: 0;
          }

          &.item2 {
            background-image: url("~@/assets/images/page8/item.png");
            margin-left: 0;
            padding-top: 10px;
            padding-left: 60px;
            padding-right: 60px;
            cursor: pointer;

            .yuan {
              background-image: url("~@/assets/images/page7/yuan.png");
              background-size: cover;
              width: 80px;
              height: 80px;
              box-sizing: border-box;
              padding-top: 10px;

              .icon {
                width: 57px;
                height: 40px;
                margin: 0 auto;

                img {
                  width: 100%;
                  height: 100%;
                }
              }

              .text {
                text-align: center;
                font-size: 10px;
              }

              &.y2,
              &.y4 {
                margin-top: 0px;
              }

              &.y3 {
                margin-top: 40px;
              }

              &.y5,
              &.y6 {
                margin: 0 15px;
                margin-top: 0px;
              }
            }
          }
        }

        .itemtitle {
          font-size: 16px;
          margin-top: 70px;
          text-align: center;

          &.on {
            color: #00e4ff;
          }
        }
      }

      .titleBox {
        line-height: 19px;

        .title {
          font-size: 14px;
        }

        .change {
          cursor: pointer;
          position: relative;
          z-index: 1;

          .icon {
            width: 19px;
            height: 19px;
            margin-right: 10px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .text {
            font-size: 14px;
            color: #00e4ff;
          }

          &.on {
            margin-right: 20px;
          }
        }
      }

      .ballCore {
        background-image: url("~@/assets/images/page2/ball-bg.png");
        background-size: cover;
        width: 325px;
        height: 304px;
        text-align: center;
        position: relative;
        margin-left: 50px;

        .c1 {
          padding-top: 10px;
        }

        .cc {
          // width: 248px;
          // height: 248px;
          margin-top: 70px;
          position: relative;
          z-index: 1;
        }

        .c3 {
          padding-top: 0px;
        }

        .size1 {
          font-size: 49px;
          line-height: 49px;
        }

        .size2 {
          font-size: 8px;
        }

        .size3 {
          font-size: 20px;
        }

        .size4 {
          font-size: 18px;
        }

        .cbg {
          background-image: url("~@/assets/images/page2/ball-center.gif");
          width: 400px;
          height: 400px;
          background-size: cover;
          position: absolute;
          top: -38px;
          left: -40px;
          z-index: 0;
        }
      }

      .bottomRight {
        font-size: 10px;
        color: #a9a9aa;
        line-height: 20px;
        // margin-top: 15px;
      }
    }
  }

  .ddmapBox {
    position: relative;
    width: 900px;
    height: 880px;
    z-index: 99;

    .bottomTabs {
      position: absolute;
      z-index: 2;
      bottom: 100px;
      left: 180px;

      .item {
        width: 115px;
        height: 23px;
        background-size: cover;
        font-size: 14px;
        text-align: center;
        line-height: 23px;
        margin: 0 40px;
        cursor: pointer;

        &.on {
          background-image: url("~@/assets/images/1920/page0/tabs.png");
          color: #00e4ff;
        }
      }
    }

    .leftLenged {
      position: absolute;
      z-index: 2;
      bottom: 180px;
      left: 30px;

      .item {
        margin-top: 20px;

        .color {
          width: 12px;
          height: 6px;
          margin-top: 3.5px;

          &.c1 {
            background: #dd8514;
          }

          &.c2 {
            background: #3172d2;
          }

          &.c3 {
            background: #00ff55;
          }

          &.c4 {
            background: #b4d132;
          }
        }

        .text {
          font-size: 14px;
          margin-left: 14px;
          line-height: 14px;
        }
      }
    }

    .top10 {
      .item {
        position: absolute;
        width: 20px;
        height: 26px;
        z-index: 2;
        background-size: cover;
        animation: imgOpacity31 3s ease-in-out infinite forwards;

        .text {
          padding-top: 28px;
          width: 30px;
          text-align: center;
          font-size: 12px;
          transform: translateX(-5px);
        }

        &.top1 {
          background-image: url("~@/assets/images/1920/page0/top1.png");
          top: 216px;
          left: 600px;
        }

        &.top2 {
          background-image: url("~@/assets/images/1920/page0/top2.png");
          top: 382px;
          left: 668px;
        }

        &.top3 {
          background-image: url("~@/assets/images/1920/page0/top3.png");
          top: 532px;
          left: 548px;
        }

        &.top4 {
          background-image: url("~@/assets/images/1920/page0/top4.png");
          top: 552px;
          left: 568px;
        }

        &.top5 {
          background-image: url("~@/assets/images/1920/page0/top5.png");
          top: 276px;
          left: 610px;
        }

        &.top6 {
          background-image: url("~@/assets/images/1920/page0/top6.png");
          top: 400px;
          left: 636px;
        }

        &.top7 {
          background-image: url("~@/assets/images/1920/page0/top7.png");
          top: 400px;
          left: 488px;
        }

        &.top8 {
          background-image: url("~@/assets/images/1920/page0/top8.png");
          top: 428px;
          left: 660px;
        }

        &.top9 {
          background-image: url("~@/assets/images/1920/page0/top9.png");
          top: 256px;
          left: 620px;
        }

        &.top10 {
          background-image: url("~@/assets/images/1920/page0/top10.png");
          top: 387px;
          left: 632px;
        }
      }

      &.op3 {
        opacity: 0.3;
      }
    }

    .scaleBox {
      // transform: scale(0.82) translateX(-100px);
    }

    .ysPointList {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 10px;
      left: 50px;

      .ysLenged {
        position: absolute;
        bottom: 160px;
        left: 0px;
        width: 200px;

        .item {
          width: 50%;
          margin-top: 10px;

          .icon {
            width: 15px;
            height: 18px;
            background-size: cover;
            margin-top: 3px;
            margin-right: 10px;
          }

          .ne {
            text-align: center;
            margin-top: 3px;
            font-size: 14px;
          }

          &.c_1 {
            z-index: 16;

            .icon {
              background-image: url("~@/assets/images/page7/c-1.png");
            }
          }

          &.c_2 {
            z-index: 15;

            .icon {
              background-image: url("~@/assets/images/page7/c-2.png");
            }
          }

          &.c_3 {
            z-index: 14;

            .icon {
              background-image: url("~@/assets/images/page7/c-3.png");
            }
          }

          &.c_4 {
            z-index: 13;

            .icon {
              background-image: url("~@/assets/images/page7/c-4.png");
            }
          }

          &.c_5 {
            z-index: 12;

            .icon {
              background-image: url("~@/assets/images/page7/c-5.png");
            }
          }

          &.c_6 {
            z-index: 11;

            .icon {
              background-image: url("~@/assets/images/page7/c-6.png");
            }
          }
        }
      }

      .ys {
        position: absolute;
        top: -80px;
        left: -80px;

        .icon {
          width: 18px;
          height: 21px;
          background-size: cover;
          margin: 0 auto;
        }

        .ne {
          text-align: center;
          margin-top: 3px;
          font-size: 14px;
        }

        &.ys1 {
          left: 320px;
          top: 246px;
        }

        &.ys2 {
          left: 420px;
          top: 460px;
        }

        &.ys3 {
          left: 450px;
          top: 320px;
        }

        &.ys4 {
          left: 510px;
          top: 180px;
        }

        &.ys5 {
          left: 490px;
          top: 470px;
        }

        &.ys6 {
          left: 500px;
          top: 280px;
        }

        &.ys7 {
          left: 520px;
          top: 270px;
        }

        &.ys8 {
          left: 540px;
          top: 260px;
        }

        &.ys9 {
          left: 370px;
          top: 400px;
        }

        &.ys10 {
          left: 520px;
          top: 180px;
        }

        &.ys11 {
          left: 520px;
          top: 210px;
        }

        &.ys12 {
          left: 520px;
          top: 520px;
        }

        &.ys13 {
          left: 595px;
          top: 430px;
        }

        &.ys14 {
          left: 590px;
          top: 365px;
        }

        &.ys15 {
          left: 510px;
          top: 210px;
        }

        &.ys16 {
          left: 500px;
          top: 180px;
        }

        &.ys17 {
          left: 430px;
          top: 410px;
        }

        &.ys18 {
          left: 540px;
          top: 210px;
        }

        &.ys19 {
          left: 490px;
          top: 510px;
        }

        &.ys20 {
          left: 570px;
          top: 490px;
        }

        &.ys21 {
          left: 625px;
          top: 385px;
        }

        &.c_1 {
          z-index: 16;

          .icon {
            background-image: url("~@/assets/images/page7/c-1.png");
          }
        }

        &.c_2 {
          z-index: 15;

          .icon {
            background-image: url("~@/assets/images/page7/c-2.png");
          }
        }

        &.c_3 {
          z-index: 14;

          .icon {
            background-image: url("~@/assets/images/page7/c-3.png");
          }
        }

        &.c_4 {
          z-index: 13;

          .icon {
            background-image: url("~@/assets/images/page7/c-4.png");
          }
        }

        &.c_5 {
          z-index: 12;

          .icon {
            background-image: url("~@/assets/images/page7/c-5.png");
          }
        }

        &.c_6 {
          z-index: 11;

          .icon {
            background-image: url("~@/assets/images/page7/c-6.png");
          }
        }
      }
    }

    .dialogPosi {
      .greenPosi {
        background-image: url("~@/assets/images/page7/green.png");
        background-size: cover;
        width: 134px;
        height: 578px;
        position: absolute;
        z-index: 1;
        transform: rotate(180deg);

        // &.on {
        //   background-image: url('~@/assets/images/page7/yellow.png');
        // }

        // &.g2 {
        //   background-image: url('~@/assets/images/page7/green2.png');
        // }

        // &.y2 {
        //   background-image: url('~@/assets/images/page7/yellow2.png');
        // }
      }

      .infoPosi {
        width: 800px;
        height: 590px;
        position: absolute;
        z-index: 1000;
        background: rgba($color: #201f23, $alpha: 0.95);
        border: 1px solid #172e3a;
        border-radius: 8px;
        box-sizing: border-box;
        padding: 20px;

        .infomap {
          background-color: #1a191c;
          padding-top: 20px;
          position: relative;
          border-radius: 8px;

          .line {
            width: 1px;
            height: 283px;
            background-image: url("~@/assets/images/page7/linetop.png");
            background-size: cover;
            top: 60px;
            left: 50%;
            margin-left: -0.5px;
          }

          .infomapitem {
            width: 50%;

            .title {
              font-size: 16px;
              text-align: center;
            }

            .bg {
              width: 403px;
              height: 320px;
              background-size: cover;
              position: relative;
              margin: 0 auto;

              .bgPoint {
                position: absolute;

                .point {
                  width: 12px;
                  height: 11px;
                  background-image: url("~@/assets/images/page7/point.png");
                  background-size: cover;
                  margin: 0 auto;
                }

                .text {
                  font-size: 12px;
                  color: #29ad9c;
                  text-align: center;
                  margin-top: 7px;
                }
                &.high {
                  .point {
                    width: 14px;
                    height: 13px;
                  }

                  .text {
                    font-size: 14px;
                    color: #29ad9c;
                    text-align: center;
                    font-weight: bold;
                    margin-top: 7px;
                  }
                }
              }

              &.bg1 {
                background-image: url("~@/assets/images/page7/map2.png");

                .bgPoint {
                  &.p1 {
                    top: 90px;
                    left: 290px;
                  }

                  &.p2 {
                    top: 140px;
                    left: 90px;
                  }

                  &.p3 {
                    top: 220px;
                    left: 210px;
                  }
                }
              }

              &.bg2 {
                background-image: url("~@/assets/images/page7/map1.png");

                .bgPoint {
                  &.p1 {
                    top: 50px;
                    left: 220px;
                  }

                  &.p2 {
                    top: 110px;
                    left: 230px;
                  }

                  &.p3 {
                    top: 80px;
                    left: 130px;
                  }
                }
              }

              &.bg3 {
                background-image: url("~@/assets/images/page7/map3.png");

                .bgPoint {
                  &.p1 {
                    top: 240px;
                    left: 210px;
                  }

                  &.p2 {
                    top: 130px;
                    left: 170px;
                  }

                  &.p3 {
                    top: 240px;
                    left: 280px;
                  }
                }
              }

              &.bg4 {
                background-image: url("~@/assets/images/page7/map4.png");

                .bgPoint {
                  &.p1 {
                    top: 210px;
                    left: 100px;
                  }

                  &.p2 {
                    top: 190px;
                    left: 80px;
                  }

                  &.p3 {
                    top: 200px;
                    left: 130px;
                  }
                }
              }

              &.bg5 {
                background-image: url("~@/assets/images/page7/map5.png");

                .bgPoint {
                  &.p1 {
                    top: 110px;
                    left: 140px;
                  }
                }
              }

              &.bg6 {
                background-image: url("~@/assets/images/page7/map6.png");

                .bgPoint {
                  &.p1 {
                    top: 220px;
                    left: 230px;
                  }

                  &.p2 {
                    top: 100px;
                    left: 140px;
                  }

                  &.p3 {
                    top: 190px;
                    left: 170px;
                  }
                }
              }

              &.bg7 {
                background-image: url("~@/assets/images/page7/map7.png");

                .bgPoint {
                  &.p1 {
                    top: 60px;
                    left: 110px;
                  }
                }
              }

              &.bg8 {
                background-image: url("~@/assets/images/page7/map8.png");

                .bgPoint {
                  &.p1 {
                    top: 250px;
                    left: 200px;
                  }
                }
              }

              &.bg9 {
                background-image: url("~@/assets/images/page7/map9.png");

                .bgPoint {
                  &.p1 {
                    top: 180px;
                    left: 220px;
                  }
                }
              }

              &.bg10 {
                background-image: url("~@/assets/images/page7/map10.png");

                .bgPoint {
                  &.p1 {
                    top: 150px;
                    left: 190px;
                  }
                }
              }
            }
          }
        }

        .pueLine {
          width: 685px;
          height: 1px;
          background-image: url("~@/assets/images/page7/linerow.png");
          background-size: cover;
          margin: 0 auto;
          margin-top: 20px;
        }

        .pueCore {
          .row {
            padding: 5px 0;
            margin-top: 10px;

            .w5 {
              width: 20%;

              .w5icon {
                background-image: url("~@/assets/images/page7/info-icon1.png");
                background-size: cover;
                width: 20px;
                height: 20px;
                margin-right: 10px;
              }

              .w5icon2 {
                background-image: url("~@/assets/images/page7/info-icon2.png");
                background-size: cover;
                width: 20px;
                height: 20px;
                margin-right: 10px;
              }
            }
          }
        }

        .puedownCore {
          padding-right: 50px;
          margin-top: 10px;

          .row {
            .w3 {
              div {
                padding: 5px 0;
              }

              .w3icon1 {
                background-image: url("~@/assets/images/page7/info-icon3.png");
                background-size: cover;
                width: 20px;
                height: 20px;
                margin-right: 10px;
                padding: 0;
                margin-top: 10px;
              }

              .w3icon2 {
                background-image: url("~@/assets/images/page7/info-icon4.png");
                background-size: cover;
                width: 20px;
                height: 20px;
                margin-right: 10px;
                padding: 0;
                margin-top: 10px;
              }

              .w3icon3 {
                background-image: url("~@/assets/images/page7/info-icon5.png");
                background-size: cover;
                width: 20px;
                height: 20px;
                margin-right: 10px;
                padding: 0;
                margin-top: 10px;
              }
            }
          }
        }
      }

      &.posi1 {
        .greenPosi {
          top: -50px;
          left: 620px;
        }

        .infoPosi {
          top: -50px;
          left: 760px;
        }
      }

      &.posi2 {
        .greenPosi {
          top: 170px;
          left: 460px;
        }

        .infoPosi {
          top: 170px;
          left: 600px;
        }
      }

      &.posi3 {
        .greenPosi {
          top: 300px;
          left: 575px;
          // transform: rotateY(180deg);
        }

        .infoPosi {
          top: 300px;
          left: 715px;
        }
      }

      &.posi4 {
        .greenPosi {
          top: 120px;
          left: 640px;
        }

        .infoPosi {
          top: 120px;
          left: 780px;
        }
      }

      &.posi5 {
        .greenPosi {
          top: 10px;
          left: 445px;
        }

        .infoPosi {
          top: 10px;
          left: 585px;
        }
      }

      &.posi6 {
        .greenPosi {
          top: 65px;
          left: 350px;
        }

        .infoPosi {
          top: 65px;
          left: 490px;
        }
      }

      &.posi7 {
        .greenPosi {
          top: -50px;
          left: 520px;
        }

        .infoPosi {
          top: -50px;
          left: 660px;
        }
      }

      &.posi8 {
        .greenPosi {
          top: 255px;
          left: 430px;
        }

        .infoPosi {
          top: 255px;
          left: 570px;
        }
      }
    }

    .posi {
      position: absolute;
      z-index: 9;
      cursor: pointer;

      &.posi1 {
        top: 220px;
        left: 590px;
      }

      &.posi2 {
        top: 425px;
        left: 360px;
      }

      &.posi3 {
        top: 560px;
        left: 550px;
      }

      &.posi4 {
        top: 375px;
        left: 605px;
      }

      &.posi5 {
        top: 280px;
        left: 350px;
      }

      &.posi6 {
        top: 335px;
        left: 255px;
      }

      &.posi7 {
        top: 215px;
        left: 415px;
      }

      &.posi8 {
        top: 525px;
        left: 340px;
      }
    }

    .greenBox {
      background-image: url("~@/assets/images/1920/page0/green.png");
      background-size: cover;
      width: 54px;
      height: 54px;
      text-align: center;
      display: table;

      .word {
        display: table-cell;
        vertical-align: middle;
        font-size: 12px;
      }

      &.yellowBox {
        background-image: url("~@/assets/images/1920/page0/blue.png");
      }

      &.greenBox2 {
        background-image: url("~@/assets/images/1920/page0/green2.png");
        width: 70px;
        height: 70px;
      }

      &.greenBox3 {
        background-image: url("~@/assets/images/1920/page0/green3.png");
        width: 70px;
        height: 70px;
      }

      &:hover {
        // transform: scale(1.1);
      }
    }

    .name {
      font-size: 12px;
      // line-height: 54px;
      text-align: center;
      color: #00e4ff;
      margin-left: 9px;
      margin-right: 9px;

      &.s1 {
        padding-top: 20px;
      }

      &.s2 {
        padding-top: 30px;
      }

      &.s3 {
        padding-top: 40px;
      }

      &.s4 {
        padding-top: 50px;
      }
    }

    .line {
      background-size: cover;
      position: absolute;
      z-index: 1;

      &.line1 {
        background-image: url("~@/assets/images/1920/page0/line1.png");
        width: 60px;
        height: 33px;
        top: 220px;
        left: 540px;

        &.lineopa {
          animation: imgOpacity31 3s ease-in-out infinite forwards;
        }
      }

      &.line2 {
        background-image: url("~@/assets/images/1920/page0/line2.png");
        width: 87px;
        height: 311px;
        top: 265px;
        left: 510px;

        &.lineopa {
          animation: imgOpacity51 2s ease-in-out infinite forwards;
        }
      }

      &.line3 {
        background-image: url("~@/assets/images/1920/page0/line3.png");
        width: 143px;
        height: 152px;
        top: 250px;
        left: 525px;

        &.lineopa {
          animation: imgOpacity51 3s ease-in-out infinite forwards;
        }
      }

      &.line4 {
        background-image: url("~@/assets/images/1920/page0/line4.png");
        width: 125px;
        height: 57px;
        top: 260px;
        left: 460px;

        &.lineopa {
          animation: imgOpacity31 2s ease-in-out infinite forwards;
        }
      }

      &.line5 {
        background-image: url("~@/assets/images/1920/page0/line5.png");
        width: 297px;
        height: 78px;
        top: 331px;
        left: 365px;

        &.lineopa {
          animation: imgOpacity31 3s ease-in-out infinite forwards;
        }
      }

      &.line6 {
        background-image: url("~@/assets/images/1920/page0/line6.png");
        width: 237px;
        height: 211px;
        top: 378px;
        left: 338px;

        &.lineopa {
          animation: imgOpacity51 1s ease-in-out infinite forwards;
        }
      }

      &.line7 {
        background-image: url("~@/assets/images/1920/page0/line7.png");
        width: 44px;
        height: 50px;
        top: 485px;
        left: 420px;

        &.lineopa {
          animation: imgOpacity31 4s ease-in-out infinite forwards;
        }
      }

      &.line8 {
        background-image: url("~@/assets/images/1920/page0/line8.png");
        width: 112px;
        height: 53px;
        top: 560px;
        left: 440px;

        &.lineopa {
          animation: imgOpacity51 2s ease-in-out infinite forwards;
        }
      }

      &.line9 {
        background-image: url("~@/assets/images/1920/page0/line9.png");
        width: 234px;
        height: 132px;
        top: 427px;
        left: 452px;

        &.lineopa {
          animation: imgOpacity51 2s ease-in-out infinite forwards;
        }
      }
    }

    &.map2 {
      background-image: url("~@/assets/images/page2/l2-map-1.png");

      .point {
        position: absolute;
        z-index: 8;
        top: 0;
        left: 0;
        background-image: url("~@/assets/images/page2/l2-map-2.png");
        background-size: cover;
        width: 992px;
        height: 774px;
      }

      .lenged {
        position: absolute;
        z-index: 8;
        top: 50px;
        left: -100px;

        .item {
          margin-bottom: 20px;

          .l {
            width: 12px;
            height: 6px;
            background: #00b2ff;
            margin-top: 5px;
            margin-right: 10px;

            &.on {
              background: #bb8f09;
            }
          }

          .r {
            font-size: 16px;
            color: #ffffff;
          }
        }
      }
    }

    &.hide {
      background: none;
    }

    .bottomRight {
      font-size: 10px;
      color: #a9a9aa;
      line-height: 20px;
      position: absolute;
      bottom: 50px;
      right: 40px;
      z-index: 1;
    }

    .lengedBottom {
      position: absolute;
      bottom: 15px;
      left: 100px;
      z-index: 1;

      .item {
        margin-left: 20px;

        .color {
          width: 12px;
          height: 4px;
          background: #ffdb60;
          margin-top: 7px;
        }

        .text {
          font-size: 12px;
          padding-left: 10px;
        }

        &.on {
          .color {
            background: rgba(0, 228, 255, 1);
          }
        }
      }
    }
  }

  .centerCore {
    width: 900px;
    height: 880px;
    background: rgba(38, 38, 42, 0.1);
    box-sizing: border-box;
    padding: 20px 30px;
    position: relative;
    display: none;

    .titleBox {
      line-height: 19px;

      .title {
        font-size: 14px;
      }

      .change {
        cursor: pointer;
        position: relative;
        z-index: 1;

        .icon {
          width: 19px;
          height: 19px;
          margin-right: 10px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .text {
          font-size: 14px;
          color: #00e4ff;
        }

        &.on {
          margin-right: 20px;
        }
      }
    }

    .ballCore {
      background-image: url("~@/assets/images/page2/ball-bg.png");
      background-size: cover;
      width: 325px;
      height: 304px;
      text-align: center;
      position: relative;
      margin-left: 50px;

      .c1 {
        padding-top: 10px;
      }

      .cc {
        // width: 248px;
        // height: 248px;
        margin-top: 70px;
        position: relative;
        z-index: 1;
      }

      .c3 {
        padding-top: 0px;
      }

      .size1 {
        font-size: 49px;
        line-height: 49px;
      }

      .size2 {
        font-size: 8px;
      }

      .size3 {
        font-size: 20px;
      }

      .size4 {
        font-size: 18px;
      }

      .cbg {
        background-image: url("~@/assets/images/page2/ball-center.gif");
        width: 400px;
        height: 400px;
        background-size: cover;
        position: absolute;
        top: -38px;
        left: -40px;
        z-index: 0;
      }
    }

    .bottomRight {
      font-size: 10px;
      color: #a9a9aa;
      line-height: 20px;
      margin-top: 15px;
    }

    &.on {
      // background: none;
    }
  }

  .rightCore {
    width: 900px;
    height: 880px;
    background: #201f23;
    box-sizing: border-box;
    padding: 20px 30px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;

    .bottomRight {
      font-size: 10px;
      color: #a9a9aa;
      line-height: 20px;
      margin-top: 0px;
      transform: translateY(-25px);
    }

    .titleBox {
      line-height: 19px;

      .title {
        font-size: 14px;
      }

      .change {
        cursor: pointer;

        .icon {
          width: 19px;
          height: 19px;
          margin-right: 10px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .text {
          font-size: 14px;
          color: #00e4ff;
        }

        &.on {
          margin-right: 20px;
        }
      }
    }

    .close {
      background-image: url("~@/assets/images/page5/close.png");
      background-size: cover;
      width: 25px;
      height: 25px;
      position: absolute;
      top: 10px;
      right: 10px;
      cursor: pointer;
      z-index: 1;
    }
  }

  .showYs {
    // width: 100%;
    // height: 100%;
    // position: absolute;
    // top: 50px;
    // left: 50px;
    // display: none;
  }

  .namemap {
    background-image: url("~@/assets/images/page7/namemap.png");
    background-size: cover;
    width: 813px;
    height: 634px;
    position: relative;
    margin-top: 30px;
    margin-left: 30px;

    // transform: scale(0.82);
    .mplist {
      position: absolute;
      bottom: -100px;
      left: 0;
      z-index: 1;
      width: 350px;

      .stem {
        width: 350px;
        // width: 100%;
        margin-top: 10px;

        .icon {
          width: 15px;
          height: 15px;
          background-image: url("~@/assets/images/page7/gp.png");
          background-size: cover;
          margin-top: 2px;
        }

        .color {
          width: 12px;
          height: 6px;
          margin-top: 5px;

          &.c1 {
            background: #00b2ff;
          }

          &.c2 {
            background: #ffd900;
          }
        }

        .text {
          font-size: 14px;
          padding-left: 15px;
        }
      }
    }

    .bottomRight2 {
      font-size: 10px;
      color: #a9a9aa;
      line-height: 20px;
      position: absolute;
      bottom: -100px;
      right: 0;
      z-index: 1;
      // margin-top: 100px;
      // transform: translateY(150px);
      // transform: translateX(30px);
    }
  }

  .op3 {
    opacity: 0.1;
  }

  .ddmap {
    &.hide {
      opacity: 0;
    }
  }
}

@keyframes transformXAni {
  0% {
    transform: translateX(-62px);
  }

  100% {
    transform: translateX(-502px);
  }
}

@keyframes imgOpacity {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes imgOpacityFun2 {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

@keyframes imgOpacity010 {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes widthAni {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(00px);
  }
}

@keyframes displayAni {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

@keyframes leftWordAni-2 {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-1400px);
    opacity: 0;
  }
}

@keyframes leftWordAni1 {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes leftWordAni3 {
  0% {
    display: block;
    opacity: 0;
    right: -1200px;
  }

  100% {
    opacity: 1;
    right: 0;
  }
}

@keyframes leftWordAni2 {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-100px);
  }
}

@keyframes leftWordAni2 {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-100px);
  }
}

@keyframes imgOpacity31 {
  0% {
    opacity: 0.3;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.3;
  }
}

@keyframes imgOpacity51 {
  0% {
    opacity: 0.5;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.5;
  }
}
</style>
