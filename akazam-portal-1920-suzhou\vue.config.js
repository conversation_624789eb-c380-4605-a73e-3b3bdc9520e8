'use strict'
const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}

const px2rem = require('postcss-px2rem')


// 配置基本大小
const postcss = px2rem({
  // 基准大小 baseSize，需要和rem.js中相同
  remUnit: 16
})


const CompressionPlugin = require('compression-webpack-plugin')
const name = '长三角算力资源一体化调度示范平台'

module.exports = {
  devServer: {
    open: false,        // 是否自动打开浏览器页面
    host: 'localhost',    // 指定使用一个 host，默认是 localhost
    // host: '**************',    // 指定使用一个 host，默认是 localhost
    port: 8101,         // 端口地址
    https: false,       // 使用https提供服务
    // 这里写你调用接口的基础路径，来解决跨域，如果设置了代理，那你本地开发环境的axios的baseUrl要写为 '' ，即空字符串
    proxy: 'http://test.xiuft.com:83',
    // proxy: 'http://hcssh.icsb.shop'
},
  assetsDir: 'assets',
  runtimeCompiler: true,
  productionSourceMap: false,
  lintOnSave: false,
  css: {
    loaderOptions: {
      postcss: {
        plugins: [
          postcss
        ]
      }
    }
  },
  configureWebpack: {
    name: name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    plugins: [
      new CompressionPlugin({
        cache: false,                   // 不启用文件缓存
        test: /\.(js|css|html)?$/i,     // 压缩文件格式
        filename: '[path].gz[query]',   // 压缩后的文件名
        algorithm: 'gzip',              // 使用gzip压缩
        minRatio: 0.8                   // 压缩率小于1才会压缩
      })
    ],
  },
  chainWebpack(config) {
    config.plugin('html').tap((args) => { //标题
      args[0].title = '长三角算力资源一体化调度示范平台';
      return args;
    })
  },
}
