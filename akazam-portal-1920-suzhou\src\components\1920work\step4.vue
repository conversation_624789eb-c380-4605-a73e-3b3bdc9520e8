<template>
  <div class="pageWork4 flex-box-between">
    <div class="leftStep flex-box">
      <div class="stepList flex-box">
        <div class="item on al" @click="endFun(1)">需求输入</div>
        <div class="arraw on al"></div>
        <div class="item on al" @click="endFun(2)">资源池分配</div>
        <div class="arraw on al"></div>
        <div class="item on" @click="endFun(3)">运行环境设置</div>
        <div class="arraw on al"></div>
        <div class="item on al" @click="endFun(4)">模型训练</div>
        <div class="arraw on al"></div>
        <div class="item on flex-box-center">
          <div class="icon"></div>推理部署
        </div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(7)">验证访问</div>
      </div>
      <div class="wordList">
        <p :class="wordAni1 ? 'ani' : ''">应用部署文件：Image Application Build </p>
        <p :class="wordAni2 ? 'ani' : ''" class="flex-box">
          <i v-if="iconAni1" class="el-icon-loading"></i>
          <img v-if="iconAni2" src="~@/assets/images/page4/step1/success.png" alt="">
          {{ word2 }}
        </p>
        <p :class="wordAni3 ? 'ani' : ''" class="flex-box">
          <i v-if="iconAni3" class="el-icon-loading"></i>
          <img v-if="iconAni4" src="~@/assets/images/page4/step1/success.png" alt="">
          {{ word1 }}
        </p>
      </div>
    </div>
    <div class="rightCore">
      <div class="top">
        <div class="bg"></div>
        <div class="choose" :class="job3SelectRegion ? 'ani' : ''">
          <div class="img"></div>
          <div class="text">{{ job3SelectRegion }}</div>
        </div>
        <div class="title">部署资源池</div>
        <div class="list flex-box">
          <div class="name">阿里云</div>
          <div class="son flex-box" v-for="(item, index) in namelist3" :key="index"
            :class="(job3SelectRegion === '阿里云-华东1（杭州）' && item == '华东1（杭州）') || (job3SelectRegion === '阿里云-华东2（上海）' && item == '华东2（上海）') || (job3SelectRegion === '阿里云-华北2（北京）' && item == '华北2（北京）') ? 'on' : ''">
            <div class="color"></div>
            <div class="text">阿里云-{{ item }}</div>
          </div>
        </div>
        <div class="list flex-box">
          <div class="name">华为云</div>
          <div class="son flex-box" v-for="(item, index) in namelist2" :key="index"
            :class="(job3SelectRegion === '华为云-华东-上海一' && item == '华东-上海一') || (job3SelectRegion === '华为云-华北-北京四' && item == '华北-北京四') || (job3SelectRegion === '华为云-华北-乌兰察布一' && item == '华北-乌兰察布一') || (job3SelectRegion === '华为云-华南-广州' && item == '华南-广州') ? 'on' : ''">
            <div class="color"></div>
            <div class="text">华为云-{{ item }}</div>
          </div>
        </div>
        <div class="list flex-box">
          <div class="name">天翼云</div>
          <div class="son flex-box" v-for="(item, index) in namelist" :key="index"
            :class="(job3SelectRegion === '天翼云-绍兴2' && item == '绍兴2') || (job3SelectRegion === '天翼云-太原2' && item == '太原2') ? 'on' : ''">
            <div class="color"></div>
            <div class="text">天翼云-{{ item }}</div>
          </div>
        </div>
        <div class="lineCore flex-box" :class="lineAni ? 'ani' : ''">
          <div class="text">应用部署</div>
          <div class="line">
            <div class="inner" :style="{ 'width': `${percent}%` }"></div>
          </div>
        </div>
      </div>
      <div class="down flex-box-between">
        <div class="charts">
          <div class="line1" ref="line1"></div>
        </div>
        <div class="charts">
          <div class="line2" ref="line2"></div>
        </div>
        <div class="charts">
          <div class="line3" ref="line3"></div>
        </div>
        <div class="charts">
          <div class="line4" ref="line4"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getStore, setStore } from "@/common/util";
import * as echarts from 'echarts';

export default {
  name: 'workstep2',
  props: {
    showBs: {
      type: Boolean,
      default: false
    },
    job1SelectRegion: {
      type: String,
      default: '华为云-上海一'
    },
    job1SelectType: {
      type: String,
      default: '智能推荐'
    },
    job2SelectRegion: {
      type: String,
      default: '天翼云-苏州(合营)'
    },
    job2SelectType: {
      type: String,
      default: '智能推荐'
    },
    job3SelectRegion: {
      type: String,
      default: '天翼云-太原2'
    },
  },
  data() {
    return {
      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      wordAni4: false,
      wordAni5: false,
      chartsMap: null,
      tabIndex: null,
      sonIndex: null,
      chartsline1: null,
      chartsline2: null,
      chartsline3: null,
      chartsline4: null,
      tabsAni: null,
      barBoxAni: null,
      lineAni: null,
      lineWordAni: null,
      tabsTimer: null,
      item1Ani: null,
      item2Ani: null,
      item3Ani: null,
      item4Ani: null,
      sonTimer: null,
      timerpercent: null,
      percent: 0,
      num1: 1.99,
      num2: 2.99,
      sonIndexChoose: null,
      isAuto: null,
      // namelist: ['晋'],
      // namelist2: ['粤'],
      // namelist3: ['京', '粤', '浙', '沪'],
      namelist: ['太原2'],
      namelist2: ['华东-上海一', '华北-北京四', '华北-乌兰察布一', '华南-广州'],
      namelist3: ['华东1（杭州）', '华东2（上海）', '华北2（北京）'],
      namelist4: ['京', '沪', '渝', '粤', '苏', '川'],
      word1: '部署完成-外网访问',
      word2: '选择部署资源池',
      iconAni1: true,
      iconAni2: false,
      iconAni3: true,
      iconAni4: false,
      lineBoxAni: false
    }
  },
  created() {
    this.isAuto = this.GLOBAL.isAuto
  },
  mounted() {
    this.init()
  },
  methods: {
    secondFun() {
      this.wordAni3 = true
      this.wordAni4 = true
      this.lineAniFun()
      this.init1(true)
      this.init2(true)
      this.init3(true)
      this.init4(true)
      setTimeout(() => {
        this.iconAni3 = false
        this.iconAni4 = true
        this.$emit('stepEnd', 7)
      }, 2000);
    },
    numAni() {
      for (let i = 0; i < 20; i++) {
        setTimeout(() => {
          if (!this.num1 || !this.num2) {
            return false
          }
          this.num1 = (Math.random() * 10).toFixed(2)
          this.num2 = (Math.random() * 10).toFixed(2)
          if (i > 18) {
            this.num1 = 1.99
            this.num2 = 2.99
          }
        }, 500 * i);
      }
    },
    init() {
      let settimer1 = setTimeout(() => {
        this.wordAni1 = true
      }, 600);
      let settimer2 = setTimeout(() => {
        this.wordAni2 = true
      }, 1200);
      let settimer3 = setTimeout(() => {
        this.iconAni1 = false
        this.iconAni2 = true
        // this.sizeAniFun()
        this.lineAni = true
      }, 2000);
      let settimer4 = setTimeout(() => {
        this.init1(false)
        this.init2(false)
        this.init3(false)
        this.init4(false)
      }, 3000);
      let settimer5 = setTimeout(() => {
      }, 2500);
      let settimer6 = setTimeout(() => {
      }, 4000);
      let settimer7 = setTimeout(() => {
      }, 4500);
      let settimer8 = setTimeout(() => {

      }, 5000);
      let settimer9 = setTimeout(() => {
      }, 5500);

      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
        clearTimeout(settimer8)
        settimer8 = null;
        clearTimeout(settimer9)
        settimer9 = null;
      })
    },
    endFun(index) {
      if (this.isAuto) {
        return false
      }
      this.$emit('endFun', index)
    },
    tabsAniFun() {
      this.tabIndex = 0
      this.tabsTimer = setInterval(() => {
        this.tabIndex += 1
        if (this.tabIndex > 3) {
          this.tabIndex = 0
        }
      }, 400);
      setTimeout(() => {
        clearInterval(this.tabsTimer)
      }, 3200);
    },
    sizeAniFun() {
      this.sonIndexChoose = 0
      this.sonTimer = setInterval(() => {
        this.sonIndexChoose += 1
        if (this.sonIndexChoose > 5) {
          this.sonIndexChoose = 5
          clearInterval(this.sonTimer)
        }
      }, 800);
    },
    lineAniFun() {
      this.timerpercent = setInterval(() => {
        this.percent += 1
        if (this.percent >= 100) {
          clearInterval(this.timerpercent)
        }
      }, 20);
    },
    init1(isData) {
      let option = {
        title: {
          text: '网络吞吐量',
          textStyle: {
            color: 'rgba(255,255,255,1)',
            fontSize: this.GLOBAL.relPx(16),
            fontWeight: 'normal'
          },
          left: '1%',
          top: '5%'
        },
        grid: {
          left: '40',
          right: '20',
          top: '50',
          bottom: '20',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['9:00', '9:30', '10:00', '10:30', '11:00'],
          axisLabel: {
            color: 'rgba(255,255,255,0.8)'
          },
          axisLine: {
            lineStyle: {
              width: 0.5,
            }
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 2,
          min: 0,
          max: 100,
          axisLabel: {
            color: 'rgba(255,255,255,0.8)'
          },
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [
          {
            data: isData ? [50, 90, 20, 70, 90] : [0, 0, 0, 0, 0],
            type: 'line',
            areaStyle: {
              normal: {
                // 渐变填充色（线条下半部分）
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(50, 94, 252,0.3)" },
                  { offset: 1, color: "rgba(50, 94, 252,0.05)" }
                ])
              }
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: "rgba(50, 94, 252,0.3)"
                }
              }
            },
            symbol: "none",
            smooth: true,
          }
        ]
      }
      // 内存泄漏 无dom 不执行
      if (!this.chartsline1) {
        this.chartsline1 = echarts.init(this.$refs.line1, null, { width: this.GLOBAL.relPx(580), height: this.GLOBAL.relPx(170) });
      }
      this.chartsline1.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsline1.setOption(option);
    },
    init2(isData) {
      let option = {
        title: {
          text: '内存利用率',
          textStyle: {
            color: 'rgba(255,255,255,1)',
            fontSize: this.GLOBAL.relPx(16),
            fontWeight: 'normal'
          },
          left: '1%',
          top: '5%'
        },
        grid: {
          left: '40',
          right: '20',
          top: '50',
          bottom: '20',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['9:00', '9:30', '10:00', '10:30', '11:00'],
          axisLabel: {
            color: 'rgba(255,255,255,0.8)'
          },
          axisLine: {
            lineStyle: {
              width: 0.5,
            }
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 2,
          min: 0,
          max: 100,
          axisLabel: {
            color: 'rgba(255,255,255,0.8)'
          },
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [
          {
            data: isData ? [80, 60, 40, 30, 50] : [0, 0, 0, 0, 0],
            type: 'line',
            areaStyle: {
              normal: {
                // 渐变填充色（线条下半部分）
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(50, 94, 252,0.3)" },
                  { offset: 1, color: "rgba(50, 94, 252,0.05)" }
                ])
              }
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: "rgba(50, 94, 252,0.3)"
                }
              }
            },
            symbol: "none",
            smooth: true,
          }
        ]
      }
      // 内存泄漏 无dom 不执行
      if (!this.chartsline2) {
        this.chartsline2 = echarts.init(this.$refs.line2, null, { width: this.GLOBAL.relPx(580), height: this.GLOBAL.relPx(170) });
      }
      this.chartsline2.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsline2.setOption(option);
    },
    init3(isData) {
      let option = {
        title: {
          text: '磁盘IO',
          textStyle: {
            color: 'rgba(255,255,255,1)',
            fontSize: this.GLOBAL.relPx(16),
            fontWeight: 'normal'
          },
          left: '1%',
          top: '5%'
        },
        grid: {
          left: '40',
          right: '20',
          top: '50',
          bottom: '20',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['9:00', '9:30', '10:00', '10:30', '11:00'],
          axisLabel: {
            color: 'rgba(255,255,255,0.8)'
          },
          axisLine: {
            lineStyle: {
              width: 0.5,
            }
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 2,
          min: 0,
          max: 100,
          axisLabel: {
            color: 'rgba(255,255,255,0.8)'
          },
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [
          {
            data: isData ? [40, 50, 40, 60, 80] : [0, 0, 0, 0, 0],
            type: 'line',
            areaStyle: {
              normal: {
                // 渐变填充色（线条下半部分）
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(43, 193, 255,0.3)" },
                  { offset: 1, color: "rgba(43, 193, 255,0.05)" }
                ])
              }
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: "rgba(43, 193, 255,0.3)"
                }
              }
            },
            symbol: "none",
            smooth: true,
          }
        ]
      }
      // 内存泄漏 无dom 不执行
      if (!this.chartsline3) {
        this.chartsline3 = echarts.init(this.$refs.line3, null, { width: this.GLOBAL.relPx(580), height: this.GLOBAL.relPx(170) });
      }
      this.chartsline3.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsline3.setOption(option);
    },
    init4(isData) {
      let option = {
        title: {
          text: 'CPU利用率',
          textStyle: {
            color: 'rgba(255,255,255,1)',
            fontSize: this.GLOBAL.relPx(16),
            fontWeight: 'normal'
          },
          left: '1%',
          top: '5%'
        },
        grid: {
          left: '40',
          right: '20',
          top: '50',
          bottom: '20',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['9:00', '9:30', '10:00', '10:30', '11:00'],
          axisLabel: {
            color: 'rgba(255,255,255,0.8)'
          },
          axisLine: {
            lineStyle: {
              width: 0.5,
            }
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 2,
          min: 0,
          max: 100,
          axisLabel: {
            color: 'rgba(255,255,255,0.8)'
          },
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [
          {
            data: isData ? [60, 30, 50, 40, 80] : [],
            type: 'line',
            areaStyle: {
              normal: {
                // 渐变填充色（线条下半部分）
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(43, 193, 255,0.3)" },
                  { offset: 1, color: "rgba(43, 193, 255,0.05)" }
                ])
              }
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: "rgba(43, 193, 255,0.3)"
                }
              }
            },
            symbol: "none",
            smooth: true,
          }
        ]
      }
      // 内存泄漏 无dom 不执行
      if (!this.chartsline4) {
        this.chartsline4 = echarts.init(this.$refs.line4, null, { width: this.GLOBAL.relPx(580), height: this.GLOBAL.relPx(170) });
      }
      this.chartsline4.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsline4.setOption(option);
    }
  },
  beforeDestroy() {
    if (this.chartsline1) {
      this.chartsline1.dispose()
    }
    if (this.chartsline2) {
      this.chartsline2.dispose()
    }
    if (this.tabsTimer) {
      clearInterval(this.tabsTimer)
    }
    if (this.sonTimer) {
      clearInterval(this.sonTimer)
    }
    if (this.timerpercent) {
      clearInterval(this.timerpercent)
    }
  },
}
</script>

<style lang="scss">
.pageWork4 {
  .rightCore {
    width: 1203px;
    position: relative;

    .bg {
      background-image: url('~@/assets/images/1920/work/step5/bg.png');
      background-size: contain;
      width: 639px;
      height: 380px;
      left: 270px;
      top: 0;
      z-index: 0;
      position: absolute;
      opacity: 0.5;
    }

    .choose {
      left: 510px;
      top: 120px;
      z-index: 3;
      position: absolute;
      opacity: 0;

      .img {
        width: 121px;
        height: 122px;
        background-image: url('~@/assets/images/1920/work/step5/item.png');
        background-size: contain;
        margin: 0 auto;
      }

      .text {
        font-size: 20px;
        color: #00E4FF;
        text-align: center;
        margin-top: 20px;
      }

      &.ani {
        animation: imgOpacity 1s linear 1 forwards;
      }
    }

    .top {
      height: 380px;
      background: rgba($color: #26262A, $alpha: 0.15);
      border-radius: 4px;

      .title {
        font-size: 18px;
        padding: 13px;
      }

      .list {
        padding-top: 20px;
        padding-left: 20px;

        .name {
          font-size: 16px;
          margin-right: 20px;
        }

        .son {
          margin-right: 40px;
          margin-top: 4px;

          .color {
            width: 16px;
            height: 16px;
            background-color: #1E283B;
            margin-right: 15px;
          }

          .text {
            font-size: 14px;
            line-height: 14px;
            margin-top: 1px;
            text-align: center;
            color: #666;
          }

          &.on {
            .color {
              background-color: #00E4FF;
            }

            .text {
              color: #ffffff;
            }
          }
        }
      }

      .lineCore {
        margin-top: 170px;
        margin-left: 26px;
        opacity: 0;

        .text {
          font-size: 16px;
          line-height: 16px;
        }

        .line {
          width: 1080px;
          height: 10px;
          background: rgba(17, 17, 17, 1);
          border-radius: 5px;
          margin: 0 auto;
          margin-top: 3px;
          margin-left: 10px;

          .inner {
            background-image: url('~@/assets/images/page4/step4/percent.png');
            width: 0%;
            height: 10px;
            border-radius: 5px;
          }



          &.on {
            width: 400px;
          }
        }

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }

    .down {
      height: 380px;
      background: rgba($color: #26262A, $alpha: 0.15);
      border-radius: 4px;
      margin-top: 30px;
    }
  }

}

@keyframes imgOpacity {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
</style>
