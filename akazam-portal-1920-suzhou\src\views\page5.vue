<template>
  <div class="pageCommon page5">
    <!-- 头部-s -->
    <header-common :icon="2" :name="'全国一体化算力网络'"></header-common>
    <!-- 头部-e -->
    <div class="main flex-box-between">
      <div class="dialogMap">
        <div class="map" ref="dialogMapEarthRef" v-show="tabIndex == 3"></div>
        <div class="map" ref="dialogMap" v-show="tabIndex != 3"></div>
        <div class="titleCore flex-box-between">
          <div class="selectCore flex-box">
            <!-- <div class="item flex-box">
              <div class="label">源城市：</div>
              <div class="selectBox">
                <el-select v-model="value" placeholder="请选择" class="select" @change="startChangeFun">
                  <el-option v-for="(item, index) in tabIndex === 1 ? mainList : tabIndex === 2 ? mainList2 : mainList3"
                    :key="index" :label="item.xname" :value="index">
                  </el-option>
                </el-select>
              </div>
            </div> -->
            <div class="item flex-box">
              <div class="label">目的城市：</div>
              <div class="selectBox">
                <el-select v-model="value2" placeholder="请选择" class="select" @change="endChangeFun" multiple
                  collapse-tags>
                  <el-checkbox class="myCheckBox flex-box-end" v-model="checked" @change='selectAll'>全选</el-checkbox>
                  <el-option v-for="(item, index) in tableData" :key="index" :label="item.ycity" :value="index">
                  </el-option>
                </el-select>
              </div>
            </div>
          </div>
        </div>
        <div class="tableList">
          <div class="ul flex-box-between">
            <div class="item w1">{{ startName }} 至 </div>
            <div class="item w2">源宿运营商</div>
            <div class="item w3">ASPath</div>
            <div class="item w4">IP跳数</div>
            <div class="item w5">延迟(ms)</div>
            <div class="item w6">抖动(ms)</div>
            <div class="item w7">丢包(%)</div>
          </div>
          <div class="liList">
            <div class="li flex-box-between" v-for="(item, index) in tableData" :key="index"
              :class="index % 2 === 0 ? 'on' : ''" v-show="value2.indexOf(index) > -1">
              <div class="item w1">{{ item.ycity }}</div>
              <div class="item w2">{{ item.isp || '-' }}</div>
              <div class="item w3">{{ item.asnum || '-' }}</div>
              <div class="item w4">{{ item.dump || '-' }}</div>
              <div class="item w5">{{ item.delay || 0 }}</div>
              <div class="item w6">{{ item.jitter || 0 }}</div>
              <div class="item w7">{{ item.lost || 0 }}%</div>
            </div>
          </div>
        </div>
      </div>
      <div class="centerRight">
        <div class="top">
          <div class="titleCore flex-box">
            <div class="icon"><img src="~@/assets/images/page5/title.png"></div>
            <div class="title">{{ chooseCityName }}</div>
          </div>
          <div class="hlCore">
            <!-- <div class="hl1"></div>
              <div class="hl2"></div>
              <div class="hl3"></div>
              <div class="hl4"></div> -->
            <div class="hlms1">{{ hllist4ms }}(ms)</div>
            <div class="hlms2">{{ hllist3ms }}(ms)</div>
            <div class="hlms3">{{ hllist2ms }}(ms)</div>
            <div class="hlms4">{{ hllist1ms }}(ms)</div>
            <div class="hlname1">{{ hllist4 }}</div>
            <div class="hlname2">{{ hllist3 }}</div>
            <div class="hlname3">{{ hllist2 }}</div>
            <div class="hlname4">{{ hllist1 }}</div>
          </div>
        </div>
        <div class="down flex-box-between">
          <div class="item flex-box-center" :class="tabIndex == 4 ? 'on' : ''" @click="tabChange(4)">
            <div class="icon"><img src="~@/assets/images/page5/r-icon2.png"></div>
            <div class="text">算力时延圈</div>
          </div>
          <!-- <div class="item flex-box-center" :class="tabIndex == 1 ? 'on' : ''" @click="tabChange(1)">
            <div class="icon"><img src="~@/assets/images/page5/r-icon1.png"></div>
            <div class="text">算力网络</div>
          </div> -->
          <!-- <div class="item flex-box-center" :class="tabIndex == 2 ? 'on' : ''" @click="tabChange(2)">
            <div class="icon"><img src="~@/assets/images/page5/r-icon2.png"></div>
            <div class="text">多云DCI</div>
          </div> -->
          <div class="item flex-box-center" :class="tabIndex == 3 ? 'on' : ''" @click="tabChange(3)">
            <div class="icon"><img src="~@/assets/images/page5/r-icon3.png"></div>
            <div class="text">云间互联</div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="topFour flex-box-between">
          <div class="son">
            <div class="title">全国数据中心连接骨干网比例</div>
            <div class="value">55.50<span>%</span></div>
          </div>
          <div class="son">
            <div class="title">大型以上数据中心连接骨干网比例</div>
            <div class="value">69.00<span>%</span></div>
          </div>
          <div class="son">
            <div class="title">在用数据中心出口带宽</div>
            <div class="value">489<span>G(bit/s)</span></div>
          </div>
          <div class="son">
            <div class="title">在用单机架带宽</div>
            <div class="value">608.9<span>M(bit/s)</span></div>
          </div>
        </div>

        <div class="bottomRight">数据来源:中国信息通信研究院、公开数据统计 </div>
      </div>
      <div class="leftCore" v-if="false">
        <div class="top flex-box-between">
          <div class="item">
            <div class="title">大通道专线</div>
            <div class="topval">
              <div class="value">87<span>根</span></div>
              <div class="line"></div>
              <div class="topvaldown flex-box-between">
                <div class="son flex-box">
                  <div class="icon"><img src="~@/assets/images/1920/page5/icon1.png" alt=""></div>
                  <div class="text">云专网：45根</div>
                </div>
                <div class="son flex-box">
                  <div class="icon"><img src="~@/assets/images/1920/page5/icon2.png" alt=""></div>
                  <div class="text">本地入云：42根</div>
                </div>
              </div>
            </div>
          </div>
          <div class="item">
            <div class="title">大通道总带宽</div>
            <div class="value">640<i>+</i><span>G</span></div>
          </div>
        </div>
        <div class="downMapCore">
          <div class="top">
            <div class="title">上海数据中心出口带宽</div>
            <div class="tabs flex-box-between">
              <div class="label" @click="outTypeFun">{{ outType }}：<i class="el-icon-arrow-down"></i></div>
              <!-- <el-select v-model="outType" placeholder="请选择" class="select" @change="startChangeFun">
                <el-option key="163出口带宽" value="163出口带宽" label="163出口带宽"></el-option>
                <el-option key="城域网出口带宽" value="城域网出口带宽" label="城域网出口带宽"></el-option>
              </el-select> -->
              <div class="tabsflex flex-box" v-if="outType == '163出口带宽'">
                <div class="itemttp" :class="mapIndex === 1 ? 'on' : ''" @click="mapIndexChangeFun(1, 1)">&gt;200G</div>
                <div class="itemttp" :class="mapIndex === 2 ? 'on' : ''" @click="mapIndexChangeFun(2, 1)">200-800G</div>
                <div class="itemttp" :class="mapIndex === 3 ? 'on' : ''" @click="mapIndexChangeFun(3, 1)">≥800G</div>
              </div>
              <div class="tabsflex flex-box" v-else>
                <div class="itemttp on" @click="mapIndexChangeFun(1, 2)">≥800G</div>
              </div>
            </div>
          </div>
          <div class="down">
            <!-- <div class="map" ref="map"></div> -->
            <div class="roomList" v-if="outType == '163出口带宽'">
              <vue-seamless-scroll :data="list163[mapIndex - 1]" class="seamless-warp" :class-option="classOption">
                <div class="item" v-for="( item, index ) in  list163[mapIndex - 1] " :key="index">
                  <div class="title">{{ item.name }}</div>
                  <div class="ul flex-box">
                    <div class="li">机架：{{ item.num }}个</div>
                    <div class="li">出口带宽：{{ item.g }}G</div>
                  </div>
                </div>
              </vue-seamless-scroll>
            </div>
            <div class="roomList" v-else>
              <vue-seamless-scroll :data="listCy" class="seamless-warp" :class-option="classOption">
                <div class="item" v-for="( item, index ) in  listCy " :key="index">
                  <div class="title">{{ item.name }}</div>
                  <div class="ul flex-box">
                    <div class="li">机架：{{ item.num }}个</div>
                    <div class="li">出口带宽：{{ item.g }}G</div>
                  </div>
                </div>
              </vue-seamless-scroll>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mapDialog" v-show="showmapDialog">
      <div class="flex-box-end">
        <div class="close" @click.stop="showmapDialog = false"></div>
      </div>
      <div class="map" ref="mapShanghai"></div>
    </div>
    <!-- <div class="center flex-box-between">
      <div>
        <div class="centerCore" ref="scrollContainer" v-show="tabIndex === 1">
          <div class="topProvice flex-box">
            <div class="item" v-for="( item, index ) in  mainList " :key="index"><span>{{ item.xname }}</span></div>
          </div>
          <div class="downProvice">
            <div class="item flex-box" v-for="( item, index ) in  mainList " :key="index">
              <div class="xname" @click="centerChoose2(item, index)"><span>{{ item.xname }}</span></div>
              <div class="son" v-for="( son, index2 ) in  item.list " :key="index2">
                <div class="box" v-show="lengedIndex == 1" :class="index == 7 && index2 == 2 ? 'on' : ''">{{
                  son.delay || 0
                }}<span></span>
                </div>
                <div class="box" v-show="lengedIndex == 2" :class="index == 8 && index2 == 5 ? 'on' : ''">{{
                  son.jitter || 0
                }}<span></span>
                </div>
                <div class="box" v-show="lengedIndex == 3" :class="index == 6 && index2 == 7 ? 'on' : ''">{{
                  son.lost || 0
                }}<span></span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="centerCore c2" ref="scrollContainer" v-show="tabIndex === 2">
          <div class="topProvice flex-box">
            <div class="item" v-for="( item, index ) in  mainList2 " :key="index"><span>{{ item.xname }}</span></div>
          </div>
          <div class="downProvice">
            <div class="item flex-box" v-for="( item, index ) in  mainList2 " :key="index">
              <div class="xname" @click="centerChoose(item, index)"><span>{{ item.xname }}</span></div>
              <div class="son" v-for="( son, index2 ) in  item.list " :key="index2">
                <div class="box" v-show="lengedIndex == 1" :class="index == 3 && index2 == 10 ? 'on' : ''">{{
                  son.delay || 0
                }}<span></span>
                </div>
                <div class="box" v-show="lengedIndex == 2" :class="index == 3 && index2 == 1 ? 'on' : ''">{{
                  son.jitter || 0
                }}<span></span>
                </div>
                <div class="box" v-show="lengedIndex == 3" :class="index == 1 && index2 == 13 ? 'on' : ''">{{
                  son.lost || 0
                }}<span></span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="centerCore c3" ref="scrollContainer" v-show="tabIndex === 3">
          <div class="topProvice flex-box">
            <div class="item" v-for="( item, index ) in  mainList3 " :key="index"><span>{{ item.xname }}</span></div>
          </div>
          <div class="downProvice">
            <div class="item flex-box" v-for="( item, index ) in  mainList3 " :key="index">
              <div class="xname" @click="centerChoose(item, index)"><span>{{ item.xname }}</span></div>
              <div class="son" v-for="( son, index2 ) in  item.list " :key="index2">
                <div class="box" v-show="lengedIndex == 1" :class="index == 8 && index2 == 3 ? 'on' : ''">{{
                  son.delay || 0
                }}<span></span>
                </div>
                <div class="box" v-show="lengedIndex == 2" :class="index == 6 && index2 == 8 ? 'on' : ''">{{
                  son.jitter || 0
                }}<span></span>
                </div>
                <div class="box" v-show="lengedIndex == 3" :class="index == 7 && index2 == 7 ? 'on' : ''">{{
                  son.lost || 0
                }}<span></span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="centerLenged flex-box-center">
          <div class="item flex-box" :class="lengedIndex == 1 ? 'on' : ''" @click="lengedChange(1)">
            <div class="color"></div>
            <div class="text">时延(ms)</div>
          </div>
          <div class="item flex-box" :class="lengedIndex == 2 ? 'on' : ''" @click="lengedChange(2)">
            <div class="color"></div>
            <div class="text">抖动(ms)</div>
          </div>
          <div class="item flex-box" :class="lengedIndex == 3 ? 'on' : ''" @click="lengedChange(3)">
            <div class="color"></div>
            <div class="text">丢包(%)</div>
          </div>
        </div>
      </div>

    </div> -->
  </div>
</template>


<script>
import headerCommon from '../components/header/Index'
import dataJson from '@/assets/json/cloud_matrix.json'
import dataJsonday2 from '@/assets/json/cloud_matrix_day2.json'
import dataJson2 from '@/assets/json/cloud_matrix2.json'
import dataJson3 from '@/assets/json/cloud_matrix3.json'
import dataJson4 from '@/assets/json/yzw.json'
import * as echarts from 'echarts';
import chinaMap from '@/assets/json/chinaMap.json'
import geoCoordMap from '@/assets/json/geoCoordMap.json'
import worldMap from '@/assets/json/world.json'
import shanghaiMap from '@/assets/json/shanghai.json'
import vueSeamlessScroll from 'vue-seamless-scroll'

export default {
  components: {
    headerCommon,
    vueSeamlessScroll
  },
  data() {
    return {
      tabIndex: 0,
      mainList: dataJson.data,
      mainList2: dataJson2.data,
      mainList3: dataJson3.data,
      lengedIndex: 1,
      showDialog: false,
      dialogMap: null,
      value: 0,
      value2: [],
      tableData: [],
      startName: '',
      mapIndex: 2,
      hllist1: '',
      hllist2: '',
      hllist3: '',
      hllist4: '',
      hllist1ms: 0,
      hllist2ms: 0,
      hllist3ms: 0,
      hllist4ms: 0,
      chooseCityName: '',
      checked: true,
      dialogMapEarth: null,
      list163: [
        [{ name: '北艾大楼', g: 100, num: 646, value: [121.54284, 31.18961] }, { name: '桂箐数据中心大楼', g: 120, num: 391, value: [121.41127, 31.17118] }, { name: '国定数据中心大楼', g: 160, num: 582, value: [121.51530, 31.30272] }, { name: '呼兰数据中心大楼', g: 60, num: 476, value: [121.45707, 31.35104] }, { name: '纪蕰数据中心大楼', g: 120, num: 402, value: [121.44280, 31.34771] }, { name: '欧阳数据中心大楼', g: 80, num: 724, value: [121.49491, 31.27279] }, { name: '浦川大楼', g: 40, num: 555, value: [121.68401, 31.25773] }, { name: '金海大楼', g: 6, num: 96, value: [121.62954, 31.26740] }, { name: '蕰川数据中心四期大楼', g: 80, num: 1926, value: [121.34857, 31.49385] }, { name: '同普大楼', g: 20, num: 211, value: [121.38270, 31.23619] }, { name: '新桃浦大楼', g: 40, num: 108, value: [121.36387, 31.28687] }, { name: '信息枢纽大楼', g: 4, num: 92, value: [121.51733, 31.24125] }, { name: '信息园区B1楼', g: 40, num: 425, value: [121.54202, 31.13398] }, { name: '信息园区B24B楼', g: 40, num: 1654, value: [121.54242, 31.13235] }, { name: '信息园区B7楼', g: 120, num: 841, value: [121.54416, 31.13237] }, { name: '张东1号楼', g: 120, num: 340, value: [121.64228, 31.22177] }, { name: '周家渡数据中心', g: 40, num: 156, value: [121.50354, 31.18507] }],
        [{ name: '安晓数据中心', g: 400, num: 1901, value: [121.24404, 31.31952] }, { name: '富特数据中心大楼', g: 800, num: 972, value: [121.61088, 31.32671] }, { name: '富特数据中心三期大楼', g: 200, num: 2412, value: [121.61170, 31.32667] }, { name: '桂桥数据中心大楼', g: 280, num: 299, value: [121.63456, 31.25358] }, { name: '华信大楼', g: 440, num: 1680, value: [121.60671, 31.33901] }, { name: '建安数据中心大楼', g: 320, num: 2572, value: [121.10331, 30.86593] }, { name: '金京数据中心大楼', g: 480, num: 1819, value: [121.62213, 31.28442] }, { name: '钦州大楼', g: 480, num: 204, value: [121.40764, 31.18305] }, { name: '兴顺数据中心', g: 400, num: 2551, value: [121.19049, 31.44915] }, { name: '信息园区B2', g: 280, num: 1181, value: [121.54242, 31.13235] }, { name: '真如1号楼', g: 520, num: 234, value: [121.39931, 31.28430] }, { name: '真如3号楼', g: 400, num: 1553, value: [121.39900, 31.28432] }],
        [{ name: '蕰川数据中心二期大楼', g: 2760, num: 3956, value: [121.34857, 31.49385] }, { name: '信息园区B15A楼', g: 1200, num: 1378, value: [121.54242, 31.13235] }, { name: '卓青数据中心大楼', g: 2800, num: 3217, value: [121.09785, 31.18554] }, { name: '漕盈数据中心1号楼', g: 4000, num: 1096, value: [121.10219, 31.19123] }, { name: '信息园区B14B楼', g: 1400, num: 292, value: [121.54202, 31.13398] }]
      ],
      listCy: [{ name: '华京（北块局大楼）', g: 800, num: 920, value: [121.61712, 31.32784] }, { name: '华信大楼', g: 2000, num: 1680, value: [121.60671, 31.33901] }, { name: '浦川大楼', g: 1200, num: 555, value: [121.68401, 31.25773] }, { name: '真如1号楼', g: 950, num: 234, value: [121.39931, 31.28430] }],
      outType: '163出口带宽',
      chartsMapShanghai: null,
      showmapDialog: false
    };
  },
  filters: {

  },
  computed: {
    classOption() {
      return {
        step: 0.5, // 数值越大速度滚动越快
        limitMoveNum: 2, // 开始无缝滚动的数据量 
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) 
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) 
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      }
    }
  },
  mounted() {
    let index = 1
    if (this.$route.query && this.$route.query.index) {
      index = this.$route.query.index
    }
    this.tabChange(Number(index))
    // this.initMap()
    // this.centerChoose(this.mainList[0])
    // this.centerChoose2(this.mainList[0], 0)
    // this.initMapShanghai()
  },
  created() {
    this.dayInitData()
    echarts.registerMap('china', { geoJSON: chinaMap })
    echarts.registerMap('world', { geoJSON: worldMap })
    echarts.registerMap('shanghai', { geoJSON: shanghaiMap })
    // let str = `阿比让:[5.19,4.01]/阿布扎比:[24.27,54.23]/阿布贾:[9.12,7.11]/阿卡普尔科:[16.51,-99.56]/阿克拉:[5.33,0.15]/艾达克岛:[51.52,176.39]/亚当斯敦:[-25.04,-130.05]/亚的斯亚贝巴:[9.03,38.42]/阿得莱德:[-34.56,138.36]/亚丁:[12.5,45]/阿格拉:[27.09,78]/阿瓜斯卡连特斯:[21.51,-102.18]/艾哈迈达巴德:[23.03,72.4]/阿科隆:[41.04,-81.31]/吉萨:[30.01,31.12]/奥尔巴尼:[42.4,-73.47]/阿尔布开克:[35.07,-106.4]/亚历山大:[31.13,29.55]/阿尔及尔:[36.42,3.13]/阿拉木图:[43.19,76.55]/阿洛菲:[-19.03,-169.55]/安汶:[-4.5,128.1]/安曼:[31.57,35.56]/阿姆斯特丹:[52.21,4.52]/阿纳德尔:[64.4,177.32]/阿纳海姆:[33.5,-117.52]/安克雷奇:[61.13,-149.52]/安道尔:[42.3,1.31]/安卡拉:[40.02,32.54]/塔那那利佛:[-18.55,47.31]/阿皮亚:[-13.48,-171.45]/阿克陶:[44.31,50.16]/阿克托贝:[50.17,57.1]/阿灵顿:[32.41,-97.07]/阿什哈巴德:[37.58,-58.24]/阿斯马拉:[15.2,-38.58]/阿斯塔纳:[51.1,71.3]/亚松森:[-25.15,-57.4]/雅典:[38.02,23.44]/亚特兰大:[33.46,-84.25]/奥克兰:[-36.55,174.45]/奥克斯塔:[44.19,-69.46]/奥罗拉:[39.42,-104.43]/奥斯丁:[30.17,-97.44]/亚速尔群岛:[38.3,-28]/巴格达:[33.14,44.22]/巴库:[40.22,49.53]/巴厘巴板:[-1.15,116.5]/巴尔的摩:[39.17,-76.37]/巴马科:[12.4,-7.59]/斯里巴加湾港:[4.56,114.58]/万隆:[-6.57,107.34]/班加罗尔:[12.58,77.34]/曼谷:[13.5,100.29]/班吉:[4.23,18.37]/班珠尔:[13.28,-16.39]/巴塞罗那:[41.18,2.06]/巴尔瑙尔:[53.21,83.47]/巴塞尔:[47.34,7.36]/巴士拉:[30.3,47.49]/巴斯特尔:[16.14,-61.32]/巴斯特尔:[17.18,-62.43]/巴斯蒂亚:[42.41,9.26]/巴吞鲁日:[30.27,-91.08]/贝鲁特:[33.52,35.3]/贝尔法斯特:[54.36,-5.57]/贝尔格莱德:[44.49,20.28]/贝尔莫潘:[17.25,-88.46]/柏林:[52.31,13.2]/伯尔尼:[46.57,7.26]/伯利恒:[31.42,35.12]/布巴内斯:[20.15,85.5]/比林斯:[45.47,-108.27]/伯明翰:[52.3,-1.55]/伯明翰:[33.3,-86.55]/比什凯克:[42.53,74.46]/俾斯麦酒:[46.49,-100.47]/比绍:[11.52,-15.39]/勃朗峰-萨伯隆:[51.26,-57.08]/波哥大:[4.38,-74.05]/博伊西:[43.37,-116.13]/波士顿:[42.19,-71.05]/布雷德斯:[16.46,-62.12]/布兰普顿:[43.41,-79.46]/巴西利亚:[-15.45,-47.57]/布拉迪斯拉发:[48.09,17.07]/布拉柴维尔:[-4.14,15.14]/布里奇顿:[13.06,-59.37]/布里斯班:[-27.28,153.02]/布鲁塞尔:[50.51,4.21]/布加勒斯特:[44.23,26.1]/布达佩斯:[47.26,19.15]/布宜诺斯艾利斯:[-34.2,-58.3]/布法罗:[42.52,-78.55]/布琼布拉:[-3.22,29.21]/开罗:[30.04,31.17]/卡尔加里:[51.05,-114.05]/卡利:[3.24,-76.3]/堪培拉:[-35.18,149.08]/坎昆:[21.1,-86.51]/开普敦:[-33.55,18.27]/加拉加斯:[10.3,-66.58]/加地夫:[51.28,-3.11]/卡森城:[39.1,-118.46]/卡萨布兰卡:[33.36,-7.37]/卡斯特里:[14.01,-60.59]/卡宴:[4.55,-52.18]/宿务岛:[10.17,123.54]/查尔斯顿:[38.23,-81.4]/夏洛特:[35.05,-80.5]/夏洛特敦:[46.14,-63.09]/查塔姆岛:[-44.0,-176.35]/车里雅宾斯克:[55.1,61.25]/钦奈:[13.05,80.18]/夏延文:[41.08,-104.49]/芝加哥:[41.51,-87.41]/奇瓦瓦:[28.38,-106.05]/基希讷乌:[47.02,28.5]/吉大港:[22.2,91.48]/乔巴山:[48.04,114.3]/基督城:[-43.32,172.37]/辛辛那提:[39.1,-84.3]/克利夫兰:[41.3,-81.41]/科伦坡:[6.55,79.52]/哥伦比亚:[34.01,-81]/哥伦布:[39.59,-82.59]/科纳克里:[9.3,-13.43]/康科特:[43.13,-71.32]/哥本哈根:[55.43,12.34]/科尔多瓦:[37.53,-4.46]/达喀尔:[14.38,-17.27]/达拉斯:[32.47,-96.47]/大马士革:[33.3,36.19]/达累斯萨拉姆:[-6.51,39.18]/达尔文:[-12.28,130.51]/德令哈:[28.4,77.14]/登巴萨:[-8.4,115.14]/丹佛:[39.43,-104.59]/得梅因:[41.36,-93.38]/底特律:[42.23,-83.05]/达卡:[23.51,90.24]/帝力:[-8.35,125.35]/吉布提:[12.0,42.5]/多多马:[-6.1,35.4]/多哈:[25.15,51.34]/多佛尔:[39.1,-75.32]/迪拜:[25.13,55.17]/都柏林:[53.26,-6.15]/德班:[-29.53,31.03]/杜尚别:[38.38,68.51]/杜塞尔多夫:[51.13,6.47]/复活节岛:[-27.05,-109.2]/爱丁堡:[55.57,-3.13]/埃德蒙顿:[53.34,-113.25]/阿尤恩:[27.09,-13.12]/埃尔帕索:[31.45,-106.29]/英德:[-8.51,121.4]/伊斯法罕:[32.42,51.4]/费尔班克斯:[64.5,-147.43]/费萨拉巴德:[31.25,73.09]/费尔南多-迪诺罗尼亚:[-3.54,-32.25]/法兰西堡:[14.36,-61.05]/福特沃斯:[32.45,-97.2]/法兰克福:[38.12,-84.52]/法兰克福:[50.02,8.34]/费里敦:[8.3,-13.17]/弗雷斯诺:[36.45,-119.45]/福冈:[33.39,130.21]/福纳佛提:[-8.31,179.13]/丰沙尔:[32.38,-16.54]/哈博罗内:[-24.45,25.55]/加拉帕戈斯群岛:[-0.54,-89.36]/甘比尔群岛:[-23.08,-134.57]/加蒂诺:[45.29,-75.4]/加沙:[31.3,34.28]/格但斯克:[54.22,18.38]/日内瓦:[46.14,6.04]/乔治敦:[6.46,-58.1]/乔治敦:[19.2,-81.23]/直布罗陀:[36.07,-5.22]/格拉斯哥:[55.52,-4.15]/瓜达拉哈拉:[20.4,-103.21]/关岛:[13.3,144.4]/危地马拉:[14.38,-90.22]/瓜亚基尔:[-2.13,-79.54]/哈利法克斯:[44.38,-63.35]/汉堡:[53.33,10]/汉密尔顿:[32.18,-64.47]/汉密尔顿:[43.15,-79.51]/河内:[21.01,105.53]/哈拉雷:[-17.49,31.04]/哈里斯堡:[40.16,-76.53]/哈特福德:[41.46,-72.41]/哈瓦那:[23.08,-82.23]/赫勒拿:[46.35,-112.02]/赫尔辛基:[60.1,24.53]/广岛:[34.23,132.27]/胡志明:[10.46,106.43]/荷伯特:[-42.54,147.18]/霍尼亚拉:[-9.32,160.12]/檀香山:[21.19,-157.5]/休斯顿:[29.45,-95.23]/科布多:[46.4,90.45]/海得拉巴:[17.22,78.26]/仁川:[37.3,126.38]/印第安纳波利斯:[39.47,-86.08]/印多尔:[22.42,75.54]/伊卡瑞特:[63.45,-68.3]/伊斯兰堡:[33.4,73.08]/伊斯坦布尔:[41.02,28.58]/伊兹密尔:[38.24,27.09]/杰克逊:[32.2,-90.11]/杰克逊维尔:[30.2,-81.4]/斋浦尔:[26.53,75.5]/雅加达:[-6.08,106.45]/查亚普拉:[-2.28,140.38]/吉达:[21.3,39.1]/杰斐逊城:[38.34,-92.11]/泽西城:[40.42,-74.03]/耶路撒冷:[31.47,35.13]/约翰内斯堡:[-26.08,27.54]/朱诺:[58.18,-134.25]/喀布尔:[34.3,69.1]/加里宁格勒:[54.43,20.3]/堪察加:[53.01,158.39]/坎帕拉:[0.19,32.35]/尼日利亚卡诺:[12.0,8.31]/坎普尔:[26.27,80.14]/堪萨斯城:[39.02,-94.33]/卡拉奇:[24.51,67.02]/加德满都餐厅:[27.42,85.19]/考纳斯:[54.54,23.54]/川崎:[35.32,139.43]/喀山:[55.45,49.1]/喀士穆:[15.34,32.36]/孔敬:[16.25,102.5]/库尔纳:[22.49,89.34]/基加利:[-1.59,30.05]/京斯敦:[-29.03,167.58]/京斯敦:[17.58,-76.48]/金斯敦:[13.12,-61.14]/金沙萨:[-4.18,15.18]/圣诞岛:[1.52,-157.2]/北九州:[33.52,130.49]/诺克斯维尔:[35.58,-83.56]/神户:[34.41,135.1]/加尔各答:[22.34,88.2]/科罗尔:[7.3,134.3]/克拉科夫:[50.03,19.55]/克拉斯诺亚尔斯克:[56.05,92.46]/吉隆坡:[3.08,101.42]/古邦:[-10.23,123.38]/科威特省:[29.2,48]/基辅:[50.28,30.29]/京都:[35.0,135.45]/拉柯鲁尼亚:[43.22,-8.24]/拉巴斯:[-16.3,-68.09]/拉普拉塔:[-34.55,-57.57]/拉各斯:[6.35,3.02]/拉合尔:[31.34,74.22]/拉斯帕尔马斯:[28.08,-15.27]/拉斯维加斯:[36.1,-115.1]/洛桑:[46.32,6.39]/拉瓦尔:[45.35,-73.45]/莱昂:[21.1,-101.42]/法耶特:[38.02,-84.27]/利伯维尔:[0.3,9.25]/利隆圭:[-13.58,33.49]/利马:[-12.06,-76.55]/林肯:[40.49,-96.4]/里斯本:[38.42,-9.05]/小石城:[34.44,-92.19]/利物浦:[53.25,-3]/卢布尔雅那:[46.03,14.31]/罗兹:[51.49,19.28]/洛美:[6.1,1.21]/伦敦:[42.59,-81.14]/伦敦:[51.3,-0.07]/长滩:[33.47,-118.09]/隆格伊:[45.32,-73.3]/豪勋爵岛:[-33.3,159]/洛杉矶:[34.05,-118.22]/路易斯维尔:[38.13,-85.48]/罗安达:[-8.5,13.2]/卢本巴希:[-11.44,27.29]/勒克瑙:[26.5,80.54]/鲁得希阿那:[30.56,75.52]/卢萨卡:[-15.2,28.14]/卢森堡:[49.37,6.08]/麦迪逊:[43.05,-89.23]/马德里:[40.26,-3.42]/马杜赖:[9.55,78.07]/马朱罗:[7.09,171.12]/麦加:[21.26,39.49]/马拉博:[3.45,8.48]/马朗:[-7.59,112.45]/马累:[4.1,73.28]/马穆楚:[-12.47,45.14]/美娜多:[1.3,124.58]/马那瓜:[12.06,-86.18]/麦纳麦:[26.12,50.36]/马瑙斯:[-3.06,-60]/马尼拉:[14.37,121]/马普托:[-25.58,32.35]/马德普拉塔:[-38.0,-57.32]/马卡姆:[43.49,-79.19]/马塞卢:[-29.18,27.28]/马塔兰:[-8.36,116.07]/马萨特兰:[23.13,-106.25]/姆巴巴纳:[-26.19,31.08]/棉兰:[3.35,98.39]/麦德林:[6.15,-75.36]/墨尔本:[-37.49,144.58]/孟菲斯:[35.05,-90]/门多萨:[-32.54,-68.5]/梅里达:[20.58,-89.37]/梅萨:[33.25,-111.44]/墨西卡利:[32.38,-115.27]/墨西哥城:[19.28,-99.09]/迈阿密:[25.47,-80.13]/米德兰:[32.0,-102.05]/中途岛:[28.13,-177.22]/米兰:[45.28,9.1]/密尔沃基雄鹿:[43.03,-87.57]/明尼阿波利斯:[45,-93.15]/明斯克:[53.51,27.3]/米西索加:[43.41,-79.36]/莫比尔:[30.4,-88.05]/摩加迪沙:[2.02,45.21]/摩纳哥:[43.4,7.25]/蒙罗维亚:[6.2,-10.46]/蒙特雷:[25.4,-100.2]/蒙得维的亚:[-34.53,-56.11]/蒙哥马利:[32.22,-86.2]/蒙彼利埃:[44.16,-72.34]/蒙特利尔:[45.3,-73.35]/莫罗尼:[-11.4,43.19]/莫斯科:[55.45,37.37]/孟买:[18.56,72.51]/慕尼黑:[48.08,11.35]/摩尔曼斯克:[68.59,33.08]/马斯喀特:[23.36,58.37]/名古屋:[35.1,136.55]/那格浦尔:[21.1,79.12]/内罗毕:[-1.17,36.49]/那不勒斯:[40.5,14.14]/纳什维尔:[36.1,-86.46]/拿骚:[25.03,-77.2]/恩贾梅纳:[12.1,14.59]/内乌肯:[-38.57,-68.04]/新德里:[28.37,77.13]/新奥尔良:[29.58,-90.05]/纽约:[40.44,-73.55]/纽瓦克:[40.43,-74.1]/尼亚美:[13.32,2.05]/尼斯:[43.42,7.16]/尼科西亚:[35.11,33.23]/下诺夫哥罗德:[56.2,44.01]/诺姆:[64.32,-165.24]/诺福克:[36.54,-76.18]/努瓦克肖特:[18.09,-15.58]/努美阿:[-22.16,166.27]/诺夫哥罗德:[58.3,31.2]/新西伯利亚:[55.04,82.55]/努库阿洛法:[-21.07,-175.12]/努克:[64.1,-51.4]/奥克兰:[37.47,-122.13]/敖德萨:[46.3,30.46]/冈山:[34.4,133.54]/俄克拉何马城:[35.29,-97.32]/鄂木斯克:[55.0,73.22]/奥拉涅斯塔克:[12.3,-69.58]/奥兰多:[28.3,-81.22]/大阪:[34.4,135.3]/奥斯陆:[59.56,10.41]/渥太华:[45.25,-75.43]/瓦加杜古:[12.2,-1.4]/帕果帕果:[-14.16,-170.42]/巨港:[-2.59,104.5]/波赫恩:[6.55,158.1]/帕尔马:[39.26,2.39]/巴拿马:[8.57,-79.3]/帕皮提:[-17.32,-149.34]/帕拉马里博:[5.52,-55.14]/巴黎:[48.51,2.2]/巴特那:[25.37,85.12]/彭沙科拉:[30.3,87.12]/彼尔姆:[58.01,56.1]/珀斯:[-31.58,115.49]/白沙瓦:[34.01,71.4]/费拉德尔菲亚:[40.0,-75.09]/金边:[11.35,104.55]/菲尼克斯:[33.3,-112.05]/皮尔:[44.22,-100.2]/匹兹堡:[40.26,-80]/波德戈里察:[42.27,19.28]/太子港:[18.32,-72.2]/法兰西港:[-49.21,70.13]/伊丽莎白港:[-33.57,25.36]/路易港:[-20.09,57.29]/莫尔兹比港:[-9.3,147.07]/西班牙港:[10.38,-61.31]/维拉港:[-17.44,168.19]/波特兰:[45.31,-122.39]/波尔图:[41.09,-8.37]/阿雷格里港:[-30.02,-51.14]/波多诺伏:[6.3,2.47]/波兹南:[52.25,16.53]/布拉格:[50.05,14.25]/普拉亚:[14.55,-23.31]/比勒陀利亚:[-25.43,28.11]/普里什蒂纳:[42.39,21.1]/普罗维登斯:[41.49,-71.25]/浦那:[18.34,73.58]/釜山:[35.05,129.02]/平壤:[39.0,125.47]/魁北克:[46.5,-71.15]/基多:[-0.14,-78.3]/拉巴:[-8.27,118.45]/拉巴特:[34.02,-6.51]/瑞丽:[35.47,-78.39]/拉皮德城:[44.05,-103.13]/拉罗汤加岛:[-21.2,-160.16]/拉瓦基:[-3.08,-171.05]/累西腓:[-8.06,-34.53]/里贾那:[50.3,-104.38]/雷克雅未克:[64.09,-21.58]/里士满:[37.32,-77.28]/里加:[56.53,24.05]/里奥布兰科:[-9.59,-67.49]/里约热内卢:[-22.54,-43.15]/河滨市:[33.56,-117.23]/利雅得:[24.39,46.44]/罗德城:[18.3,-64.3]/罗彻斯特:[43.12,-77.37]/罗马:[41.52,12.37]/罗萨里奥:[-32.57,-60.4]/罗索:[15.18,-61.23]/鹿特丹:[51.55,4.29]/梁赞:[54.37,39.45]/萨克拉门托:[38.34,-121.28]/圣蒂尼斯:[-20.52,55.28]/圣乔治:[12.04,-61.44]/圣赫利尔:[49.11,-2.07]/圣约翰:[45.16,-66.03]/圣约翰:[17.07,-61.51]/圣彼得堡:[59.55,30.25]/塞班岛:[15.12,145.45]/塞伦:[44.56,-123.02]/盐湖城:[40.46,-111.52]/萨尔塔:[-24.47,-65.24]/萨尔瓦多:[-12.58,-38.29]/萨尔茨保:[47.54,13.03]/萨马拉:[53.1,50.15]/圣安东尼奥:[29.25,-98.3]/圣博娜迪诺:[34.06,-117.17]/圣地亚哥:[32.43,-117.09]/旧金山:[37.46,-122.26]/圣何塞:[9.59,-84.04]/圣何塞:[37.2,-121.53]/圣胡安:[18.29,-66.08]/圣路易波托西:[22.09,-100.59]/圣马力诺:[43.55,12.28]/圣萨尔瓦多:[13.4,-89.1]/萨那:[15.23,44.14]/圣安那:[14.0,-89.31]/圣达菲:[35.4,-105.57]/圣地亚哥:[-33.26,-70.4]/圣多明各:[18.3,-69.57]/圣保罗:[-23.34,-46.38]/圣多美:[0.2,6.44]/扎幌:[43.05,141.21]/萨拉热窝:[43.52,18.26]/萨斯卡通:[52.1,-106.4]/西雅图:[47.38,-122.2]/三宝垄:[-6.58,110.29]/仙台:[38.16,140.52]/首尔:[37.35,127.03]/锡亚尔科特:[32.29,74.35]/新加坡:[1.22,103.45]/新卡拉雅:[-8.06,115.07]/苏福尔斯:[43.34,-96.42]/斯科普里:[41.35,21.3]/索非亚:[42.43,23.2]/圣约翰:[47.34,-52.41]/圣路易斯:[38.4,-90.15]/圣保罗:[45.0,-93.1]/圣彼得堡:[27.45,-82.38]/斯坦利:[-51.42,-57.52]/斯德哥尔摩:[59.23,18]/斯托克顿:[37.58,-121.18]/苏克雷:[-19.02,-65.16]/苏腊巴亚:[-7.14,112.45]/苏腊卡尔塔:[-7.32,110.5]/苏拉特:[21.1,72.54]/萨里:[49.11,-122.51]/苏瓦:[-18.08,178.25]/悉尼:[-33.55,151.17]/什切青:[53.25,14.32]/大丘:[35.52,128.36]/泰奥海伊:[-9.0,-139.3]/塔林:[59.22,24.48]/坦帕:[27.58,-82.38]/丹吉尔:[35.48,-5.45]/塔拉瓦:[1.25,173]/塔什干:[41.16,69.13]/第比利斯:[41.43,44.48]/特古西加尔巴:[14.05,-87.14]/德黑兰:[35.45,51.3]/特拉维夫:[32.05,34.46]/特尔纳特:[0.48,127.23]/新村:[-10.3,105.4]/瓦利:[-18.13,-63.04]/廷布:[-27.29,89.4]/特里凡得琅:[8.3,76.57]/提华纳:[32.32,-117.01]/地拉那:[41.2,19.48]/东京:[35.41,139.44]/托莱多:[41.4,-83.35]/托皮卡:[39.02,-95.41]/多伦多:[43.4,-79.22]/托沙芬:[62.01,-6.46]/特伦顿:[40.13,-74.46]/的黎波里:[32.58,13.12]/图森:[32.13,-110.58]/图库曼:[-26.3,-65.2]/突尼斯:[36.47,10.1]/都灵:[45.04,7.4]/秋明:[57.09,65.32]/乌法:[54.45,55.58]/乌兰巴托:[47.55,106.53]/安那拉斯加:[53.51,-166.43]/巴罗达:[22.19,73.14]/瓦杜兹:[47.09,9.31]/瓦莱塔:[35.53,14.31]/温哥华:[49.13,-123.06]/瓦腊纳西:[25.2,83]/梵蒂冈城:[41.54,12.27]/威尼斯:[45.26,12.2]/韦拉克鲁斯:[19.11,-96.1]/维多利亚:[48.25,-123.21]/维多利亚:[-4.4,55.28]/维也纳:[48.13,16.22]/万象:[18.01,102.48]/维尔纽斯:[54.4,25.19]/弗吉尼亚海滨市:[36.44,-76.02]/维萨卡帕特南:[17.42,83.24]/符拉迪沃斯托克:[43.09,131.53]/华沙:[52.15,21]/华盛顿市:[38.53,-77.02]/惠灵顿:[-41.17,174.47]/怀特霍斯:[60.41,-135.08]/卫奇塔:[37.43,-97.2]/威廉斯塔德:[12.12,-68.56]/温得和克:[-22.34,17.06]/温泽:[42.18,-83.01]/温尼伯:[49.53,-97.1]/弗罗茨瓦夫:[51.05,17]/雅库茨克:[62.1,129.51]/亚穆苏克罗:[6.51,-5.18]/仰光:[16.46,96.09]/雅温得:[3.51,11.31]/亚伦:[-0.32,166.55]/叶卡捷琳堡:[56.52,60.35]/耶洛奈夫:[62.3,-114.29]/耶烈万:[40.1,44.31]/横滨:[35.27,139.39]/南萨哈林斯克:[46.58,142.44]/萨格勒布:[45.49,15.58]/苏黎世:[47.22,8.32]`
    // let list = str.split('/')
    // let json = {}
    // let newarr = []
    // for (let i = 0; i < list.length; i++) {
    //   let arr = list[i].split(':')
    //   let key = arr[0]
    //   let value = arr[1]
    //   json[key] = [JSON.parse(value)[1], JSON.parse(value)[0]] 
    // }
    // let list = []
    // this.mainList3.forEach((ele, index) => {
    //   if (index < 50) {
    //     let list2 = []
    //     ele.list.forEach((ele2, index2) => {
    //       if (index2 < 50) {
    //         list2.push(ele2)
    //       }
    //     })
    //     list.push({ xname: ele.xname, list: list2 })
    //   }
    // })
    // let list = {}
    // this.mainList.forEach(ele => {
    //   list[ele.xname] = []
    // })
    // 数组分类
    // const grouped = dataJson4.data.reduce((result, item) => {
    //   (result[item.xcity] = result[item.xcity] || []).push(item);
    //   return result;
    // }, {});
    // let list = []
    // for (let k in grouped) {
    //   list.push({ xname: k, list: grouped[k] }
    //   )
    // }
    // let province = [
    //   {
    //     "ProID": 1,
    //     "name": "北京市",
    //     "ProSort": 1,
    //     "ProRemark": "直辖市"
    //   },
    //   {
    //     "ProID": 2,
    //     "name": "天津市",
    //     "ProSort": 2,
    //     "ProRemark": "直辖市"
    //   },
    //   {
    //     "ProID": 3,
    //     "name": "河北省",
    //     "ProSort": 5,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 4,
    //     "name": "山西省",
    //     "ProSort": 6,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 5,
    //     "name": "内蒙古自治区",
    //     "ProSort": 32,
    //     "ProRemark": "自治区"
    //   },
    //   {
    //     "ProID": 6,
    //     "name": "辽宁省",
    //     "ProSort": 8,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 7,
    //     "name": "吉林省",
    //     "ProSort": 9,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 8,
    //     "name": "黑龙江省",
    //     "ProSort": 10,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 9,
    //     "name": "上海市",
    //     "ProSort": 3,
    //     "ProRemark": "直辖市"
    //   },
    //   {
    //     "ProID": 10,
    //     "name": "江苏省",
    //     "ProSort": 11,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 11,
    //     "name": "浙江省",
    //     "ProSort": 12,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 12,
    //     "name": "安徽省",
    //     "ProSort": 13,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 13,
    //     "name": "福建省",
    //     "ProSort": 14,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 14,
    //     "name": "江西省",
    //     "ProSort": 15,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 15,
    //     "name": "山东省",
    //     "ProSort": 16,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 16,
    //     "name": "河南省",
    //     "ProSort": 17,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 17,
    //     "name": "湖北省",
    //     "ProSort": 18,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 18,
    //     "name": "湖南省",
    //     "ProSort": 19,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 19,
    //     "name": "广东省",
    //     "ProSort": 20,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 20,
    //     "name": "海南省",
    //     "ProSort": 24,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 21,
    //     "name": "广西壮族自治区",
    //     "ProSort": 28,
    //     "ProRemark": "自治区"
    //   },
    //   {
    //     "ProID": 22,
    //     "name": "甘肃省",
    //     "ProSort": 21,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 23,
    //     "name": "陕西省",
    //     "ProSort": 27,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 24,
    //     "name": "新 疆维吾尔自治区",
    //     "ProSort": 31,
    //     "ProRemark": "自治区"
    //   },
    //   {
    //     "ProID": 25,
    //     "name": "青海省",
    //     "ProSort": 26,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 26,
    //     "name": "宁夏回族自治区",
    //     "ProSort": 30,
    //     "ProRemark": "自治区"
    //   },
    //   {
    //     "ProID": 27,
    //     "name": "重庆市",
    //     "ProSort": 4,
    //     "ProRemark": "直辖市"
    //   },
    //   {
    //     "ProID": 28,
    //     "name": "四川省",
    //     "ProSort": 22,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 29,
    //     "name": "贵州省",
    //     "ProSort": 23,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 30,
    //     "name": "云南省",
    //     "ProSort": 25,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 31,
    //     "name": "西藏自治区",
    //     "ProSort": 29,
    //     "ProRemark": "自治区"
    //   },
    //   {
    //     "ProID": 32,
    //     "name": "台湾省",
    //     "ProSort": 7,
    //     "ProRemark": "省份"
    //   },
    //   {
    //     "ProID": 33,
    //     "name": "澳门特别行政区",
    //     "ProSort": 33,
    //     "ProRemark": "特别行政区"
    //   },
    //   {
    //     "ProID": 34,
    //     "name": "香港特别行政区",
    //     "ProSort": 34,
    //     "ProRemark": "特别行政区"
    //   }
    // ]
    // let arr = []
    // province.forEach(ele => {
    //   arr.push({ cname: ele.name.slice(0,2), iconts: 0, iconzs: 0, iconcs: 0, tslist: [{ name: '天翼云', num: 0 }, { name: '阿里云', num: 0 }, { name: '华为云', num: 0 }, { name: '腾讯云', num: 0 }, { name: '微软云', num: 0 }, { name: 'AWS', num: 0 }, { name: '临港国产', num: 0 }, { name: '园区算力', num: 0 }, { name: '上海公共算力', num: 0 }], zslist: [{ name: '天翼云', num: 0 }, { name: '阿里云', num: 0 }, { name: '华为云', num: 0 }, { name: '腾讯云', num: 0 }, { name: '微软云', num: 0 }, { name: 'AWS', num: 0 }, { name: '临港国产', num: 0 }, { name: '园区算力', num: 0 }, { name: '上海公共算力', num: 0 }], cslist: [{ name: '天翼云', num: 0 }, { name: '阿里云', num: 0 }, { name: '华为云', num: 0 }, { name: '腾讯云', num: 0 }, { name: '微软云', num: 0 }, { name: 'AWS', num: 0 }, { name: '临港国产', num: 0 }, { name: '园区算力', num: 0 }, { name: '上海公共算力', num: 0 }] })
    // })
    // this.centerChoose(this.mainList[0])
    // this.mainList.forEach(ele => {
    //   ele.list.forEach(ele2 => {
    //     ele2['isp'] = '中国电信'
    //     ele2['asnum'] = '-'
    //     ele2['dump'] = '-'
    //   })
    // })
    // this.mainList.forEach(ele => {
    //   ele.list.forEach(ele2 => {
    //     let randomInt = Math.floor(Math.random() * (2 - (-2) + 1)) + (-2)
    //     ele2['delay'] = Number(ele2['delay']) + randomInt
    //   })
    // })
  },
  methods: {
    dayInitData() {
      let now = new Date().getDate()
      if (now % 2 === 1) {
        this.mainList = dataJsonday2.data
      }
    },
    outTypeFun() {
      this.outType = this.outType == '163出口带宽' ? '城域网出口带宽' : '163出口带宽'
      if (this.outType == '163出口带宽') {
        this.mapIndex = 2
      }
    },
    initEarth(fromName, toNameList, xname) {
      let _this = this
      let fromList = [{ name: fromName, value: geoCoordMap[fromName], xname: xname ? xname : '' }]
      let toList = []
      let bjData = []
      toNameList.forEach(ele => {
        toList.push({ name: ele.ycity, value: geoCoordMap[ele.ycity], xname: xname ? xname : '' })
        bjData.push([{ name: fromName }, { name: ele.ycity }])
      })
      let convert = (data) => {
        let res = [];
        for (var i = 0; i < data.length; i++) {
          var dataItem = data[i];
          var fromCoord = geoCoordMap[dataItem[0].name];
          var toCoord = geoCoordMap[dataItem[1].name];
          if (fromCoord && toCoord) {
            res.push({
              fromName: dataItem[0].name,
              toName: dataItem[1].name,
              coords: [fromCoord, toCoord]
            });
          }
        }
        return res;
      };
      let list = convert(bjData)
      let option = {
        backgroundColor: '',
        geo: {
          map: 'world',
          roam: false,
          zoom: 1.2,
          label: {
            emphasis: {
              show: false
            }
          },
          silent: true,
          itemStyle: {
            normal: {
              areaColor: {
                type: 'radial',
                x: 0.3,
                y: 0.3,
                r: 0.8,
                colorStops: [{
                  offset: 0,
                  color: '#464646' // 0% 处的颜色
                }, {
                  offset: 1,
                  color: '#464646'  // 100% 处的颜色
                }],
                globalCoord: true // 缺省为 false
              },
              shadowColor: '#464646',
              shadowOffsetX: 5,
              shadowOffsetY: 5
            },
          },
        },
        series: [
          {
            type: 'map',
            roam: false,
            label: {
              show: false,
              textStyle: {
                color: '#ccc'
              },
            },
            // selectedMode: false,
            selectedMode: 'multiple',
            emphasis: {
              disabled: true
            },
            select: {
              disabled: true,
              itemStyle: {
                borderColor: '#787879',
                borderWidth: 1,
                areaColor: '#334053'
              },
              label: {
                color: '#828282'
              },
            },

            itemStyle: {
              borderColor: '#787879',
              borderWidth: 1,
              areaColor: '#333'
            },
            zoom: 1.2,
            //     roam: false,
            map: 'world', //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
          },
          {
            name: '上海',
            type: 'lines',
            zlevel: 10,
            symbol: ['none'],
            symbolSize: 10,
            effect: {
              show: true,
              period: 4, //箭头指向速度，值越小速度越快
              trailLength: 0.1, //特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: 'arrow', //箭头图标
              symbolSize: 8, //图标大小
              opacity: 0.5,
            },
            lineStyle: {
              normal: {
                color: 'rgba(0, 228, 255,0.5)',
                width: 1,
                opacity: 0.2,
                type: 'solid',
                curveness: -0.2
              }
            },
            data: list,
            zlevel: 2
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: fromList,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 10
              // return params.data.warning
            },
            rippleEffect: {
              scale: 2, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: '#FF9633',
              }
            },
            zlevel: 10
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: toList,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 9
              // return params.data.warning
            },
            rippleEffect: {
              scale: 3, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: '#00E4FF',
              }
            },
            zlevel: 3
          },
        ]
      };
      if (!this.dialogMapEarth) {
        this.dialogMapEarth = echarts.init(this.$refs.dialogMapEarthRef, null, { width: this.GLOBAL.relPx(1300), height: this.GLOBAL.relPx(660) });
        this.GLOBAL.echartsDomArray.push(this.dialogMapEarth)
      }
      this.dialogMapEarth.off('click')
      this.dialogMapEarth.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.dialogMapEarth.setOption(option);
      this.dialogMapEarth.on('click', function (params) {
        let seriesType = params.seriesType
        if (seriesType === 'effectScatter') {
          let toName = params.name
          _this.mapClickLinesRow(toName)
        }
      });
    },
    selectAll(type) {
      this.value2 = []
      let list = this.tabIndex === 1 ? this.mainList : this.tabIndex === 2 ? this.mainList2 : this.mainList3
      if (type) {
        list.forEach((ele, index) => {
          this.value2.push(index)
        })
      } else {
        this.value2 = []
      }
      this.checked = type
      this.endChangeFun(this.value2)
    },
    mapIndexChangeFun(index) {
      if (this.mapIndex != index) {
        this.mapIndex = index
      }
      this.showmapDialog = true
      this.initMapShanghai()
    },
    mapClickLinesRow(name) {
      let list = this.tabIndex === 1 ? this.mainList : this.tabIndex === 2 ? this.mainList2 : this.mainList3
      let row = {}
      let value = 0
      list.forEach((ele, index) => {
        if (ele.xname.indexOf(name) > -1) {
          row = ele
          value = index
        }
      })
      if (row.xname.indexOf('_') > -1) {
        let newArr = []
        row.list.forEach(ele => {
          newArr.push({ delay: ele.delay, jitter: ele.jitter, lost: ele.lost, xcity: ele.xcity.split('_')[0], ycity: ele.ycity.split('_')[0] })
        })
        let newName = row.xname.split('_')[0]
        this.startName = row.xname
        this.value = value
        // this.value2 = []
        this.selectAll(true)
        // this.tableData = row.list
        this.openMap(newName, newArr)
        this.initEarth(newName, newArr)
        this.centerChoose(row)
      } else {
        this.startName = row.xname
        this.value = value
        // this.value2 = []
        this.selectAll(true)
        // this.tableData = row.list
        this.openMap(row.xname, row.list)
        this.initEarth(row.xname, row.list)
        this.centerChoose(row)
      }
    },
    startChangeFun(e) {
      let row = this.tabIndex === 1 ? this.mainList[e] : this.tabIndex === 2 ? this.mainList2[e] : this.mainList3[e]
      if (row.xname.indexOf('_') > -1) {
        let newArr = []
        row.list.forEach(ele => {
          newArr.push({ delay: ele.delay, jitter: ele.jitter, lost: ele.lost, xcity: ele.xcity.split('_')[0], ycity: ele.ycity.split('_')[0] })
        })
        let newName = row.xname.split('_')[0]
        this.startName = row.xname
        this.value = e
        // this.value2 = [9999]
        this.selectAll(true)
        // this.tableData = row.list
        this.openMap(newName, newArr)
        this.initEarth(newName, newArr)
        this.centerChoose(row)
      } else {
        this.startName = row.xname
        this.value = e
        // this.value2 = [9999]
        this.selectAll(true)
        // this.tableData = row.list
        this.openMap(row.xname, row.list)
        this.initEarth(row.xname, row.list)
        this.centerChoose(row)
      }
    },
    endChangeFun(e) {
      let row = this.tabIndex === 1 ? this.mainList[this.value] : this.tabIndex === 2 ? this.mainList2[this.value] : this.mainList3[this.value]
      let list = []
      row.list.forEach((ele, index) => {
        if (e.indexOf(index) > -1) {
          list.push(ele)
        }
      })
      // if (e === 9999) {
      //   list = row.list
      // } else {
      //   row.list.forEach((ele, index) => {
      //     if (e === index) {
      //       list = [ele]
      //     }
      //   })
      // }
      if (row.xname.indexOf('_') > -1) {

        let newArr = []
        list.forEach(ele => {
          newArr.push({ delay: ele.delay, jitter: ele.jitter, lost: ele.lost, xcity: ele.xcity.split('_')[0], ycity: ele.ycity.split('_')[0] })
        })
        let newName = row.xname.split('_')[0]
        // this.openMap(newName, newArr)
        // this.initEarth(newName, newArr)
        this.centerChoose(row)
        this.centerChoose2(row, this.value)
      } else {
        // this.openMap(row.xname, list)
        // this.initEarth(row.xname, list)
        this.centerChoose(row)
        this.centerChoose2(row, this.value)
      }
    },
    centerChoose(row) {
      if (this.lengedIndex !== 1) { return false }
      this.chooseCityName = row.xname

      let str2 = JSON.parse(JSON.stringify(row.list))
      let data2 = str2.sort(function (a, b) {
        return Number(a.delay) - Number(b.delay)
      });
      let list = []
      data2.forEach((ele, index) => {
        if (this.value2.indexOf(index) > -1) {
          list.push(ele)
        }
      })
      let str = JSON.parse(JSON.stringify(list))
      let data = str.sort(function (a, b) {
        return Number(a.delay) - Number(b.delay)
      });
      let length = data.length
      let num1 = 2
      let num2 = 5
      let num3 = Math.floor(length * 0.35)
      let num4 = data.length - num1 - num2 - num3
      let list1 = []
      let list2 = []
      let list3 = []
      let list4 = []
      if (length < 1) {
        list1 = []
        list2 = []
        list3 = []
        list4 = []
      } else if (length < 2) {
        list1 = data.slice(0, 1)
        list2 = []
        list3 = []
        list4 = []
      } else if (length < 3) {
        list1 = data.slice(0, 1)
        list2 = data.slice(1, 2)
        list3 = []
        list4 = []
      } else if (length < 4) {
        list1 = data.slice(0, 1)
        list2 = data.slice(1, 2)
        list3 = data.slice(2, 3)
        list4 = []
      } else if (length < 5) {
        list1 = data.slice(0, 1)
        list2 = data.slice(1, 2)
        list3 = data.slice(2, 3)
        list4 = data.slice(3, 4)
      } else if (length < 20) {
        list1 = data.slice(0, 1)
        list2 = data.slice(1, 3)
        list3 = data.slice(3, num3 + 3)
        list4 = data.slice(num3 + 3, length)
      } else {
        list1 = this.tabIndex === 1 ? data.slice(0, 2) : data.slice(0, 2)
        list2 = data.slice(2, 4)
        list3 = data.slice(4, num3 + 6)
        list4 = data.slice(num3 + 7, length)
      }
      let arr1 = []
      list1.forEach(ele => {
        arr1.push(ele.ycity)
      })
      let arr2 = []
      list2.forEach(ele => {
        arr2.push(ele.ycity)
      })
      let arr3 = []
      list3.forEach(ele => {
        arr3.push(ele.ycity)
      })
      let arr4 = []
      list4.forEach(ele => {
        arr4.push(ele.ycity)
      })
      this.hllist1ms = 1
      this.hllist2ms = 3
      this.hllist3ms = 5
      this.hllist4ms = 20
      // this.hllist1ms = list1 && list1.length > 0 ? list1[list1.length - 1].delay ? list1[list1.length - 1].delay : 0 : '-'
      // this.hllist2ms = list2 && list2.length > 0 ? list2[list2.length - 1].delay ? list2[list2.length - 1].delay : 0 : '-'
      // this.hllist3ms = list3 && list3.length > 0 ? list3[list3.length - 1].delay ? list3[list3.length - 1].delay : 0 : '-'
      // this.hllist4ms = list4 && list4.length > 0 ? list4[list4.length - 1].delay ? list4[list4.length - 1].delay : 0 : '-'
      // this.hllist1ms = Math.ceil((list1[list1.length - 1].delay) / 10) * 10
      // this.hllist2ms = Math.ceil((list2[list2.length - 1].delay) / 10) * 10
      // this.hllist3ms = Math.ceil((list3[list3.length - 1].delay) / 10) * 10
      // this.hllist4ms = Math.ceil((list4[list4.length - 1].delay) / 10) * 10
      // this.hllist1 = arr1.join('、')
      // this.hllist2 = arr2.join('、')
      // this.hllist3 = arr3.join('、')
      // this.hllist4 = arr4.join('、')
      let narr1 = []
      let narr2 = []
      let narr3 = []
      let narr4 = []
      data.forEach(ele => {
        if (Number(ele.delay) <= 1) {
          narr1.push(ele.ycity)
        } else if (Number(ele.delay) <= 3 && Number(ele.delay) > 1) {
          narr2.push(ele.ycity)
        } else if (Number(ele.delay) <= 5 && Number(ele.delay) > 3) {
          narr3.push(ele.ycity)
        } else if (Number(ele.delay) > 5) {
          narr4.push(ele.ycity)
        }
      })
      this.hllist1 = narr1.join('、')
      this.hllist2 = narr2.join('、')
      this.hllist3 = narr3.join('、')
      this.hllist4 = narr4.join('、')
      this.tableData = data2
    },
    centerChoose2(row, index) {

      // 注释-暂时取消 2023年10月1日 15:17:03
      // if (this.showDialog) {
      //   return false
      // }
      this.startName = row.xname
      this.value = index
      // this.value2 = [9999]
      // this.selectAll(true)
      // this.tableData = row.list
      // this.showDialog = true
      let str2 = JSON.parse(JSON.stringify(row.list))
      let data2 = str2.sort(function (a, b) {
        return Number(a.delay) - Number(b.delay)
      });
      let list = []
      data2.forEach((ele, index) => {
        if (this.value2.indexOf(index) > -1) {
          list.push(ele)
        }
      })
      let str = JSON.parse(JSON.stringify(list))
      let data = str.sort(function (a, b) {
        return Number(a.delay) - Number(b.delay)
      });

      if (row.xname.indexOf('_') > -1) {
        let newArr = []
        list.forEach(ele => {
          newArr.push({ delay: ele.delay, jitter: ele.jitter, lost: ele.lost, xcity: ele.xcity.split('_')[0], ycity: ele.ycity.split('_')[0] })
        })
        let newName = row.xname.split('_')[0]
        this.openMap(newName, newArr, row.xname)
        this.initEarth(newName, newArr, row.xname)
      } else {
        this.openMap(row.xname, list)
        this.initEarth(row.xname, list)
      }
    },
    openMap(fromName, toNameList, xname) {
      let _this = this
      if (!this.dialogMap) {
        this.dialogMap = echarts.init(this.$refs.dialogMap, null, { width: this.GLOBAL.relPx(1300), height: this.GLOBAL.relPx(660) });
        this.GLOBAL.echartsDomArray.push(this.dialogMap)
      }
      let fromList = [{ name: fromName, value: geoCoordMap[fromName], xname: xname ? xname : '' }]
      let toList = []
      let bjData = []
      toNameList.forEach(ele => {
        toList.push({ name: ele.ycity, value: geoCoordMap[ele.ycity], xname: xname ? xname : '' })
        bjData.push([{ name: fromName }, { name: ele.ycity }])
      })
      let convert = (data) => {
        let res = [];
        for (var i = 0; i < data.length; i++) {
          var dataItem = data[i];
          var fromCoord = geoCoordMap[dataItem[0].name];
          var toCoord = geoCoordMap[dataItem[1].name];
          if (fromCoord && toCoord) {
            res.push({
              fromName: dataItem[0].name,
              toName: dataItem[1].name,
              coords: [fromCoord, toCoord]
            });
          }
        }
        return res;
      };
      let list = convert(bjData)
      let option = {
        backgroundColor: '',
        title: {
          left: 'center',
          textStyle: {
            color: '#fff'
          }
        },
        geo: {
          map: 'china',
          aspectScale: 0.72, //长宽比
          center: [102.51888, 35.84850],
          zoom: 1.7,
          roam: false,
          label: {
            show: false
          },
          emphasis: {
            disabled: true,
            itemStyle: {
              normal: {
                areaColor: {
                  type: 'radial',
                  x: 0.3,
                  y: 0.3,
                  r: 0.8,
                  colorStops: [{
                    offset: 0,
                    color: '#464646' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: '#464646'  // 100% 处的颜色
                  }],
                  globalCoord: true // 缺省为 false
                },
                shadowColor: '#464646',
                shadowOffsetX: 5,
                shadowOffsetY: 5
              },
            },
          },
          itemStyle: {
            normal: {
              areaColor: {
                type: 'radial',
                x: 0.3,
                y: 0.3,
                r: 0.8,
                colorStops: [{
                  offset: 0,
                  color: '#464646' // 0% 处的颜色
                }, {
                  offset: 1,
                  color: '#464646'  // 100% 处的颜色
                }],
                globalCoord: true // 缺省为 false
              },
              shadowColor: '#464646',
              shadowOffsetX: 5,
              shadowOffsetY: 5
            },
          },
          regions: [{
            name: '南海诸岛',
            itemStyle: {
              areaColor: 'rgba(0, 10, 52, 1)',
              borderColor: 'rgba(0, 10, 52, 1)',
              normal: {
                opacity: 0,
                label: {
                  show: false,
                  color: "#009cc9",
                }
              }
            },
          }],
        },
        series: [
          {
            name: '上海',
            type: 'lines',
            zlevel: 10,
            symbol: ['none'],
            symbolSize: 10,
            effect: {
              show: true,
              period: 4, //箭头指向速度，值越小速度越快
              trailLength: 0.1, //特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: 'arrow', //箭头图标
              symbolSize: 8, //图标大小
              opacity: 0.5,
            },
            lineStyle: {
              normal: {
                color: 'rgba(0, 228, 255,0.5)',
                width: 1,
                opacity: 0.2,
                type: 'solid',
                curveness: -0.2
              }
            },
            data: list,
            zlevel: 2
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: fromList,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 10
              // return params.data.warning
            },
            rippleEffect: {
              scale: 2, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: '#FF9633',
              }
            },
            zlevel: 10
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: toList,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 9
              // return params.data.warning
            },
            rippleEffect: {
              scale: 3, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: '#00E4FF',
              }
            },
            zlevel: 3
          },
          {
            type: 'map',
            roam: false,
            center: [103.41888, 35.74850],
            label: {
              show: true,
              textStyle: {
                color: '#ccc'
              },
            },
            // selectedMode: false,
            selectedMode: 'multiple',
            emphasis: {
              disabled: true
            },
            select: {
              disabled: true,
              itemStyle: {
                borderColor: '#787879',
                borderWidth: 1,
                areaColor: '#334053'
              },
              label: {
                color: '#828282'
              },
            },

            itemStyle: {
              borderColor: '#787879',
              borderWidth: 1,
              areaColor: '#333'
            },
            zoom: 1.7,
            //     roam: false,
            map: 'china', //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
          },
        ]
      };
      this.dialogMap.off('click')
      this.dialogMap.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.dialogMap.setOption(option);
      this.dialogMap.on('click', function (params) {
        let seriesType = params.seriesType
        if (seriesType === 'effectScatter') {
          let toName = params.name
          _this.mapClickLinesRow(toName)
        }
      });
    },
    initMap() {
      if (!this.chartsMap) {
        this.chartsMap = echarts.init(this.$refs.map, null, { width: this.GLOBAL.relPx(380), height: this.GLOBAL.relPx(270) });
        this.GLOBAL.echartsDomArray.push(this.chartsMap)
      }
      let data = []
      if (this.mapIndex == 1) {
        data = [
          { name: '上海信息园区', value: [121.54462, 31.13231], warning: 5 },
          { name: '京津冀产业园数据中心', value: [116.84461, 39.63426], warning: 5 },
          { name: '武汉未来城二期', value: [114.51023, 30.39377], warning: 5 },
          { name: '常州国际数据中心', value: [119.87584, 31.80354], warning: 5 }
        ]
      }
      if (this.mapIndex == 2) {
        data = [
          { name: '上海蕰川数据中心', value: [121.42574, 31.42167], warning: 5 },
          { name: '兆维数据中心', value: [116.49956, 39.97992], warning: 5 },
          { name: '南京电信河西国际数据中心', value: [118.74620, 32.03908], warning: 5 },
          { name: '福州云谷仓科数据中心', value: [119.35398, 26.03350], warning: 5 }
        ]
      }
      if (this.mapIndex == 3) {
        data = [
          { name: '上海卓青数据中心', value: [121.09785, 31.18554], warning: 5 },
          { name: '芜湖城东数据中心', value: [118.43798, 31.35540], warning: 5 },
          { name: '杭州兴议数据中心', value: [120.24545, 30.21114], warning: 5 },
        ]
      }
      let option = {
        backgroundColor: '',
        title: {
          left: 'center',
          textStyle: {
            color: '#fff'
          }
        },
        geo: {
          map: 'china',
          roam: false,
          zoom: 1.5,
          center: [103.41888, 35.54850],
          label: {
            show: false,
            color: "rgba(255,255,255,0.6)",
            fontSize: this.GLOBAL.relPx(12),
            fontFamily: 'Microsoft YaHei',
            // textBorderColor: '#f00000',
            // textBorderWidth: 5,
            // fontWeight: 'bold'
            emphasis: {
              show: true
            }
          },
          silent: true,
          itemStyle: {
            normal: {
              areaColor: 'rgba(51, 51, 51, 0.9)',
              borderColor: '#111'
            },
            emphasis: {
              areaColor: '#2a333d'
            }
          }
        },
        series: [
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: data,
            showEffectOn: 'render',
            tooltip: {
              show: true,
              formatter: '{a}',
              triggerOn: "click",
            },
            symbolSize(value, params) {
              // return 0
              return params.data.warning
            },
            rippleEffect: {
              scale: 4, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "rgba(0, 214, 223,0.3)"
                },
                {
                  offset: 1,
                  color: "rgba(0, 214, 223,1)"
                }
                ]),
                shadowBlur: 10,
                shadowColor: '#333'
              }
            },
            emphasis: {
              show: false,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "rgba(0, 154, 228,0.3)"
                },
                {
                  offset: 1,
                  color: "rgba(0, 154, 228,1)"
                }
                ]),
              }
            },
            zlevel: 3
          },
          {
            type: 'map',
            roam: false,
            center: [103.41888, 35.74850],
            label: {
              show: true,
              textStyle: {
                color: '#828282'
              },
            },
            // selectedMode: false,
            selectedMode: 'multiple',
            emphasis: {
              disabled: true
            },
            select: {
              disabled: true,
              itemStyle: {
                borderColor: '#787879',
                borderWidth: 1,
                areaColor: '#334053'
              },
              label: {
                color: '#828282'
              },
            },

            itemStyle: {
              borderColor: '#787879',
              borderWidth: 1,
              areaColor: '#333'
            },
            zoom: 1.7,
            //     roam: false,
            map: 'china', //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
          },
        ]
      };
      this.chartsMap.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsMap.setOption(option);
      this.chartsMap.on('click', function (params) {
      });
    },
    initMapShanghai() {
      if (!this.chartsMapShanghai) {
        this.chartsMapShanghai = echarts.init(this.$refs.mapShanghai, null, { width: this.GLOBAL.relPx(340), height: this.GLOBAL.relPx(376) });
        this.GLOBAL.echartsDomArray.push(this.chartsMapShanghai)
      }
      let data = []
      if (this.outType == '163出口带宽') {
        if (this.mapIndex == 1) {
          data = this.list163[0]
        }
        if (this.mapIndex == 2) {
          data = this.list163[1]
        }
        if (this.mapIndex == 3) {
          data = this.list163[2]
        }
      } else {
        data = this.listCy
      }
      let option = {
        backgroundColor: '',
        title: {
          left: 'center',
          textStyle: {
            color: '#fff'
          }
        },
        geo: {
          map: 'shanghai',
          roam: false,
          zoom: 1.25,
          center: [121.47066, 31.27382],
          label: {
            show: false,
            color: "rgba(255,255,255,0.6)",
            fontSize: this.GLOBAL.relPx(12),
            fontFamily: 'Microsoft YaHei',
            // textBorderColor: '#f00000',
            // textBorderWidth: 5,
            // fontWeight: 'bold'
            emphasis: {
              show: true
            }
          },
          silent: true,
          itemStyle: {
            normal: {
              areaColor: 'rgba(51, 51, 51, 0.9)',
              borderColor: '#111'
            },
            emphasis: {
              areaColor: '#2a333d'
            }
          }
        },
        tooltip: {
          trigger: 'item',
          // className: 'custom-tooltip-box', // 命名父级类名
          triggerOn: 'mousemove',
          position: 'right',
          formatter: '{b}',
          backgroundColor: 'none',
          borderColor: 'none',
          borderWidth: 0,
          textStyle: {
            color: '#00E4FF'
          }
          // formatter: function (params) {
          //   const { name, value } = params
          //   var htmlText = `<div class='custom-tooltip-style'>  
          //                   <div class='custom-tooltip-top'>${name}</div>
          //               </div>`
          //   return htmlText
          // }
        },
        series: [
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: data,
            showEffectOn: 'render',
            symbolSize(value, params) {
              return 6
              // return params.data.warning
            },
            // rippleEffect: {
            // scale: 4, // 波纹的最大缩放比例
            // brushType: 'stroke'
            // },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "rgba(0, 214, 223,0.3)"
                },
                {
                  offset: 1,
                  color: "rgba(0, 214, 223,1)"
                }
                ]),
                shadowBlur: 10,
                shadowColor: '#333'
              }
            },
            emphasis: {
              show: false,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "rgba(0, 154, 228,0.3)"
                },
                {
                  offset: 1,
                  color: "rgba(0, 154, 228,1)"
                }
                ]),
              }
            },
            zlevel: 3
          },
          {
            type: 'map',
            roam: false,
            center: [121.47066, 31.27382],
            label: {
              show: true,
              textStyle: {
                color: '#828282',
                fontSize: 8
              },
            },
            selectedMode: false,
            // selectedMode: 'multiple',
            emphasis: {
              disabled: true
            },
            tooltip: {
              show: false
            },
            select: {
              disabled: true,
              itemStyle: {
                borderColor: '#787879',
                borderWidth: 1,
                areaColor: '#334053'
              },
              label: {
                color: '#828282'
              },
            },

            itemStyle: {
              borderColor: '#787879',
              borderWidth: 1,
              areaColor: '#333'
            },
            zoom: 1.2,
            //     roam: false,
            map: 'shanghai', //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
          },
        ]
      };
      this.chartsMapShanghai.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsMapShanghai.setOption(option);
    },
    lengedChange(index) {
      if (this.lengedIndex != index) {
        this.lengedIndex = index
        this.$refs.scrollContainer.scrollTop = 0;
        this.$refs.scrollContainer.scrollLeft = 0;
      }
    },
    tabChange(index) {
      if (index == 4) {
        this.$router.push({
          path: '/page6'
        })
        return false
      }
      if (this.tabIndex != index) {
        this.tabIndex = index
        this.lengedIndex = 1
        let row = index === 1 ? this.mainList[23] : index === 2 ? this.mainList2[2] : this.mainList3[0]
        this.startName = row.xname
        this.value = index === 1 ? 23 : index === 2 ? 2 : 0
        // this.value2 = 9999
        this.selectAll(true)
        // this.tableData = row.list
        // this.centerChoose(row)
        // this.centerChoose2(row, index === 2 ? 2 : 0)
      }
    },
  },
  destroyed() {
  },
};
</script>

<style lang="scss" scoped>
.page5 {
  .main {
    padding-top: 10px;

    .tabCore {
      width: 700px;

      .tab {
        .item {
          background-image: url('~@/assets/images/page5/tab_item.png');
          background-size: cover;
          width: 231px;
          height: 79px;
          cursor: pointer;

          .icon {
            width: 40px;
            height: 40px;
            margin-top: 19.5px;
            opacity: 0.5;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .word {
            line-height: 79px;
            font-size: 26px;
            margin-left: 20px;
            opacity: 0.5;
          }

          &.on {
            background-image: url('~@/assets/images/page5/tab_item_on.png');

            .icon {
              opacity: 1;
            }

            .word {
              opacity: 1;
            }
          }
        }
      }

      .tabDownCore {

        .tabDown {
          padding: 25px 18px 0 18px;
          margin-top: 15px;
          position: relative;

          .imgCore {
            background-color: rgba($color: #ccc, $alpha: 0.05);
            border-radius: 10px;
            padding: 40px 0;
          }

          .img {
            width: 100%;
            display: block;
            margin: 0 auto;
          }

          .lengedBottom {
            margin-top: 20px;

            .item {
              margin-left: 20px;

              .color {
                width: 12px;
                height: 4px;
                background: #00DEFF;
                margin-top: 7px;
              }

              .text {
                font-size: 12px;
                padding-left: 10px;
              }

              &.on {
                .color {
                  background: #00FF90;
                }
              }
            }
          }

          &.tab1 {
            padding-left: 0;
            padding-right: 0;
            margin-top: 0;
            padding-top: 15px;

            .lengedBottom {
              position: absolute;
              bottom: 10px;
              right: 20px;
            }
          }

          &.tab3 {
            padding-left: 0;
            padding-right: 0;
            margin-top: 0px;
            padding-top: 15px;

            .img {
              width: 509px;
              height: 344px;
              display: block;
              margin: 0 auto;
            }
          }
        }
      }
    }

    .centerRight {
      width: 469px;

      .top {
        background-color: rgba($color: #26262A, $alpha: 0.15);
        // height: 670px;
        // width: 100%;
        border: 1px solid rgba($color: #ffffff, $alpha: 0.15);

        .titleCore {
          padding: 10px 0 10px 12px;
          border-bottom: 1px solid rgba($color: #ffffff, $alpha: 0.15);

          .icon {
            width: 25px;
            height: 18px;

            img {
              width: 100%;
              height: 100%
            }
          }

          .title {
            font-size: 14px;
            line-height: 18px;
            margin-left: 10px;
          }
        }

        .hlCore {
          position: relative;
          background-image: url('~@/assets/images/1920/page5/hl.png');
          background-size: cover;
          width: 469px;
          height: 753px;

          .hlname1 {
            width: 220px;
            height: 270px;
            position: absolute;
            top: 155px;
            left: 165px;
            z-index: 1;
            display: flex; //弹性布局
            justify-content: center; //子元素相对父元素水平（主轴）居中
            align-items: center; //子元素相对父元素垂直（交叉轴）居中
            font-size: 13px;
            line-height: 18px;
          }

          .hlname2 {
            width: 240px;
            height: 110px;
            position: absolute;
            top: 590px;
            left: 125px;
            z-index: 1;
            display: flex; //弹性布局
            justify-content: center; //子元素相对父元素水平（主轴）居中
            align-items: center; //子元素相对父元素垂直（交叉轴）居中
            font-size: 13px;
            line-height: 18px;
          }

          .hlname3 {
            width: 70px;
            height: 95px;
            position: absolute;
            top: 530px;
            left: 10px;
            z-index: 1;
            display: flex; //弹性布局
            justify-content: center; //子元素相对父元素水平（主轴）居中
            align-items: center; //子元素相对父元素垂直（交叉轴）居中
            font-size: 13px;
            line-height: 18px;
          }

          .hlname4 {
            width: 90px;
            height: 40px;
            position: absolute;
            top: 480px;
            left: 85px;
            z-index: 1;
            display: flex; //弹性布局
            justify-content: center; //子元素相对父元素水平（主轴）居中
            align-items: center; //子元素相对父元素垂直（交叉轴）居中
            font-size: 13px;
            line-height: 18px;
          }

          .hlms1 {
            font-size: 17px;
            color: #00DDFF;
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1;
          }

          .hlms2 {
            font-size: 17px;
            color: #1FCF8F;
            position: absolute;
            top: 720px;
            right: 10px;
            z-index: 1;
          }

          .hlms3 {
            font-size: 17px;
            color: #00C0FA;
            position: absolute;
            top: 720px;
            left: 10px;
            z-index: 1;
          }

          .hlms4 {
            font-size: 17px;
            color: #0273EB;
            position: absolute;
            top: 450px;
            left: 10px;
            z-index: 1;
          }

          .hl1 {
            background-image: url('~@/assets/images/page5/hl-4.png');
            background-size: cover;
            width: 290px;
            height: 429px;
            position: absolute;
            z-index: 1;
            top: 0;
            left: 22px;
          }

          .hl2 {
            background-image: url('~@/assets/images/page5/hl-3.png');
            background-size: cover;
            width: 280px;
            height: 451px;
            position: absolute;
            z-index: 2;
            top: 144px;
            left: 110px;
          }

          .hl3 {
            background-image: url('~@/assets/images/page5/hl-2.png');
            background-size: cover;
            width: 281px;
            height: 186px;
            position: absolute;
            z-index: 1;
            top: 442px;
            left: 3px;
          }

          .hl4 {
            background-image: url('~@/assets/images/page5/hl-1.png');
            background-size: cover;
            width: 151px;
            height: 120px;
            position: absolute;
            z-index: 1;
            top: 386px;
            left: 0px;
          }
        }
      }

      .down {
        margin-top: 30px;

        .item {
          width: 230px;
          height: 37px;
          background: rgba($color: #26262A, $alpha: 1);
          border: 1px solid #FFFFFF;
          border-radius: 2px;
          opacity: 0.5;
          box-sizing: border-box;
          padding-top: 8px;
          cursor: pointer;
          // margin: 0 20px;
          .icon {
            width: 21px;
            height: 21px;
            margin-right: 10px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .text {
            font-size: 15px;
          }

          &.on {
            opacity: 1;
            border-color: #2A53A4;
            background-color: rgba($color: #2A53A4, $alpha: 0.5);
          }
        }
      }
    }



    .right {
      width: 930px;
      display: none;

      .topFour {
        color: #ffffff;

        .son {
          width: 450px;
          height: 158px;
          background: rgba($color: #26262A, $alpha: 0.15);
          box-sizing: border-box;
          padding: 20px 0 0 20px;
          margin-bottom: 20px;

          .title {
            font-size: 14px;
            line-height: 14px;
          }

          .value {
            font-size: 36px;
            line-height: 36px;
            text-align: center;
            padding-top: 40px;

            span {
              font-size: 20px;
            }
          }
        }
      }


    }
  }

  .center {
    width: 1300px;
    overflow: hidden;
    display: none;

    .centerBox {
      width: 100%;
    }

    .centerCore {
      width: 900px;
      height: 680px;
      overflow: scroll;
      box-sizing: border-box;
      display: none;

      &::-webkit-scrollbar {
        background: none;
        border-radius: 10px;
      }

      &::-webkit-scrollbar-thumb {
        width: 20px;
        background-color: rgba($color: #ffffff, $alpha: 0.1);
        border-radius: 10px;
      }

      &::-webkit-scrollbar-track {
        background: none;
        border-radius: 10px;
      }

      // 108 *34
      .topProvice {
        width: 3672px;
        margin-left: 108px;
        position: sticky;
        top: 0px;
        z-index: 5;

        .item {
          width: 108px;
          text-align: center;
          position: relative;
          z-index: 1;

          span {
            background-color: rgba($color: #000000, $alpha: 0.4);
            padding: 5px;
            border-radius: 3px;
          }
        }
      }

      .downProvice {
        width: 3780px;

        // z-index: 5;
        .item {
          margin-top: 33px;
          position: relative;
          z-index: 1;

          // width: 50px;
          .xname {
            width: 108px;
            line-height: 34px;
            position: sticky;
            left: 0;
            z-index: 5;
            cursor: pointer;

            span {
              background-color: rgba($color: #000000, $alpha: 0.4);
              padding: 5px;
              border-radius: 3px;
            }
          }

          .son {
            width: 108px;
            text-align: center;

            .box {
              width: 34px;
              height: 34px;
              background: #1A191C;
              border: 1px solid #5470C6;
              border-radius: 50%;
              font-size: 16px;
              text-align: center;
              line-height: 32px;
              margin: auto;
              color: rgba($color: #ffffff, $alpha: 0.8);
              position: relative;

              span {
                width: 14px;
                height: 14px;
                background: #FF6933;
                border-radius: 50%;
                position: absolute;
                top: -2px;
                right: -2px;
                z-index: 1;
                opacity: 0;
              }

              &.on {
                color: #FAA302;
                border: 1px dashed #FAA302;

                span {
                  opacity: 1;
                }
              }
            }
          }
        }
      }

      &.c2 {

        // 128 * 17
        .topProvice {
          width: 2176px;
          margin-left: 128px;

          .item {
            width: 128px;
          }
        }

        .downProvice {
          width: 2304px;

          .item {
            .xname {
              width: 128px;
            }

            .son {
              width: 128px;
            }
          }
        }
      }

      &.c3 {

        // 128 * 99
        .topProvice {
          width: 6400px;
          margin-left: 128px;

          .item {
            width: 128px;
          }
        }

        .downProvice {
          width: 6528px;

          .item {
            .xname {
              width: 128px;
            }

            .son {
              width: 128px;
            }
          }
        }
      }
    }

    .centerLenged {
      margin-top: 30px;
      width: 800px;

      .item {
        margin: 0 40px;
        cursor: pointer;

        .color {
          width: 12px;
          height: 12px;
          background: #999999;
          border-radius: 6px;
          margin-top: 4px;
          margin-right: 10px;
        }

        .text {
          font-size: 16px;
          color: #999999;
        }

        &.on {
          .color {
            background: #5470C6;
          }

          .text {
            color: #ffffff;
          }
        }
      }
    }
  }

  .dialogMap {
    width: 1330px;
    // height: 900px;

    .map {
      width: 1300px;
      height: 660px;
      margin: 0 auto;
    }

    .titleCore {
      padding: 20px;
      font-size: 14px;
      line-height: 14px;
      border-bottom: 1px solid rgba($color: #ffffff, $alpha: 0.1);

      .name {}

      .selectCore {
        .item {
          margin-right: 40px;
        }

        .label {
          color: #00E4FF;
        }
      }
    }

    .tableList {
      font-size: 14px;
      line-height: 14px;
      text-align: right;
      padding: 0 13px;
      
      .item {
        width: 80px;
      }

      .w2 {
        width: 160px;
      }

      .w3 {
        width: 160px;
      }

      .w4 {
        width: 80px;
      }

      .ul {
        color: #00E3FB;
        padding: 15px 10px;

        .w1 {
          width: 140px;
          text-align: left;
        }
      }

      .liList {
        height: 100px;
        overflow: hidden;
        overflow-y: scroll;
        margin-top: 10px;
      }

      .li {
        color: rgba($color: #ffffff, $alpha: 0.8);
        padding: 10px 10px;

        .w1 {
          width: 150px;
          text-align: left;
        }

        &.on {
          // background-color: rgba($color: #2A2A2D, $alpha: 0.3);
        }
      }
    }
  }

  .leftCore {
    width: 414px;

    .downMapCore {
      background: rgba($color: #26262A, $alpha: 0.15);

      .top {
        height: 80px;
        line-height: 40px;
        border-bottom: 1px solid rgba($color: #ffffff, $alpha: 0.05);
        font-size: 14px;
        box-sizing: border-box;
        padding: 0 20px;

        .title {}

        .tabs {
          color: rgba($color: #ffffff, $alpha: 0.8);

          .tabsflex {
            width: 210px;
          }

          .itemttp {
            margin-left: 15px;
            cursor: pointer;
            font-size: 14px;

            &.on {
              color: #00E4FF;
            }
          }
        }

        .label {
          color: #00E4FF;
          cursor: pointer;
        }
      }

      .down {
        .map {
          width: 380px;
          height: 270px;
          margin: 0 auto;
        }

        .roomList {
          width: 100%;
          height: 360px;
          overflow: hidden;
          overflow-y: scroll;
          font-size: 14px;
          padding: 20px 26px;
          box-sizing: border-box;

          .item {
            margin-bottom: 30px;

            .title {
              color: #00E4FF;
            }

            .ul {
              .li {
                margin-top: 15px;
                width: 50%;
              }
            }
          }
        }
      }
    }

    .top {
      .item {
        width: 100%;
        height: 200px;
        background-color: rgba($color: #26262A, $alpha: 0.15);
        box-sizing: border-box;
        padding: 15px;
        margin-bottom: 18px;

        .title {
          font-size: 16px;
          line-height: 16px;
        }

        .topval {
          padding: 0 53px;

          .line {
            background-image: url('~@/assets/images/1920/page5/line.png');
            background-size: cover;
            width: 309px;
            height: 2px;
            margin: 0 auto;
            margin-top: 30px;
          }

          .topvaldown {
            padding-top: 15px;
            font-size: 14px;
            line-height: 20px;

            .text {
              margin-left: 7px;
            }
          }
        }

        .value {
          font-size: 36px;
          line-height: 36px;
          text-align: center;
          margin-top: 50px;

          span {
            font-size: 20px;
          }

          i {
            font-size: 20px;
            vertical-align: text-top;
            font-style: normal;
          }
        }
      }
    }

  }



  .bottomRight {
    font-size: 10px;
    color: #A9A9AA;
    line-height: 20px;
    margin-top: 30px;
    text-align: right;
  }

  .mapDialog {
    position: fixed;
    left: 559px;
    top: 581px;
    z-index: 999;
    background-color: rgba($color: #201F23, $alpha: 0.8);
    width: 360px;
    height: 414px;
    border-radius: 8px;

    .close {
      background-image: url('~@/assets/images/page5/close.png');
      background-size: cover;
      width: 25px;
      height: 25px;
      cursor: pointer;
      margin-right: 5px;
      margin-top: 5px;
    }
  }
}
</style>

<style lang="scss">
.page5 {
  .dialogMap {
    .select {
      width: auto;

      .el-input__icon {
        line-height: 14px;
      }

      .el-input__suffix {
        height: 14px;
      }

      .el-input__inner {
        background: none;
        color: #fff;
        border: none;
        height: 14px;
      }

    }

    .el-select-dropdown {
      background: #1A191C;
    }

    .el-tag.el-tag--info {
      background: rgba($color: #26262a, $alpha: 0.8);
      border-color: rgba($color: #26262a, $alpha: 0.8);
      color: #fff;
    }
  }

  ::-webkit-scrollbar {
    width: 14px;
    height: 14px
  }

  ::-webkit-scrollbar-track,
  ::-webkit-scrollbar-thumb {
    border-radius: 999px;
    border: 5px solid transparent
  }

  ::-webkit-scrollbar-track {
    box-shadow: 1px 1px 5px rgba($color: #00E3FB, $alpha: 0.2) inset
  }

  ::-webkit-scrollbar-thumb {
    min-height: 20px;
    background-clip: content-box;
    box-shadow: 0 0 0 5px rgba($color: #00E3FB, $alpha: 0.2) inset
  }

  ::-webkit-scrollbar-corner {
    background: transparent
  }
}

.el-select-dropdown {
  .myCheckBox {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: flex-end !important;
    padding-bottom: 5px;
    padding-right: 10px;

    .el-checkbox__input {
      margin-top: 2px;
    }

    .el-checkbox__label {
      color: #1A191C;
      font-size: 14px;
    }
  }
}
</style>

<style scoped lang="scss">
// 给父盒子清除默认已有样式
/deep/ .custom-tooltip-box {
  padding: 0 !important;
  border: none !important;
  background: none !important;

  // 给子盒子自定义样式
  .custom-tooltip-style {
    margin: 0;
    display: flex;
    flex-direction: column;
    background: none;

    .custom-tooltip-top {
      display: flex;
      color: #00E4FF;
      align-items: center;
      background: none;
      padding-left: 5px;
    }

    .custom-tooltip-middle {
      opacity: 0.5;
      height: 1px;
      background: #a3a199;
      margin: 8px 0 12px 0;
    }

    .custom-tooltip-bbotton {
      color: #000000;
      font-size: 30px;
      font-family: Helvetica;
      font-weight: bold;
    }
  }

  &:last-child {
    // opacity: 0;
  }
}
</style>