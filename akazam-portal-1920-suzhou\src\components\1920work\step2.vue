<template>
  <div class="pageWork2 flex-box-between">
    <div class="leftStep flex-box">
      <div class="stepList flex-box">
        <div class="item on al" @click="endFun(1)">需求输入</div>
        <div class="arraw on al"></div>
        <div class="item on al" @click="endFun(2)">资源池分配</div>
        <div class="arraw on al"></div>
        <div class="item on flex-box-center">
          <div class="icon"></div>
          运行环境设置
        </div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(4)">模型训练</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(5)">推理部署</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(7)">验证访问</div>
      </div>
      <div>
        <div class="wordList">
          <p :class="wordAni1 ? 'ani' : ''">应用-智算作业：图像分类学习</p>
          <p :class="wordAni2 ? 'ani' : ''" class="flex-box">
            <i v-if="iconAni1" class="el-icon-loading"></i>
            <img
              v-if="iconAni2"
              src="~@/assets/images/page4/step1/success.png"
              alt=""
            />
            {{ word1 }}
          </p>
        </div>
        <div class="wordList">
          <p :class="wordAni3 ? 'ani' : ''">应用-通算作业：图像应用打包</p>
          <p :class="wordAni4 ? 'ani' : ''" class="flex-box">
            <i v-if="iconAni3" class="el-icon-loading"></i>
            <img
              v-if="iconAni4"
              src="~@/assets/images/page4/step1/success.png"
              alt=""
            />
            {{ word1_2 }}
          </p>
        </div>
      </div>
    </div>
    <div class="rightBox flex-box-between">
      <div class="centerCore">
        <div class="core">
          <div class="item" :class="centerOpa1 ? 'ani' : ''">
            <div class="son" :class="ccAni1 ? 'ani' : ''">
              <div class="ict flex-box">
                <div class="icon">
                  <img src="~@/assets/images/page4/step1/s2-icon1.png" alt="" />
                </div>
                <div class="text">
                  智算作业运行资源池：{{ job1SelectRegion }}
                </div>
              </div>
            </div>
            <div class="son" :class="ccAni2 ? 'ani' : ''">
              <div class="ict flex-box on">
                <div class="icon">
                  <img src="~@/assets/images/1920/work/step3/yes.png" alt="" />
                </div>
                <div class="text">配置数据集输入和输出路径</div>
              </div>
            </div>
            <div class="son" :class="ccAni3 ? 'ani' : ''">
              <div class="ict flex-box on">
                <div class="icon"></div>
                <div class="text">数据集输入路径：/lp0705/v1/data/input/</div>
              </div>
            </div>
            <div class="son" :class="ccAni4 ? 'ani' : ''">
              <div class="ict flex-box on">
                <div class="icon"></div>
                <div class="text">数据集输出路径：/lp0705/v1/data/output/</div>
              </div>
            </div>
            <div class="son" :class="ccAni5 ? 'ani' : ''">
              <div class="ict flex-box on">
                <div class="icon">
                  <img src="~@/assets/images/1920/work/step3/yes.png" alt="" />
                </div>
                <div class="text">学习框架</div>
              </div>
              <div class="itemImgList flex-box-between">
                <div class="imgSon">
                  <div class="img">
                    <img
                      src="~@/assets/images/page4/step1/s2-img1.png"
                      alt=""
                    />
                  </div>
                  <div class="dp" :class="chooseAni ? 'on' : ''"></div>
                </div>
                <div class="imgSon">
                  <div class="img">
                    <img
                      src="~@/assets/images/page4/step1/s2-img2.png"
                      alt=""
                    />
                  </div>
                  <div class="dp"></div>
                </div>
                <div class="imgSon">
                  <div class="img">
                    <img
                      src="~@/assets/images/page4/step1/s2-img3.png"
                      alt=""
                    />
                  </div>
                  <div class="dp"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="item" :class="centerOpa2 ? 'ani' : ''">
            <div class="son" :class="ccAni1_2 ? 'ani' : ''">
              <div class="ict flex-box">
                <div class="icon">
                  <img src="~@/assets/images/page4/step1/s2-icon1.png" alt="" />
                </div>
                <div class="text">
                  通算作业运行资源池：{{ job2SelectRegion }}
                </div>
              </div>
            </div>
            <div class="son" :class="ccAni2_2 ? 'ani' : ''">
              <div class="ict flex-box on">
                <div class="icon">
                  <img src="~@/assets/images/1920/work/step3/yes.png" alt="" />
                </div>
                <div class="text">网络连接：</div>
              </div>
            </div>
            <div class="son" :class="ccAni2_3 ? 'ani' : ''">
              <div class="ict flex-box on">
                <div class="icon"></div>
                <div class="text">
                  {{ job1SelectRegion }}— 互联组CG001（CN2）—
                  {{ job2SelectRegion }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="rightCore">
        <div
          class="finished"
          v-show="isFinished"
          :class="isFinished ? 'ani' : ''"
        >
          <div class="title">云专网连接</div>
          <div class="map" ref="chinamap"></div>
          <div class="textposi">{{ speed }}ms</div>
          <!-- <div class="imgCore">
            <div class="imgt ani"></div>
            <div class="flex-box-between">
              <div class="imgl"></div>
              <div class="imgl"></div>
            </div>
            <div class="l1" :class="l1Ani ? 'ani' : ''"></div>
            <div class="l2"></div>
            <div class="p1"></div>
            <div class="p2"></div>
            <div class="p3"></div>
          </div>
          <div class="text t1">互联组CG001(CN2)</div>
          <div class="text t2">{{ job1SelectRegion }}</div>
          <div class="text t3">{{ job2SelectRegion }}</div>
          <div class="text t4">10ms</div> -->
        </div>
        <div
          class="finished2"
          v-show="isFinished"
          :class="isFinished ? 'ani' : ''"
        >
          <div class="title">待完成作业</div>
          <div class="flex-box-between">
            <div
              class="centerCore2 left flex-box"
              :class="isFinished ? 'opa' : ''"
            >
              <div class="wordCore">
                <div class="top flex-box-center">
                  <div class="t1">
                    7
                    <p>队列中</p>
                  </div>
                  <div class="linec"></div>
                  <div class="t1">
                    11
                    <p>执行中</p>
                  </div>
                </div>
                <div class="line2"></div>
                <div class="t2">智算作业</div>
              </div>
            </div>
            <div
              class="centerCore2 left flex-box"
              :class="isFinished ? 'opa' : ''"
            >
              <div class="wordCore">
                <div class="top flex-box-center">
                  <div class="t1">
                    5
                    <p>队列中</p>
                  </div>
                  <div class="linec"></div>
                  <div class="t1">
                    10
                    <p>执行中</p>
                  </div>
                </div>
                <div class="line2"></div>
                <div class="t2">通算作业</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getStore, setStore } from "@/common/util";
import VueMatrixRaindrop2 from "../vueMatrixDigitRain/index2.vue";
import * as d3 from "d3"; // d3
import roundCommon from "../round/Index";
import * as echarts from "echarts";
import chinaMap from "@/assets/json/geoJson.json";

export default {
  name: "workstep1",
  components: {
    VueMatrixRaindrop2,
    roundCommon,
  },
  props: {
    job1SelectRegion: {
      type: String,
      default: "华为云-上海一",
    },
    job1SelectType: {
      type: String,
      default: "智能推荐",
    },
    job2SelectRegion: {
      type: String,
      default: "天翼云-苏州(合营)",
    },
    job2SelectType: {
      type: String,
      default: "智能推荐",
    },
  },
  data() {
    return {
      point: [],
      percent: 0,
      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      wordAni4: false,
      wordAni5: false,
      wordAni6: false,
      iconAni1: true,
      iconAni2: false,
      iconAni3: true,
      iconAni4: false,
      showCode: false,
      timer: null,
      itemAni1: false,
      itemAni2: false,
      percent: 0,
      percent2: 0,
      percentdesc: "上传训练数据集...",
      percentdesc2: "作业入池中...",
      percentdesc3: "作业入池中...",
      wordBottomAni: false,
      imgItemAni: false,
      imgItemAni2: false,
      time1: 0,
      time2: 0,
      time1Timer: null,
      time2Timer: null,
      timeDate: "",
      imgLeftAni: false,
      imgRightAni: false,
      ponit1Index: null,
      ponit2Index: null,
      pointAnitimer1: null,
      pointAnitimer2: null,
      wordBottomAni2: null,
      isAuto: null,
      centerOpa: false,
      rightOpa: false,

      cwordAni1: false,
      cwordAni2: false,
      cwordAni3: false,
      cwordAni4: false,
      cwordAni5: false,
      ccAni1: false,
      ccAni2: false,
      ccAni3: false,
      ccAni4: false,
      sixItemSonAni1: false,
      sixItemSonAni2: false,
      sixItemSonAni3: false,
      sixItemSonAni4: false,
      sixItemSonAni5: false,
      sixItemSonAni6: false,
      rightValBoxitem1: false,
      rightValBoxitem2: false,
      rightValBoxitem3: false,
      rightValBoxitem4: false,
      rightValBoxitem5: false,
      rightValBoxitem6: false,
      rightValBoxitem7: false,
      rightValBoxitem8: false,
      rightValBoxitemChoose: false,

      cwordAni1_2: false,
      cwordAni2_2: false,
      cwordAni3_2: false,
      cwordAni4_2: false,
      cwordAni5_2: false,
      ccAni1_2: false,
      ccAni2_2: false,
      ccAni3_2: false,
      ccAni4_2: false,
      sixItemSonAni1_2: false,
      sixItemSonAni2_2: false,
      sixItemSonAni3_2: false,
      sixItemSonAni4_2: false,
      sixItemSonAni5_2: false,
      sixItemSonAni6_2: false,
      rightValBoxitem1_2: false,
      rightValBoxitem2_2: false,
      rightValBoxitem3_2: false,
      rightValBoxitem4_2: false,
      rightValBoxitem5_2: false,
      rightValBoxitem6_2: false,
      rightValBoxitem7_2: false,
      rightValBoxitem8_2: false,
      rightValBoxitemChoose_2: false,

      rightOpa1: false,
      rightOpa2: false,
      word1: "设置中",
      word1_2: "设置中",
      showWorkSecond: false,
      ponit1Index: null,
      ponit: [],
      centerOpa1: false,
      centerOpa2: false,
      ccAni1: false,
      ccAni2: false,
      ccAni3: false,
      ccAni4: false,
      ccAni5: false,
      ccAni1_2: false,
      ccAni2_2: false,
      ccAni2_3: false,
      chooseAni: false,
      bottomTitleAni1: false,
      bottomTitleAni2: false,
      isFinished: false,
      l1Ani: false,
      speed: 43,
      timerspeed: null,
    };
  },
  created() {
    echarts.registerMap("china", { geoJSON: chinaMap });
    this.isAuto = this.GLOBAL.isAuto;
  },
  mounted() {
    this.init();
    this.speedRandom();
  },
  methods: {
    speedRandom() {
      this.timerspeed = setInterval(() => {
        this.speed = parseInt(Math.random() * (14 - 11) + 11);
      }, 1000);
      this.GLOBAL.timerArraySet.push(this.timerspeed);
    },
    initMap() {
      let points = [
        {
          value: [121.48054, 31.23593],
          name: "上海",
          itemStyle: { color: "#21D571" },
        },
        {
          value: [113.13947, 41.00075],
          name: "乌兰察布",
          itemStyle: { color: "#21D571" },
        },
        {
          value: [114.89257, 40.77324],
          name: "张家口",
          itemStyle: { color: "#21D571" },
        },
        {
          value: [113.27143, 23.83534],
          name: "广州",
          itemStyle: { color: "#21D571" },
        },
        {
          value: [106.63658, 26.65332],
          name: "贵阳",
          itemStyle: { color: "#21D571" },
        },
        {
          value: [111.70085, 40.82298],
          name: "内蒙",
          itemStyle: { color: "#21D571" },
        },
        {
          value: [120.586, 31.297301],
          name: "苏州",
          itemStyle: { color: "#21D571" },
        },
      ];
      let chooseLngLat = [];
      let chooseLngLat2 = [];
      let points2 = [
        {
          value: [116.41339, 39.91092],
          name: "北京",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [119.41942, 32.70068],
          name: "扬州",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [121.48054, 31.23593],
          name: "上海",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [103.04954, 31.01679],
          name: "雅安",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [106.55844, 29.569],
          name: "重庆",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [106.50192, 26.4421],
          name: "贵安",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [118.09643, 24.78541],
          name: "厦门",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [113.27143, 23.83534],
          name: "广州",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [114.18732, 22.24966],
          name: "香港",
          itemStyle: { color: "#158EFF" },
        },
      ];
      let list = points.filter(
        (item) => this.job1SelectRegion.indexOf(item.name) > -1
      );
      let list2 = points.filter(
        (item) => this.job2SelectRegion.indexOf(item.name) > -1
      );
      let newlist = list.concat(list2);
      points = points.concat(list);
      chooseLngLat = list[0].value || [121.48054, 31.23593];
      chooseLngLat2 = list2[0].value || [120.586, 31.297301];
      const zoom = 10
      const center = [120.586, 31.297301]
      let option = {
        backgroundColor: "",
        geo: {
          map: "china",
          aspectScale: 0.72, //长宽比
          zoom: zoom,
          center: center,
          roam: false,
          label: {
            show: false,
          },
          emphasis: {
            disabled: false,
            itemStyle: {
              normal: {
                areaColor: {
                  type: "radial",
                  x: 0.3,
                  y: 0.3,
                  r: 0.8,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#09132c", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#274d68", // 100% 处的颜色
                    },
                  ],
                  globalCoord: true, // 缺省为 false
                },
                shadowColor: "#618198",
                shadowOffsetX: 5,
                shadowOffsetY: 5,
              },
            },
          },
          itemStyle: {
            normal: {
              areaColor: {
                type: "radial",
                x: 0.3,
                y: 0.3,
                r: 0.8,
                colorStops: [
                  {
                    offset: 0,
                    color: "#09132c", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#274d68", // 100% 处的颜色
                  },
                ],
                globalCoord: true, // 缺省为 false
              },
              shadowColor: "#618198",
              shadowOffsetX: 5,
              shadowOffsetY: 5,
            },
          },
          regions: [
            {
              name: "南海诸岛",
              itemStyle: {
                areaColor: "rgba(0, 10, 52, 1)",
                borderColor: "rgba(0, 10, 52, 1)",
                normal: {
                  opacity: 0,
                  label: {
                    show: false,
                    color: "#009cc9",
                  },
                },
              },
            },
          ],
        },
        series: [
          {
            type: "map",
            roam: false,
            label: {
              normal: {
                show: false,
                textStyle: {
                  color: "#888",
                },
              },
              emphasis: {
                show: false,
                disabled: false,
                textStyle: {
                  color: "rgb(183,185,14)",
                },
              },
            },
            selectedMode: false,
            emphasis: {
              disabled: true,
            },

            itemStyle: {
              normal: {
                borderColor: "rgb(140, 140, 140)",
                borderWidth: 1,
                areaColor: {
                  type: "radial",
                  x: 0.5,
                  y: 0.5,
                  r: 0.8,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#09132c", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#274d68", // 100% 处的颜色
                    },
                  ],
                  globalCoord: true, // 缺省为 false
                },
              },
              emphasis: {
                show: false,
                disabled: true,
                // areaColor: 'rgb(46,229,206)',
                //    shadowColor: 'rgb(12,25,50)',
                borderWidth: 0.1,
              },
            },
            zoom: zoom,
            center: center,
            //     roam: false,
            map: "china", //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
          },
          {
            type: "effectScatter",
            coordinateSystem: "geo",
            showEffectOn: "render",
            zlevel: 1,
            rippleEffect: {
              number: 1,
              period: 1,
              scale: 3,
              brushType: "fill",
            },
            hoverAnimation: false,
            label: {
              show: true,
              formatter: "{b}",
              position: "bottom",
              offset: [0, 5],
              color: "#ffffff",
            },
            itemStyle: {
              color: "#1DE9B6",
              shadowBlur: 2,
              shadowColor: "#333",
            },
            symbolSize: 8,
            data: newlist,
          },
          //地图线的动画效果
          {
            type: "lines",
            zlevel: 2,
            polyline: false,
            effect: {
              show: true,
              period: 2, //箭头指向速度，值越小速度越快
              trailLength: 0.4, //特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: "arrow", //箭头图标
              symbolSize: 5, //图标大小
              roundTrip: true,
            },
            lineStyle: {
              color: "#1DE9B6",
              type: "dashed",
              width: 1, //线条宽度
              opacity: 0.1, //尾迹线条透明度
              curveness: -0.3, //尾迹线条曲直度
            },
            label: {
              show: true,
              position: "end",
              formatter: "{@val}",
              color: "#1DE9B6",
              fontSize: 16,
            },
            data: [{ coords: [chooseLngLat, chooseLngLat2], val: "10ms" }],
          },
        ],
      };
      if (!this.mapChart) {
        this.mapChart = echarts.init(this.$refs.chinamap, null, {
          width: this.GLOBAL.relPx(430),
          height: this.GLOBAL.relPx(310),
        });
        this.GLOBAL.echartsDomArray.push(this.mapChart);
      }
      this.mapChart.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.mapChart.setOption(option);
    },
    init() {
      let arr = [];
      for (let i = 0; i < 44; i++) {
        arr.push(i);
      }
      this.point = arr;
      let settimer1 = setTimeout(() => {
        this.wordAni1 = true;
      }, 400);
      let settimer2 = setTimeout(() => {
        this.wordAni2 = true;
      }, 800);
      let settimer3 = setTimeout(() => {
        this.wordAni3 = true;
        this.centerOpa = true;
      }, 1200);
      let settimer4 = setTimeout(() => {
        this.wordAni4 = true;
        this.centerOpa1 = true;
      }, 1600);
      let settimer5 = setTimeout(() => {
        this.wordAni5 = true;
        this.ccAni1 = true;
      }, 2400);
      let settimer6 = setTimeout(() => {
        this.wordAni6 = true;
        this.ccAni2 = true;
        this.bottomTitleAni1 = true;
      }, 3200);
      let settimer7 = setTimeout(() => {
        this.centerOpa2 = true;
        this.ccAni3 = true;
        this.rightOpa1 = true;
      }, 4000);
      let settimer8 = setTimeout(() => {
        this.ccAni4 = true;
      }, 4800);
      let settimer9 = setTimeout(() => {
        this.ccAni4 = true;
      }, 5600);
      let settimer10 = setTimeout(() => {
        this.ccAni5 = true;
        this.ccAni1_2 = true;
        this.chooseAni = true;
        this.iconAni1 = false;
        this.iconAni2 = true;
        this.word1 = "设置完成";
      }, 6400);
      let settimer11 = setTimeout(() => {
        this.ccAni2_2 = true;
      }, 7200);
      let settimer12 = setTimeout(() => {
        this.ccAni2_3 = true;
        this.rightOpa2 = true;
        this.isFinished = true;
        this.initMap();
        this.l1Ani = true;
        this.bottomTitleAni2 = true;
        this.iconAni3 = false;
        this.iconAni4 = true;
        this.word1_2 = "设置完成";
        this.$emit("stepEnd", 3);
      }, 8000);

      this.$once("hook:beforeDestroy", () => {
        clearTimeout(settimer1);
        settimer1 = null;
        clearTimeout(settimer2);
        settimer2 = null;
        clearTimeout(settimer3);
        settimer3 = null;
        clearTimeout(settimer4);
        settimer4 = null;
        clearTimeout(settimer5);
        settimer5 = null;
        clearTimeout(settimer6);
        settimer6 = null;
        clearTimeout(settimer7);
        settimer7 = null;
        clearTimeout(settimer8);
        settimer8 = null;
        clearTimeout(settimer9);
        settimer9 = null;
        clearTimeout(settimer10);
        settimer10 = null;
        clearTimeout(settimer11);
        settimer11 = null;
        clearTimeout(settimer12);
        settimer12 = null;
      });
    },
    pointAni() {
      this.ponit1Index = 0;
      this.pointAnitimer1 = setInterval(() => {
        this.ponit1Index += 1;
        if (this.ponit1Index > 44) {
          this.iconAni1 = false;
          this.iconAni2 = true;
          this.word1 = "设置完成";
          clearInterval(this.pointAnitimer1);
        }
      }, 100);
    },
    pointAni2() {
      this.ponit2Index = 0;
      this.pointAnitimer2 = setInterval(() => {
        this.ponit2Index += 1;
        if (this.ponit2Index > 44) {
          this.iconAni3 = false;
          this.iconAni4 = true;
          this.word1_2 = "设置完成";
          clearInterval(this.pointAnitimer2);
          this.$emit("stepEnd", 3);
        }
      }, 60);
    },
    endFun(index) {
      if (this.isAuto) {
        return false;
      }
      this.$emit("endFun", index);
    },
    resetFun() {
      if (this.pointAnitimer1) {
        clearInterval(this.pointAnitimer1);
      }
      if (this.pointAnitimer2) {
        clearInterval(this.pointAnitimer2);
      }
    },
  },
  beforeDestroy() {
    this.resetFun();
  },
};
</script>

<style lang="scss">
.pageWork2 {
  .centerCore2 {
    opacity: 0;
    width: 605px;

    .topList {
      position: relative;

      .item {
        opacity: 0;

        .leftIcon {
          position: relative;

          .icon {
            width: 34px;
            height: 34px;
            margin-right: 23px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .text {
            font-size: 16px;
            line-height: 34px;
            width: 90px;
          }

          .arrow {
            background-image: url("~@/assets/images/1920/work/step2/arrow.png");
            background-size: contain;
            width: 16px;
            height: 10px;
            position: absolute;
            z-index: 2;
            top: 45px;
            left: 8px;
            cursor: pointer;

            &.on {
              transform: rotate(180deg);
            }
          }

          &:first-child {
          }

          &.long {
            margin-top: 45px;
          }
        }

        .cc {
          position: relative;
          background-color: rgba($color: #26262a, $alpha: 0.15);
          border-radius: 4px;
          margin-bottom: 30px;
        }

        .cc1 {
          width: 458px;
          height: 138px;

          .smallItemList {
            box-sizing: border-box;
            padding: 21px 17px;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            width: 100%;
            height: 100%;

            .smallItem {
              width: 203px;
              height: 36px;
              // background: rgba($color: #26262A, $alpha: 0.5);
              border: 1px solid #333;
              border-radius: 4px;
              font-size: 16px;
              text-align: center;
              line-height: 34px;
              opacity: 0;
              margin-bottom: 20px;

              &.on {
                animation: imgOpacity 1s linear 1 forwards;
              }
            }
          }
        }

        .cc2 {
          width: 458px;
          // height: 63px;

          .sixItem {
            padding: 14px 40px 14px 40px;

            .sixItemSon {
              .text {
                font-size: 18px;
                line-height: 16px;
              }

              .point {
                width: 10px;
                height: 10px;
                background: #666666;
                border-radius: 50%;
                margin: 0 auto;
                margin-top: 10px;
              }

              &.on {
                .point {
                  background: #21d571;
                }
              }

              &.hide {
              }
            }
          }

          .roomList {
            opacity: 0;

            &.ani {
              animation: imgOpacity 1s linear 1 forwards;
            }

            .roomitem {
              padding: 16px 28px 0 28px;
              margin-bottom: 50px;
              font-size: 16px;
              line-height: 22px;
              opacity: 0;

              &.ani {
                animation: imgOpacity 1s linear 1 forwards;
              }

              .rt {
                margin-top: 5px;
              }

              .le {
                margin-top: 12px;
              }

              .icon {
                background-image: url("~@/assets/images/1920/work/step2/item.png");
                background-size: contain;
                width: 21px;
                height: 22px;
                margin-right: 10px;
              }

              .text1 {
                margin-right: 60px;
              }

              &.on {
                color: #00e4ff;

                .icon {
                  background-image: url("~@/assets/images/1920/work/step2/item_on.png");
                }
              }
            }
          }
        }

        .cc3 {
          width: 967px;
          height: 126px;

          .bg {
            background-image: url("~@/assets/images/page4/step1/cc3-bg.png");
            background-size: cover;
            width: 93px;
            height: 103px;
            position: absolute;
            z-index: 0;
            top: 50%;
            left: 50%;
            margin-top: -51.5px;
            margin-left: -46.5px;
          }

          .rightValBoxitem {
            width: 25%;
            height: 50%;
            box-sizing: border-box;
            text-align: center;
            border-left: 1px dashed rgba($color: #ffffff, $alpha: 0.3);
            border-bottom: 1px dashed rgba($color: #ffffff, $alpha: 0.3);
            padding-top: 20px;

            p {
              margin: 0;
              font-size: 18px;
              line-height: 18px;

              span {
                font-size: 14px;
              }

              &:first-child {
                margin-bottom: 8px;
              }
            }

            &:nth-child(1),
            &:nth-child(5) {
              border-left: none;
            }

            &:nth-child(5),
            &:nth-child(6),
            &:nth-child(7),
            &:nth-child(8) {
              border-bottom: none;
            }

            &.on {
              color: #00e3fb;
            }

            &.hide {
              p {
                animation: imgOpacity2 0.5s linear 1 forwards;
              }
            }
          }
        }

        .cc4 {
          width: 458px;

          .usepercent {
            padding-top: 37px;

            .useitem {
              width: 136px;
              height: 106px;
              background: rgba($color: #26262a, $alpha: 0.15);
              border-radius: 4px;
              box-sizing: border-box;
              padding-top: 6px;

              .useitemtext {
                font-size: 14px;
                text-align: center;
                padding-top: 10px;
              }

              &.on {
                margin-right: 26px;
              }
            }
          }

          .left {
            width: 126px;
            height: 40px;
            background: #2689e0;
            border-radius: 4px;
            font-size: 18px;
            text-align: center;
            line-height: 40px;

            p {
              margin: 0;

              &.ss {
                font-size: 14px;
              }
            }

            &.on {
              background: #26a6e0;
            }
          }

          .right {
            padding-left: 30px;
            font-size: 16px;
            line-height: 40px;

            span {
              margin-right: 20px;
            }
          }
        }

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }

      .leftLine {
        width: 1px;
        border-left: 1px dashed rgba($color: #16d0ff, $alpha: 0.5);
        opacity: 0.5;
        left: 17px;
        position: absolute;
        z-index: 1;
        opacity: 0;

        &.len1 {
          height: 129px;
          top: 37px;
        }

        &.len2 {
          height: 118px;
          top: 187px;
        }

        &.len3 {
          height: 390px;
          top: 208px;
        }

        &.len4 {
          height: 170px;
          top: 37px;
        }

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }

    .bottomTitle {
      font-size: 28px;
      text-align: center;
      padding-top: 10px;
      opacity: 0;

      &.ani {
        animation: imgOpacity 1s linear 1 forwards;
      }
    }

    &.opa {
      opacity: 1;
    }

    &.left {
      width: 276px;
    }

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .wordCore {
    box-sizing: border-box;
    margin-top: 30px;
    padding-top: 15px;
    text-align: center;
    text-align: center;
    width: 276px;
    height: 194px;
    // background: rgba($color: #26262A, $alpha: 0.15);
    border-radius: 4px;

    .t1 {
      font-size: 46px;
      line-height: 46px;
      color: #ffffff;
      width: 130px;
      padding-top: 20px;

      p {
        font-size: 16px;
        margin: 0;
      }
    }

    .line2 {
      width: 30px;
      height: 3px;
      background: #1b81df;
      margin: 0 auto;
      margin-top: 20px;
    }

    .t2 {
      font-size: 18px;
      margin-top: 10px;
    }

    .linec {
      background-image: url("~@/assets/images/1920/work2/line.png");
      background-size: cover;
      width: 1px;
      height: 98px;
    }

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .rightBox {
    width: 1222px;
    box-sizing: border-box;

    .centerCore {
      .core {
        .item {
          width: 580px;
          background: rgba($color: #26262a, $alpha: 0.15);
          border-radius: 4px;
          box-sizing: border-box;
          padding: 24px 20px;
          opacity: 0;
          margin-bottom: 30px;
          padding-bottom: 1px;
          height: 380px;

          .son {
            margin-bottom: 35px;
            opacity: 0;

            .ict {
              .icon {
                width: 21px;
                height: 21px;
                margin-right: 10px;

                img {
                  width: 100%;
                  height: 100%;
                }
              }

              .text {
                font-size: 16px;
                line-height: 21px;
              }

              &.on {
                padding-left: 30px;
              }
            }

            .pointList {
              margin-top: 20px;

              .point {
                width: 100%;

                .pointson {
                  width: 10px;
                  height: 10px;
                  border-radius: 50%;
                  background-color: rgba($color: #7fb41c, $alpha: 0.4);
                  margin: 10px 7px;

                  &.on {
                    background-color: rgba($color: #3174f3, $alpha: 1);
                  }

                  &.s2 {
                    &.on {
                      background-color: rgba($color: #00c2fc, $alpha: 1);
                    }
                  }
                }
              }
            }

            .textBox {
              margin-top: 20px;
              font-size: 18px;
              color: #999999;
              line-height: 16px;
            }

            .itemImgList {
              margin-top: 20px;
              padding: 0 60px;

              .imgSon {
                .img {
                  width: 68px;
                  height: 49px;

                  img {
                    width: 100%;
                    height: 100%;
                  }
                }

                .dp {
                  width: 10px;
                  height: 10px;
                  background: #666666;
                  border-radius: 50%;
                  margin: 15px auto 0 auto;

                  &.on {
                    background: #21d571;
                  }
                }
              }
            }

            &.ani {
              animation: imgOpacity 1s linear 1 forwards;
            }
          }

          &.ani {
            animation: imgOpacity 0.5s linear 1 forwards;
          }
        }
      }

      .bottomTitle {
        font-size: 28px;
        text-align: center;
        padding-top: 70px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }

      &.opa {
        opacity: 1;
      }
    }

    .rightCore {
      .finished {
        width: 576px;
        height: 380px;
        background: rgba($color: #26262a, $alpha: 0.15);
        border-radius: 4px;
        box-sizing: border-box;
        position: relative;

        .title {
          padding-top: 15px;
          padding-left: 20px;
          font-size: 18px;
          // margin-bottom: 50px;
        }

        .map {
          width: 430px;
          margin: 0 auto;
        }

        .textposi {
          font-size: 16px;
          color: #17e372;
          position: absolute;
          bottom: 168px;
          right: 170px;
          z-index: 1;
        }

        .img {
          width: 348px;
          height: 204px;
          background-image: url("~@/assets/images/1920/work/step2/img.png");
          background-size: cover;
          margin: 0 auto;
        }

        .imgCore {
          width: 348px;
          height: 204px;
          margin: 0 auto;
          position: relative;

          .imgt {
            background-image: url("~@/assets/images/1920/work/step2/imgt.png");
            background-size: cover;
            width: 94px;
            height: 94px;
            margin: 0 auto;
            margin-bottom: 30px;

            &.ani {
              animation: rotateAni 2s linear infinite forwards;
            }
          }

          .imgl {
            background-image: url("~@/assets/images/1920/work/step2/imglr.png");
            background-size: cover;
            width: 70px;
            height: 77px;
            margin-left: 5px;

            &:nth-child(2) {
              margin-right: -10px;
            }
          }

          .l1 {
            background-image: url("~@/assets/images/1920/work/step2/l1.png");
            background-size: cover;
            width: 284px;
            height: 0;
            position: absolute;
            top: 45px;
            left: 40px;
            z-index: 5;

            &.ani {
              animation: heightAni2 1s linear 1 forwards;
            }
          }

          .l2 {
            background-image: url("~@/assets/images/1920/work/step2/l2.png");
            background-size: cover;
            width: 283px;
            height: 31px;
            position: absolute;
            top: 150px;
            left: 40px;
            z-index: 5;
          }

          .p1 {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            overflow: hidden;
            background-color: #00cfa5;
            position: absolute;
            z-index: 5;
            top: 40px;
            left: 170px;
          }

          .p2 {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            overflow: hidden;
            background-color: #00cfa5;
            position: absolute;
            z-index: 5;
            top: 140px;
            left: 35px;
          }

          .p3 {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            overflow: hidden;
            background-color: #00cfa5;
            position: absolute;
            z-index: 5;
            top: 140px;
            right: 20px;
          }
        }

        .bottomtext {
          font-size: 18px;
          text-align: center;
          margin-top: 80px;
        }

        .text {
          font-size: 16px;
          position: absolute;
          z-index: 1;

          &.t1 {
            top: 53px;
            left: 228px;
          }

          &.t2 {
            top: 299px;
            left: 99px;
          }

          &.t3 {
            top: 299px;
            left: 371px;
          }

          &.t4 {
            top: 277px;
            left: 275px;
            color: #00cfa5;
          }
        }

        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }

      .finished2 {
        width: 576px;
        height: 380px;
        background: rgba($color: #26262a, $alpha: 0.15);
        border-radius: 4px;
        box-sizing: border-box;
        position: relative;
        margin-top: 30px;

        .item {
          width: calc(50% - 1px);
          text-align: center;
          padding-top: 112px;

          .t1 {
            font-size: 60px;
            line-height: 60px;
          }

          .t2 {
            font-size: 18px;
            margin-top: 40px;
          }
        }

        .line {
          height: 299px;
          width: 1px;
          background-image: url("~@/assets/images/1920/work/step2/line.png");
          background-size: cover;
        }

        .title {
          padding-top: 15px;
          padding-left: 20px;
          font-size: 18px;
          margin-bottom: 50px;
        }

        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }
  }
}

@keyframes imgOpacity {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes imgOpacity2 {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0.2;
  }
}

@keyframes rotateAni {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes heightAni2 {
  0% {
    height: 0;
  }

  100% {
    height: 99px;
  }
}
</style>
