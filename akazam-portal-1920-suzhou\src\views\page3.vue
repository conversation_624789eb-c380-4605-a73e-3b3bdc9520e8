<template>
  <div class="pageCommon page3">
    <!-- 头部-s -->
    <header-common :icon="4" :name="'全国多元算力资源'"></header-common>
    <!-- 头部-e -->
    <div class="mainBox flex-box-between">
      <div class="leftMap">
        <div class="map2" ref="map2"></div>
        <div class="posiCore">
          <div class="posiitem  flex-box" v-for="(item, index) in provincePosiList" :key="index"
            :class="`posi${index + 1}`" v-show="item.iconts > 0 || item.iconzs > 0 || item.iconcs > 0">
            <div class="core">
              <div class="flex-box-center">
                <div class="icon icon1"
                  :class="((typeVal && typeVal != 'ts') || (typeYs === 'tyy' && item.tyyNumTs === 0) || (typeYs === 'aly' && item.alyNumTs === 0) || (typeYs === 'hwy' && item.hwyNumTs === 0) || (typeYs === 'txy' && item.txyNumTs === 0) || (typeYs === 'wry' && item.wryNumTs === 0) || (typeYs === 'aws' && item.awsNumTs === 0) || (typeYs === 'lggc' && item.lggcNumTs === 0) || (typeYs === 'yqsl' && item.yqslNumTs === 0) || (typeYs === 'shggsl' && item.shggslNumTs === 0)) ? 'opa' : 'ani'"
                  v-if="item.iconts > 0" @click="posiClickMap(item.cname, 'ts')"></div>
                <div class="icon icon2"
                  :class="((typeVal && typeVal != 'zs') || (typeYs === 'tyy' && item.tyyNumZs === 0) || (typeYs === 'aly' && item.alyNumZs === 0) || (typeYs === 'hwy' && item.hwyNumZs === 0) || (typeYs === 'txy' && item.txyNumZs === 0) || (typeYs === 'wry' && item.wryNumZs === 0) || (typeYs === 'aws' && item.awsNumZs === 0) || (typeYs === 'lggc' && item.lggcNumZs === 0) || (typeYs === 'yqsl' && item.yqslNumZs === 0) || (typeYs === 'shggsl' && item.shggslNumZs === 0)) ? 'opa' : 'ani'"
                  v-if="item.iconzs > 0" @click="posiClickMap(item.cname, 'zs')"></div>
              </div>
              <div class="flex-box-center">
                <div class="icon icon3"
                  :class="((typeVal && typeVal != 'cs') || (typeYs === 'tyy' && item.tyyNumCs === 0) || (typeYs === 'aly' && item.alyNumCs === 0) || (typeYs === 'hwy' && item.hwyNumCs === 0) || (typeYs === 'txy' && item.txyNumCs === 0) || (typeYs === 'wry' && item.wryNumCs === 0) || (typeYs === 'aws' && item.awsNumCs === 0) || (typeYs === 'lggc' && item.lggcNumCs === 0) || (typeYs === 'yqsl' && item.yqslNumCs === 0) || (typeYs === 'shggsl' && item.shggslNumCs === 0)) ? 'opa' : 'ani'"
                  v-if="item.iconcs > 0" @click="posiClickMap(item.cname, 'cs')"></div>
              </div>
            </div>
            <div v-show="item.cname == posiname">
              <div class="posiCoreright flex-box ts" v-if="typeValSon == 'ts'">
                <div class="line"></div>
                <div class="info">
                  <div class="title">{{ item.cname }}-通算节点-{{ item.iconts }}</div>
                  <div class="titleline"></div>
                  <div class="list flex-box-center">
                    <div class="item" v-for="(son, sdx) in item.tslist" :key="sdx" v-show="son.num > 0">
                      <div class="icon">
                        <img v-if="sdx == 0" src="~@/assets/images/1920/page3/dic-1.png" alt="">
                        <img v-if="sdx == 1" src="~@/assets/images/1920/page3/dic-2.png" alt="">
                        <img v-if="sdx == 2" src="~@/assets/images/1920/page3/dic-3.png" alt="">
                        <img v-if="sdx == 3" src="~@/assets/images/1920/page3/dic-4.png" alt="">
                        <img v-if="sdx == 4" src="~@/assets/images/1920/page3/dic-5.png" alt="">
                        <img v-if="sdx == 5" src="~@/assets/images/1920/page3/dic-6.png" alt="">
                        <img v-if="sdx == 6 || sdx == 7" src="~@/assets/images/1920/page3/dic-78.png" alt="">
                        <img v-if="sdx == 8" src="~@/assets/images/1920/page3/dic-9.png" alt="">
                      </div>
                      <div>{{ son.name }}</div>
                      <div>{{ son.num }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="posiCoreright flex-box zs" v-if="typeValSon == 'zs'">
                <div class="line"></div>
                <div class="info">
                  <div class="title">{{ item.cname }}-智算节点-{{ item.iconzs }}</div>
                  <div class="titleline"></div>
                  <div class="list flex-box-center">
                    <div class="item" v-for="(son, sdx) in item.zslist" :key="sdx" v-show="son.num > 0">
                      <div class="icon">
                        <img v-if="sdx == 0" src="~@/assets/images/1920/page3/dic-1.png" alt="">
                        <img v-if="sdx == 1" src="~@/assets/images/1920/page3/dic-2.png" alt="">
                        <img v-if="sdx == 2" src="~@/assets/images/1920/page3/dic-3.png" alt="">
                        <img v-if="sdx == 3" src="~@/assets/images/1920/page3/dic-4.png" alt="">
                        <img v-if="sdx == 4" src="~@/assets/images/1920/page3/dic-5.png" alt="">
                        <img v-if="sdx == 5" src="~@/assets/images/1920/page3/dic-6.png" alt="">
                        <img v-if="sdx == 6 || sdx == 7" src="~@/assets/images/1920/page3/dic-78.png" alt="">
                        <img v-if="sdx == 8" src="~@/assets/images/1920/page3/dic-9.png" alt="">
                      </div>
                      <div>{{ son.name }}</div>
                      <div>{{ son.num }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="posiCoreright flex-box cs" v-if="typeValSon == 'cs'">
                <div class="line"></div>
                <div class="info">
                  <div class="title">{{ item.cname }}-超算节点-{{ item.iconcs }}</div>
                  <div class="titleline"></div>
                  <div class="list flex-box-center">
                    <div class="item" v-for="(son, sdx) in item.cslist" :key="sdx" v-show="son.num > 0">
                      <div class="icon">
                        <img v-if="sdx == 0" src="~@/assets/images/1920/page3/dic-1.png" alt="">
                        <img v-if="sdx == 1" src="~@/assets/images/1920/page3/dic-2.png" alt="">
                        <img v-if="sdx == 2" src="~@/assets/images/1920/page3/dic-3.png" alt="">
                        <img v-if="sdx == 3" src="~@/assets/images/1920/page3/dic-4.png" alt="">
                        <img v-if="sdx == 4" src="~@/assets/images/1920/page3/dic-5.png" alt="">
                        <img v-if="sdx == 5" src="~@/assets/images/1920/page3/dic-6.png" alt="">
                        <img v-if="sdx == 6 || sdx == 7" src="~@/assets/images/1920/page3/dic-78.png" alt="">
                        <img v-if="sdx == 8" src="~@/assets/images/1920/page3/dic-9.png" alt="">
                      </div>
                      <div>{{ son.name }}</div>
                      <div>{{ son.num }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="numberList">
          <div class="item flex-box-between">
            <div class="ss flex-box-center" @click="typeYsClick('tyy')"
              :class="typeYs && typeYs != 'tyy' ? 'hide' : ''">
              <!-- <div class="icon"><img src="~@/assets/images/page3/dic-1.png" alt=""></div> -->
              <div class="text">天翼云</div>
            </div>
            <div class="ss flex-box-center" @click="typeYsClick('aly')"
              :class="typeYs && typeYs != 'aly' ? 'hide' : ''">
              <!-- <div class="icon"><img src="~@/assets/images/page3/dic-3.png" alt=""></div> -->
              <div class="text">阿里云</div>
            </div>
            <div class="ss flex-box-center" @click="typeYsClick('hwy')"
              :class="typeYs && typeYs != 'hwy' ? 'hide' : ''">
              <!-- <div class="icon"><img src="~@/assets/images/page3/dic-4.png" alt=""></div> -->
              <div class="text">华为云</div>
            </div>
            <div class="ss flex-box-center" @click="typeYsClick('txy')"
              :class="typeYs && typeYs != 'txy' ? 'hide' : ''">
              <!-- <div class="icon"><img src="~@/assets/images/page3/dic-5.png" alt=""></div> -->
              <div class="text">腾讯云</div>
            </div>
            <div class="ss flex-box-center" @click="typeYsClick('wry')"
              :class="typeYs && typeYs != 'wry' ? 'hide' : ''">
              <!-- <div class="icon"><img src="~@/assets/images/page3/dic-6.png" alt=""></div> -->
              <div class="text">微软云</div>
            </div>
            <div class="ss flex-box-center" @click="typeYsClick('aws')"
              :class="typeYs && typeYs != 'aws' ? 'hide' : ''">
              <!-- <div class="icon"><img src="~@/assets/images/page3/dic-2.png" alt=""></div> -->
              <div class="text">AWS</div>
            </div>
            <div class="ss flex-box-center" @click="typeYsClick('lggc')"
              :class="typeYs && typeYs != 'lggc' ? 'hide' : ''">
              <!-- <div class="icon"><img src="~@/assets/images/page3/dic-2.png" alt=""></div> -->
              <div class="text">临港国产</div>
            </div>
            <div class="ss flex-box-center" @click="typeYsClick('yqsl')"
              :class="typeYs && typeYs != 'yqsl' ? 'hide' : ''">
              <!-- <div class="icon"><img src="~@/assets/images/page3/dic-2.png" alt=""></div> -->
              <div class="text">苏州智算中心1</div>
            </div>
            <div class="ss flex-box-center" @click="typeYsClick('shggsl')"
              :class="typeYs && typeYs != 'shggsl' ? 'hide' : ''">
              <!-- <div class="icon"><img src="~@/assets/images/page3/dic-2.png" alt=""></div> -->
              <div class="text">苏州智算中心2</div>
            </div>
          </div>
          <div class="item2 flex-box-between" :class="typeVal && typeVal != 'ts' ? 'opa' : ''">
            <div class="name flex-box" @click="tableClickMap('ts')"><img src="~@/assets/images/1920/page3/icon1_1.png"
                alt=""> 通算节点</div>
            <div class="ss" :class="typeYs && typeYs != 'tyy' ? 'hide' : ''">39</div>
            <div class="ss" :class="typeYs && typeYs != 'aly' ? 'hide' : ''">48</div>
            <div class="ss" :class="typeYs && typeYs != 'hwy' ? 'hide' : ''">11</div>
            <div class="ss" :class="typeYs && typeYs != 'txy' ? 'hide' : ''">26</div>
            <div class="ss" :class="typeYs && typeYs != 'wry' ? 'hide' : ''">4</div>
            <div class="ss" :class="typeYs && typeYs != 'aws' ? 'hide' : ''">1</div>
            <div class="ss" :class="typeYs && typeYs != 'lggc' ? 'hide' : ''">-</div>
            <div class="ss" :class="typeYs && typeYs != 'yqsl' ? 'hide' : ''">-</div>
            <div class="ss" :class="typeYs && typeYs != 'shggsl' ? 'hide' : ''">-</div>
          </div>
          <div class="item2 flex-box-between" :class="typeVal && typeVal != 'zs' ? 'opa' : ''">
            <div class="name flex-box" @click="tableClickMap('zs')"><img src="~@/assets/images/1920/page3/icon2_2.png"
                alt="">智算节点</div>
            <div class="ss" :class="typeYs && typeYs != 'tyy' ? 'hide' : ''">8</div>
            <div class="ss" :class="typeYs && typeYs != 'aly' ? 'hide' : ''">25</div>
            <div class="ss" :class="typeYs && typeYs != 'hwy' ? 'hide' : ''">15</div>
            <div class="ss" :class="typeYs && typeYs != 'txy' ? 'hide' : ''">-</div>
            <div class="ss" :class="typeYs && typeYs != 'wry' ? 'hide' : ''">-</div>
            <div class="ss" :class="typeYs && typeYs != 'aws' ? 'hide' : ''">-</div>
            <div class="ss" :class="typeYs && typeYs != 'lggc' ? 'hide' : ''">1</div>
            <div class="ss" :class="typeYs && typeYs != 'yqsl' ? 'hide' : ''">1</div>
            <div class="ss" :class="typeYs && typeYs != 'shggsl' ? 'hide' : ''">1</div>
          </div>
          <div class="item2 flex-box-between" :class="typeVal && typeVal != 'cs' ? 'opa' : ''">
            <div class="name flex-box" @click="tableClickMap('cs')"><img src="~@/assets/images/1920/page3/icon3_3.png"
                alt="">超算节点</div>
            <div class="ss" :class="typeYs && typeYs != 'tyy' ? 'hide' : ''">4</div>
            <div class="ss" :class="typeYs && typeYs != 'aly' ? 'hide' : ''">1</div>
            <div class="ss" :class="typeYs && typeYs != 'hwy' ? 'hide' : ''">-</div>
            <div class="ss" :class="typeYs && typeYs != 'txy' ? 'hide' : ''">-</div>
            <div class="ss" :class="typeYs && typeYs != 'wry' ? 'hide' : ''">-</div>
            <div class="ss" :class="typeYs && typeYs != 'aws' ? 'hide' : ''">-</div>
            <div class="ss" :class="typeYs && typeYs != 'lggc' ? 'hide' : ''">-</div>
            <div class="ss" :class="typeYs && typeYs != 'yqsl' ? 'hide' : ''">-</div>
            <div class="ss" :class="typeYs && typeYs != 'shggsl' ? 'hide' : ''">-</div>
          </div>
        </div>
      </div>
      <div class="leftCore">
        <!-- <div class="leftBg on">
          <div class="leftTitle">使用中资源</div>
          <div class="rightThree flex-box-between">
            <div class="item">
              <div class="bigSix" :class="rightThreeIndex && rightThreeIndex != 1 ? 'opa' : ''">
                <div class="core" @click="rightThreeFun(1)">
                  <div class="num">129<span>+</span></div>
                  <div class="label">资源池</div>
                </div>
                <div class="bigSixPosiCore" v-show="rightThreeIndex == 1">
                  <div class="item posi1" @click.stop="typeYsClick('tyy')" :class="typeYs == 'tyy' ? 'on' : ''">
                    <div class="num">20.88</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">天翼云</div>
                  </div>
                  <div class="item posi2" @click.stop="typeYsClick('aly')" :class="typeYs == 'aly' ? 'on' : ''">
                    <div class="num">15.22</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">阿里云</div>
                  </div>
                  <div class="item posi3" @click.stop="typeYsClick('hwy')" :class="typeYs == 'hwy' ? 'on' : ''">
                    <div class="num">16.33</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">华为云</div>
                  </div>
                  <div class="item posi4" @click.stop="typeYsClick('aws')" :class="typeYs == 'aws' ? 'on' : ''">
                    <div class="num">5.11</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">AWS</div>
                  </div>
                  <div class="item posi5" @click.stop="typeYsClick('txy')" :class="typeYs == 'txy' ? 'on' : ''">
                    <div class="num">4.22</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">腾讯云</div>
                  </div>
                  <div class="item posi6" @click.stop="typeYsClick('wry')" :class="typeYs == 'wry' ? 'on' : ''">
                    <div class="num">2.11</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">微软云</div>
                  </div>
                </div>
              </div>
              <div class="smallname flex-box-center" :class="typeVal == 'ts' ? 'tson' : ''" @click="tableClickMap('ts')">
                <img src="~@/assets/images/1920/page3/icon1_1.png" alt="">通算
              </div>
              <div class="line"></div>
              <div class="flex-box-between">
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon2.png" alt=""></div>
                    <div>CPU： </div>
                  </div>
                  <div>64核 </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon3.png" alt=""></div>
                    <div>内存： </div>
                  </div>
                  <div>128G </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon4.png" alt=""></div>
                    <div>存储： </div>
                  </div>
                  <div>680G </div>
                </div>
              </div>
            </div>
            <div class="item">
              <div class="bigSix" :class="rightThreeIndex && rightThreeIndex != 2 ? 'opa' : ''">
                <div class="core" @click="rightThreeFun(2)">
                  <div class="num">43<span>+</span></div>
                  <div class="label">资源池</div>
                </div>
                <div class="bigSixPosiCore" v-show="rightThreeIndex == 2">
                  <div class="item posi1" @click.stop="typeYsClick('tyy')" :class="typeYs == 'tyy' ? 'on' : ''">
                    <div class="num">12.55</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">天翼云</div>
                  </div>
                  <div class="item posi2" @click.stop="typeYsClick('hwy')" :class="typeYs == 'hwy' ? 'on' : ''">
                    <div class="num">10.52</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">华为云</div>
                  </div>
                  <div class="item posi3" @click.stop="typeYsClick('aly')" :class="typeYs == 'aly' ? 'on' : ''">
                    <div class="num">12.22</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">阿里云</div>
                  </div>
                  <div class="item posi4" @click.stop="typeYsClick('lggc')" :class="typeYs == 'lggc' ? 'on' : ''">
                    <div class="num">2.66</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">临港国产</div>
                  </div>
                  <div class="item posi5" @click.stop="typeYsClick('yqsl')" :class="typeYs == 'yqsl' ? 'on' : ''">
                    <div class="num">4.55</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">苏州智算中心1</div>
                  </div>
                  <div class="item posi6" @click.stop="typeYsClick('shggsl')" :class="typeYs == 'shggsl' ? 'on' : ''">
                    <div class="num">2.18</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">苏州智算中心2服务平台</div>
                  </div>
                </div>
              </div>
              <div class="smallname flex-box-center" :class="typeVal == 'zs' ? 'zson' : ''" @click="tableClickMap('zs')">
                <img src="~@/assets/images/1920/page3/icon2_2.png" alt="">智算
              </div>
              <div class="line"></div>
              <div class="flex-box-between">
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon1.png" alt=""></div>
                    <div>GPU： </div>
                  </div>
                  <div>32卡 </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon2.png" alt=""></div>
                    <div>CPU： </div>
                  </div>
                  <div>128核 </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon3.png" alt=""></div>
                    <div>内存： </div>
                  </div>
                  <div>256G </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon4.png" alt=""></div>
                    <div>存储： </div>
                  </div>
                  <div>960G </div>
                </div>
              </div>
            </div>
            <div class="item">
              <div class="bigSix" :class="rightThreeIndex && rightThreeIndex != 3 ? 'opa' : ''">
                <div class="core" @click="rightThreeFun(3)">
                  <div class="num">5<span>+</span></div>
                  <div class="label">资源池</div>
                </div>
                <div class="bigSixPosiCore" v-show="rightThreeIndex == 3">
                  <div class="item posi5" @click.stop="typeYsClick('tyy')" :class="typeYs == 'tyy' ? 'on' : ''">
                    <div class="num">5.69</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">天翼云</div>
                  </div>
                  <div class="item posi6" @click.stop="typeYsClick('aly')" :class="typeYs == 'aly' ? 'on' : ''">
                    <div class="num">2.33</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">阿里云</div>
                  </div>
                </div>
              </div>
              <div class="smallname flex-box-center" :class="typeVal == 'cs' ? 'cson' : ''" @click="tableClickMap('cs')">
                <img src="~@/assets/images/1920/page3/icon3_3.png" alt="">超算
              </div>
              <div class="line"></div>
              <div class="flex-box-between">
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon1.png" alt=""></div>
                    <div>GPU： </div>
                  </div>
                  <div>16卡 </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon2.png" alt=""></div>
                    <div>CPU： </div>
                  </div>
                  <div>128核 </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon3.png" alt=""></div>
                    <div>内存： </div>
                  </div>
                  <div>256G </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon4.png" alt=""></div>
                    <div>存储： </div>
                  </div>
                  <div>1080G </div>
                </div>
              </div>
            </div>
          </div>
        </div> -->
        <div class="leftBg on">
          <div class="leftTitle">使用中资源</div>
          <div class="rightThree flex-box-between">
            <div class="item">
              <div class="bigSix" :class="rightThreeIndex && rightThreeIndex != 1 ? 'opa' : ''">
                <div class="core" @click="rightThreeFun(1)">
                  <div class="num">129<span>+</span></div>
                  <div class="label">资源池</div>
                </div>
                <div class="bigSixPosiCore" v-show="rightThreeIndex == 1">
                  <div class="item posi1" @click.stop="typeYsClick('tyy')" :class="typeYs == 'tyy' ? 'on' : ''">
                    <div class="num">702</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">苏州</div>
                  </div>
                  <div class="item posi2" @click.stop="typeYsClick('aly')" :class="typeYs == 'aly' ? 'on' : ''">
                    <div class="num">15.22</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">阿里云</div>
                  </div>
                  <div class="item posi3" @click.stop="typeYsClick('hwy')" :class="typeYs == 'hwy' ? 'on' : ''">
                    <div class="num">16.33</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">华为云</div>
                  </div>
                  <div class="item posi4" @click.stop="typeYsClick('aws')" :class="typeYs == 'aws' ? 'on' : ''">
                    <div class="num">5.11</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">AWS</div>
                  </div>
                  <div class="item posi5" @click.stop="typeYsClick('txy')" :class="typeYs == 'txy' ? 'on' : ''">
                    <div class="num">4.22</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">腾讯云</div>
                  </div>
                  <div class="item posi6" @click.stop="typeYsClick('wry')" :class="typeYs == 'wry' ? 'on' : ''">
                    <div class="num">2.11</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">微软云</div>
                  </div>
                </div>
              </div>
              <div class="smallname flex-box-center" :class="typeVal == 'ts' ? 'tson' : ''"
                @click="tableClickMap('ts')">
                <img src="~@/assets/images/1920/page3/icon1_1.png" alt="">通算
              </div>
              <!-- <div class="line"></div> -->
              <!-- <div class="flex-box-between">
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon2.png" alt=""></div>
                    <div>CPU： </div>
                  </div>
                  <div>64核 </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon3.png" alt=""></div>
                    <div>内存： </div>
                  </div>
                  <div>128G </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon4.png" alt=""></div>
                    <div>存储： </div>
                  </div>
                  <div>680G </div>
                </div>
              </div> -->
            </div>
            <div class="item">
              <div class="bigSix" :class="rightThreeIndex && rightThreeIndex != 2 ? 'opa' : ''">
                <div class="core" @click="rightThreeFun(2)">
                  <div class="num">43<span>+</span></div>
                  <div class="label">资源池</div>
                </div>
                <div
                  :class="typeYs == 'lggc' || typeYs == 'yqsl' || typeYs == 'shggsl' ? 'bigSixPosiCore ' : 'bigSixPosiCore'"
                  v-show="rightThreeIndex == 2">
                  <div class="item posi1 bigSixPosiCoreHighlight on-high" @click.stop="typeYsClick('shggsl')"
                    :class="typeYs == 'lggc' ? 'on-high' : ''">
                    <div class="num">114.5</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">苏州电信</div>
                  </div>
                  <div class="item posi2 bigSixPosiCoreHighlight on-high" @click.stop="typeYsClick('yqsl')"
                    :class="typeYs == 'yqsl' ? 'on-high' : ''">
                    <div class="num">250</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">吴江智算中心</div>
                  </div>
                  <div class="item posi3" @click.stop="typeYsClick('hwy')" :class="typeYs == 'hwy' ? 'on' : ''">
                    <div class="num">10.52</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">华为云</div>
                  </div>
                  <div class="item posi4" @click.stop="typeYsClick('shggsl')" :class="typeYs == 'shggsl' ? 'on' : ''">
                    <div class="num">1875</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">上海电信临港</div>
                  </div>
                  <div class="item posi5" @click.stop="typeYsClick('aly')" :class="typeYs == 'aly' ? 'on' : ''">
                    <div class="num">12.2</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">阿里云</div>
                  </div>
                  <div class="item posi6" @click.stop="typeYsClick('tyy')" :class="typeYs == 'tyy' ? 'on' : ''">
                    <div class="num">12.55</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">天翼云</div>
                  </div>
                </div>
              </div>
              <div class="smallname flex-box-center" :class="typeVal == 'zs' ? 'zson' : ''"
                @click="tableClickMap('zs')">
                <img src="~@/assets/images/1920/page3/icon2_2.png" alt="">智算
              </div>
              <!-- <div class="line"></div> -->
              <!-- <div class="flex-box-between">
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon1.png" alt=""></div>
                    <div>GPU： </div>
                  </div>
                  <div>32卡 </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon2.png" alt=""></div>
                    <div>CPU： </div>
                  </div>
                  <div>128核 </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon3.png" alt=""></div>
                    <div>内存： </div>
                  </div>
                  <div>256G </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon4.png" alt=""></div>
                    <div>存储： </div>
                  </div>
                  <div>960G </div>
                </div>
              </div> -->
            </div>
            <div class="item">
              <div class="bigSix" :class="rightThreeIndex && rightThreeIndex != 3 ? 'opa' : ''">
                <div class="core" @click="rightThreeFun(3)">
                  <div class="num">5<span>+</span></div>
                  <div class="label">资源池</div>
                </div>
                <div class="bigSixPosiCore" v-show="rightThreeIndex == 3">
                  <div class="item posi5" @click.stop="typeYsClick('tyy')" :class="typeYs == 'tyy' ? 'on' : ''">
                    <div class="num">5.69</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">天翼云</div>
                  </div>
                  <div class="item posi6" @click.stop="typeYsClick('aly')" :class="typeYs == 'aly' ? 'on' : ''">
                    <div class="num">2.33</div>
                    <div class="ff">PFlpos</div>
                    <div class="nn">阿里云</div>
                  </div>
                </div>
              </div>
              <div class="smallname flex-box-center" :class="typeVal == 'cs' ? 'cson' : ''"
                @click="tableClickMap('cs')">
                <img src="~@/assets/images/1920/page3/icon3_3.png" alt="">超算
              </div>
              <!-- <div class="line"></div> -->
              <!-- <div class="flex-box-between">
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon1.png" alt=""></div>
                    <div>GPU： </div>
                  </div>
                  <div>16卡 </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon2.png" alt=""></div>
                    <div>CPU： </div>
                  </div>
                  <div>128核 </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon3.png" alt=""></div>
                    <div>内存： </div>
                  </div>
                  <div>256G </div>
                </div>
                <div class="son flex-box-between">
                  <div class="flex-box">
                    <div class="ic"><img src="~@/assets/images/1920/page3/r-icon4.png" alt=""></div>
                    <div>存储： </div>
                  </div>
                  <div>1080G </div>
                </div>
              </div> -->
            </div>
          </div>
        </div>
        <div class="leftBg on">
          <div class="leftTitle cur" @click="goWork">算力作业</div>
          <div class="downlist">
            <div class="item flex-box-between">
              <div class="lc">
                <div class="top flex-box-center">
                  <div class="ll">
                    <div class="num">{{ rnum1 }}</div>
                    <div class="label">作业数</div>
                    <div class="point blue"></div>
                  </div>
                  <div class="line"></div>
                  <div class="ll">
                    <div class="num">{{ rnum2 }}</div>
                    <div class="label">队列数</div>
                    <div class="point green"></div>
                  </div>
                </div>
                <div class="down flex-box-center">
                  <div class="icon"><img src="~@/assets/images/1920/page3/icon1_1.png" alt=""></div>
                  <div class="word">通算</div>
                </div>
              </div>
              <div class="rc">
                <div class="dec" ref="dec1"></div>
              </div>
            </div>
            <div class="item flex-box-between">
              <div class="lc">
                <div class="top flex-box-center">
                  <div class="ll">
                    <div class="num">{{ rnum3 }}</div>
                    <div class="label">作业数</div>
                    <div class="point blue"></div>
                  </div>
                  <div class="line"></div>
                  <div class="ll">
                    <div class="num">{{ rnum4 }}</div>
                    <div class="label">队列数</div>
                    <div class="point green"></div>
                  </div>
                </div>
                <div class="down flex-box-center">
                  <div class="icon"><img src="~@/assets/images/1920/page3/icon2_2.png" alt=""></div>
                  <div class="word">智算</div>
                </div>
              </div>
              <div class="rc">
                <div class="dec" ref="dec2"></div>
              </div>
            </div>
            <div class="item flex-box-between">
              <div class="lc">
                <div class="top flex-box-center">
                  <div class="ll">
                    <div class="num">{{ rnum5 }}</div>
                    <div class="label">作业数</div>
                    <div class="point blue"></div>
                  </div>
                  <div class="line"></div>
                  <div class="ll">
                    <div class="num">{{ rnum6 }}</div>
                    <div class="label">队列数</div>
                    <div class="point green"></div>
                  </div>
                </div>
                <div class="down flex-box-center">
                  <div class="icon"><img src="~@/assets/images/1920/page3/icon3_3.png" alt=""></div>
                  <div class="word">超算</div>
                </div>
              </div>
              <div class="rc">
                <div class="dec" ref="dec3"></div>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="leftBg on">
          <div class="leftTitle">高频调用资源池</div>
          <div class="ecBox flex-box-between">
            <div>
              <div class="ecRightBottom" ref="ecRightBottom1"></div>
              <div class="name" @click="openDialogRound('all', 'E')">东部资源池</div>
            </div>
            <div>
              <div class="ecRightBottom" ref="ecRightBottom2"></div>
              <div class="name" @click="openDialogRound('all', 'W')">西部资源池</div>
            </div>
          </div>
          <div class="ecPosi" v-show="rightTopShow">
            <div class="titleBox flex-box-between">
              <div class="title">{{ listType === 'E' ? '东' : '西' }}部{{ listFilter === 'all' ? '' : listFilter === 'ts' ?
                '通算'
                : listFilter === 'zs' ? '智算' : '超算' }}高频调用资源池</div>
              <div class="close" @click="rightTopShow = false"></div>
            </div>
            <div class="roundList flex-box" :class="ecShowIndex === 2 || ecShowIndex === 3 ? '' : ''">
              @click="posiClick(index)" 
              <div class="rounditem" v-for="(item, index) in listType == 'E' ? listE : listW" :key="'list1-' + index"
                v-show="!listFilter || listFilter == 'all' || listFilter == item.type1" @click="rounditemIndexFun(index)">
                <round-common :value="item.cpuval" :name="item.name" :type="item.type"></round-common>
                <div class="roundText">{{ item.name }}</div>
                <div class="roundPosi flex-box-center" v-show="rounditemIndex === index">
                  <div class="posiSon">
                    <div class="tp">{{ item.gpuval }}<span>%</span></div>
                    <div class="dn">GPU</div>
                  </div>
                  <div class="line"></div>
                  <div class="posiSon">
                    <div class="tp">{{ item.ramval }}<span>%</span></div>
                    <div class="dn">内存</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>


<script>
import headerCommon from '../components/header/Index'
import * as echarts from 'echarts';
import roundCommon from '../components/round/Index3'
import chinaMap from '@/assets/json/chinaMap.json'
import geoCoordMap from '@/assets/json/geoCoordMap.json'

import 'echarts-gl';
import chinaJson from '@/assets/json/chinaMap2d.json'
import province from '@/assets/json/province.json'

export default {
  components: {
    headerCommon,
    roundCommon
  },
  data() {
    return {
      myChartec1: null,
      myChartec2: null,
      myChartec3: null,
      myChartec4: null,
      myChartec5: null,
      myChartec6: null,
      pointList: [],
      num1: 45.06,
      num2: 42.02,
      num3: 42.32,
      numRandomTimer: null,
      title: 'GPU平均利用率',
      indexEc6: 1,
      numRandomTimer2: null,
      ec6List: [1, 2, 3],
      // 通算 天翼云 阿里云 华为云 AWS 腾讯云 微软云
      textArray1: [
        ['上海32（自研）', '辽阳1（自研）', '中卫5（自研）', '乌鲁木齐27（自研）', '上海36（自研）', '华北2（自研）', '西南1（自研）', '南昌5（自研）', '杭州2（自研）', '南京4（自研）', '成都4（自研）', '芜湖2（自研）', '昆明2（自研）', '合肥2（自研）', '福州3（自研）', '晋中（自研）', '南京5（自研）', '内蒙6（自研）', '南京3（自研）', '海口2（自研）', '武汉4（自研）', '佛山3（自研）', '南宁2（自研）', '郴州（自研）', '九江（自研）', '长沙3（自研）', '广州6（自研）', '重庆2（自研）', '上海7（自研）', '北京5（自研）', '中卫2（自研）', '贵州1（合营）', '内蒙3（合营）', '上海4（合营）', '福州4（自研）', '西安5（自研）', '兰州2（自研）', '南京2（自研）', '太原2'],
        ['杭州-德清-A', '杭州-富阳-A', '杭州-江干-B', '杭州-临安-A', '杭州-萧山-A', '杭州-萧山-B', '杭州-萧山-D', '杭州-余杭-A', '杭州-余杭-B', '杭州-余杭-C', '杭州-余杭-D', '杭州-余杭-E', '北京-昌平-A', '北京-朝阳-B', '北京-朝阳-C', '北京-大兴-A', '北京-大兴-B', '北京-大兴-C', '北京-大兴-E', '北京-丰台-A', '北京-海淀-A', '北京-廊坊-A', '北京-廊坊-C', '北京-顺义-A', '北京-顺义-B', '北京-亦庄-A', '上海-宝山-A', '上海-宝山-B', '上海-宝山-C', '上海-宝山-D', '上海-奉贤-A', '上海-嘉定-A', '上海-浦东-A', '上海-浦东-B', '上海-浦东-C', '上海-浦东-D', '上海-浦东-E', '常熟-碧溪-A', '太仓-璜泾-A', '深圳-福田-A', '深圳-龙华-A', '深圳-龙岗-A', '深圳-南山-A', '深圳-盐田-A', '成都-高新-B', '成都-双流-A', '成都-双流-B', '上海金融云'],
        ['北京-亦庄-中金', '苏州-昆山-万国', '苏州-吴中-国科', '上海-浦东-万国1', '上海-宝山-宝信1', '上海-浦东-万国2', '上海-宝山-宝信2', '广州-黄埔-华新园', '贵阳-贵安-七星湖', '贵阳-贵安-高端园', '广州-番禺'],
        ['中国（宁夏）'],
        ['北京一', '北京二', '北京三', '北京四', '北京五', '北京六', '北京七', '广州一', '广州二', '广州三', '广州四', '广州五', '广州六', '广州七', '重庆一', '重庆二', '上海一', '上海二', '上海三', '上海四', '上海五', '南京一', '南京二', '南京三', '成都一', '成都二'],
        ['上海1', '上海2', '北京1', '北京2'],
      ],
      // 智算 华为 阿里
      textArray2: [
        ['贵阳', '上海', '苏州', '北京', '廊坊', '广州', '贵阳', '香港', '乌兰察布', '圣保罗', '圣地亚哥', '墨西哥城', '约翰内斯堡', '曼谷', '新加坡'],
        ['华北1(青岛)', '华北5(呼和浩特)', '华东2(上海)', '华南1(深圳)', '西南1 (成都)', '华北2(北京)', '华北6(乌兰察布)', '华东5(南京)', '华南2(河源)', '中国(香港)', '华北3(张家口)', '华东1(杭州)', '华东6(福州)', '华南3(广州)', '日本(东京)', '澳大利亚(悉尼)', '印度尼西亚(雅加达)', '韩国 (首尔)', '马来西亚(吉隆坡)', '印度 (孟买)', '新加坡', '泰国 (曼谷)', '美国(弗吉尼亚)', '美国(硅谷)', '德国(法兰克福)']
      ],
      // 超算 天翼 阿里
      textArray3: [
        ['石家庄20(自研)', '西安5（自研）', '贵州1（合营）', '上海4（合营）'],
        ['华北5（呼和浩特）']
      ],
      showSecond: false,
      myChartnewec1: null,
      myChartnewec2: null,
      myChartnewec3: null,
      ecShowIndex: null,
      list1: [],
      list2: [],
      list3: [],
      list: [],
      allIndex: null,
      rightLenged: 1,
      chartsMap2: null,
      mapList1_1: [],
      mapList1_2: [],
      mapList1_3: [],
      rightTopShow: false,
      rightDownShow: true,
      provincePosiList: province,
      typeVal: '',
      posiname: '',
      typeYs: '',
      rightThreeIndex: '',
      myChartEcRightBottom1: null,
      myChartEcRightBottom2: null,
      listEts: '上海32（自研）、南京3（自研）、杭州2（自研）、广州6（自研）、福州3（自研）、上海-浦东-万国1、广州-黄埔-华新园、北京-亦庄-中金、杭州-德清-A、北京-大兴-C',
      listEzs: '北京、香港、华东6(福州)、 华南3(广州)、临港1（上海）',
      listEcs: '上海4（合营）',
      listE: [],
      listWts: '中卫2（自研）、贵州1（合营）、内蒙3（合营）、西安5（自研）、兰州2（自研）、重庆2（自研）',
      listWzs: '廊坊、贵阳、乌兰察布、西南1 (成都)',
      listWcs: '华北5（呼和浩特）',
      listW: [],
      listType: '',
      listFilter: '',
      typeValSon: '',
      rounditemIndex: null,
      chartsline1: null,
      chartsline2: null,
      chartsline3: null,
      formattedDates: [],
      rnum1: 0,
      rnum2: 0,
      rnum3: 0,
      rnum4: 0,
      rnum5: 0,
      rnum6: 0,
      randomNumFunTimer: null
    };
  },
  filters: {

  },
  mounted() {
    // this.provincePosiList.forEach(ele => {
    //   ele.tslist.forEach(ele2 => {
    //     ele.iconts += ele2.num
    //   })
    //   ele.zslist.forEach(ele2 => {
    //     ele.iconzs += ele2.num
    //   })
    //   ele.cslist.forEach(ele2 => {
    //     ele.iconcs += ele2.num
    //   })
    // })
    // this.provincePosiList.forEach(ele => {
    //   ele.tslist.forEach((ele2, index2) => {
    //     if (index2 == 0) {
    //       ele.tyyNumTs = ele2.num
    //     } else if (index2 == 1) {
    //       ele.alyNumTs = ele2.num
    //     } else if (index2 == 2) {
    //       ele.hwyNumTs = ele2.num
    //     } else if (index2 == 3) {
    //       ele.txyNumTs = ele2.num
    //     } else if (index2 == 4) {
    //       ele.wryNumTs = ele2.num
    //     } else if (index2 == 5) {
    //       ele.awsNumTs = ele2.num
    //     } else if (index2 == 6) {
    //       ele.lggcNumTs = ele2.num
    //     } else if (index2 == 7) {
    //       ele.yqslNumTs = ele2.num
    //     } else if (index2 == 8) {
    //       ele.shggslNumTs = ele2.num
    //     }
    //   })
    //   ele.zslist.forEach((ele2, index2) => {
    //     if (index2 == 0) {
    //       ele.tyyNumZs = ele2.num
    //     } else if (index2 == 1) {
    //       ele.alyNumZs = ele2.num
    //     } else if (index2 == 2) {
    //       ele.hwyNumZs = ele2.num
    //     } else if (index2 == 3) {
    //       ele.txyNumZs = ele2.num
    //     } else if (index2 == 4) {
    //       ele.wryNumZs = ele2.num
    //     } else if (index2 == 5) {
    //       ele.awsNumZs = ele2.num
    //     } else if (index2 == 6) {
    //       ele.lggcNumZs = ele2.num
    //     } else if (index2 == 7) {
    //       ele.yqslNumZs = ele2.num
    //     } else if (index2 == 8) {
    //       ele.shggslNumZs = ele2.num
    //     }
    //   })
    //   ele.cslist.forEach((ele2, index2) => {
    //     if (index2 == 0) {
    //       ele.tyyNumCs = ele2.num
    //     } else if (index2 == 1) {
    //       ele.alyNumCs = ele2.num
    //     } else if (index2 == 2) {
    //       ele.hwyNumCs = ele2.num
    //     } else if (index2 == 3) {
    //       ele.txyNumCs = ele2.num
    //     } else if (index2 == 4) {
    //       ele.wryNumCs = ele2.num
    //     } else if (index2 == 5) {
    //       ele.awsNumCs = ele2.num
    //     } else if (index2 == 6) {
    //       ele.lggcNumCs = ele2.num
    //     } else if (index2 == 7) {
    //       ele.yqslNumCs = ele2.num
    //     } else if (index2 == 8) {
    //       ele.shggslNumCs = ele2.num
    //     }
    //   })
    // })
    // this.newEc1Fun()
    // this.newEc3Fun()
    // this.ec1Fun()
    // this.ec2Fun()
    // this.ec3Fun()
    // this.ec4Fun()
    // this.ec5Fun()
    // this.ec6Fun()
    // this.newec2Fun()
    let setClear1 = setTimeout(() => {
      this.numberRandom()
    }, 200);
    this.GLOBAL.timerArrayOut.push(setClear1)
    // this.initMap()
    this.initMap2()
    // this.initEcRightBottom1()
    // this.initEcRightBottom2()
    this.init1()
    this.init2()
    this.init3()
    this.randomNumFun()
  },
  created() {
    echarts.registerMap('china', { geoJSON: chinaMap })
    let arr = []
    for (let i = 0; i < 200; i++) {
      arr.push(i)
    }
    this.pointList = arr
    this.initListVal()
    // 获取当前日期
    var currentDate = new Date();

    // 获取前七天的日期
    var previousDates = [];
    for (var i = 6; i >= 0; i--) {
      var previousDate = new Date(currentDate);
      previousDate.setDate(currentDate.getDate() - i);
      previousDates.push(previousDate);
    }

    // 格式化日期
    var formattedDates = previousDates.map(function (date) {
      var year = date.getFullYear();
      var month = (date.getMonth() + 1).toString().padStart(2, '0');
      var day = date.getDate().toString().padStart(2, '0');
      return month + '.' + day;
    });
    this.formattedDates = formattedDates
  },
  methods: {
    goWork() {
      this.$router.push({
        path: '/page4'
      })
    },
    randomNumFun() {
      this.rnum1 = Math.floor(Math.random() * (600 - 550) + 550)
      this.rnum3 = Math.floor(Math.random() * (550 - 500) + 500)
      this.rnum5 = Math.floor(Math.random() * (450 - 400) + 400)
      this.rnum2 = Math.floor(Math.random() * (200 - 150) + 150)
      this.rnum4 = Math.floor(Math.random() * (150 - 100) + 100)
      this.rnum6 = Math.floor(Math.random() * (100 - 50) + 50)
      this.randomNumFunTimer = setInterval(() => {
        this.rnum1 = Math.floor(Math.random() * (600 - 550) + 550)
        this.rnum3 = Math.floor(Math.random() * (550 - 500) + 500)
        this.rnum5 = Math.floor(Math.random() * (450 - 400) + 400)
        this.rnum2 = Math.floor(Math.random() * (200 - 150) + 150)
        this.rnum4 = Math.floor(Math.random() * (150 - 100) + 100)
        this.rnum6 = Math.floor(Math.random() * (100 - 50) + 50)
      }, 2000);
      this.GLOBAL.timerArraySet.push(this.randomNumFunTimer)
    },
    init1(isData) {
      let option = {
        title: {
          text: '',
        },
        grid: {
          left: '20',
          right: '20',
          top: '20',
          bottom: '20',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.formattedDates,
          axisLabel: {
            color: 'rgba(255,255,255,0.8)'
          },
          axisLine: {
            lineStyle: {
              width: 0.5,
            }
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 2,
          min: 0,
          max: 500,
          axisLabel: {
            show: false,
            color: 'rgba(255,255,255,0.8)'
          },
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [
          {
            data: [350, 290, 420, 270, 390, 300, 400],
            type: 'line',
            areaStyle: {
              normal: {
                // 渐变填充色（线条下半部分）
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(50, 94, 252,0.3)" },
                  { offset: 1, color: "rgba(50, 94, 252,0.05)" }
                ])
              }
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: "rgba(50, 94, 252,0.3)"
                }
              }
            },
            symbol: "none",
            smooth: true,
          },
          {
            data: [150, 190, 220, 150, 90, 30, 200],
            type: 'line',
            areaStyle: {
              normal: {
                // 渐变填充色（线条下半部分）
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(55, 187, 120,0.3)" },
                  { offset: 1, color: "rgba(55, 187, 120,0.05)" }
                ])
              }
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: "rgba(50, 94, 252,0.3)"
                }
              }
            },
            symbol: "none",
            smooth: true,
          }
        ]
      }
      // 内存泄漏 无dom 不执行
      if (!this.chartsline1) {
        this.chartsline1 = echarts.init(this.$refs.dec1, null, { width: this.GLOBAL.relPx(590), height: this.GLOBAL.relPx(100) });
      }
      this.chartsline1.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsline1.setOption(option);
    },
    init2(isData) {
      let option = {
        title: {
          text: '',
        },
        grid: {
          left: '20',
          right: '20',
          top: '20',
          bottom: '20',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.formattedDates,
          axisLabel: {
            color: 'rgba(255,255,255,0.8)'
          },
          axisLine: {
            lineStyle: {
              width: 0.5,
            }
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 2,
          min: 0,
          max: 500,
          axisLabel: {
            show: false,
            color: 'rgba(255,255,255,0.8)'
          },
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [
          {
            data: [220, 150, 310, 140, 240, 200, 300],
            type: 'line',
            areaStyle: {
              normal: {
                // 渐变填充色（线条下半部分）
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(50, 94, 252,0.3)" },
                  { offset: 1, color: "rgba(50, 94, 252,0.05)" }
                ])
              }
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: "rgba(50, 94, 252,0.3)"
                }
              }
            },
            symbol: "none",
            smooth: true,
          },
          {
            data: [90, 120, 150, 100, 80, 70, 150],
            type: 'line',
            areaStyle: {
              normal: {
                // 渐变填充色（线条下半部分）
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(55, 187, 120,0.3)" },
                  { offset: 1, color: "rgba(55, 187, 120,0.05)" }
                ])
              }
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: "rgba(50, 94, 252,0.3)"
                }
              }
            },
            symbol: "none",
            smooth: true,
          }
        ]
      }
      // 内存泄漏 无dom 不执行
      if (!this.chartsline2) {
        this.chartsline2 = echarts.init(this.$refs.dec2, null, { width: this.GLOBAL.relPx(590), height: this.GLOBAL.relPx(100) });
      }
      this.chartsline2.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsline2.setOption(option);
    },
    init3(isData) {
      let option = {
        title: {
          text: '',
        },
        grid: {
          left: '20',
          right: '20',
          top: '20',
          bottom: '20',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.formattedDates,
          axisLabel: {
            color: 'rgba(255,255,255,0.8)'
          },
          axisLine: {
            lineStyle: {
              width: 0.5,
            }
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 2,
          min: 0,
          max: 500,
          axisLabel: {
            show: false,
            color: 'rgba(255,255,255,0.8)'
          },
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [
          {
            data: [300, 390, 320, 470, 290, 240, 320],
            type: 'line',
            areaStyle: {
              normal: {
                // 渐变填充色（线条下半部分）
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(50, 94, 252,0.3)" },
                  { offset: 1, color: "rgba(50, 94, 252,0.05)" }
                ])
              }
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: "rgba(50, 94, 252,0.3)"
                }
              }
            },
            symbol: "none",
            smooth: true,
          },
          {
            data: [60, 150, 120, 180, 90, 120, 150],
            type: 'line',
            areaStyle: {
              normal: {
                // 渐变填充色（线条下半部分）
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(55, 187, 120,0.3)" },
                  { offset: 1, color: "rgba(55, 187, 120,0.05)" }
                ])
              }
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: "rgba(50, 94, 252,0.3)"
                }
              }
            },
            symbol: "none",
            smooth: true,
          }
        ]
      }
      // 内存泄漏 无dom 不执行
      if (!this.chartsline3) {
        this.chartsline3 = echarts.init(this.$refs.dec3, null, { width: this.GLOBAL.relPx(590), height: this.GLOBAL.relPx(100) });
      }
      this.chartsline3.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsline3.setOption(option);
    },
    rounditemIndexFun(index) {
      if (this.rounditemIndex == index) {
        this.rounditemIndex = null
      } else {
        this.rounditemIndex = index
      }
    },
    typeYsClick(val) {
      if (this.typeYs == val) {
        this.typeYs = ''
      } else {
        this.typeYs = val
      }
    },
    initEcRightBottom1() {
      let _this = this
      var legends = ['天翼云', '华为云', '阿里云', '临港'];
      var name = ['通算', '智算', '超算'];
      var data1 = [5, { value: 0, itemStyle: { opacity: 0 } }, 1];
      var data2 = [3, 2, { value: 0, itemStyle: { opacity: 0 } }];
      var data3 = [2, 2, { value: 0, itemStyle: { opacity: 0 } }];
      var data4 = [{ value: 0, itemStyle: { opacity: 0 } }, 1, { value: 0, itemStyle: { opacity: 0 } }];
      var color = [{
        type: "linear",
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [{
          offset: 0,
          color: "#5d71c8",
        },
        {
          offset: 0.5,
          color: "#5d71c8",
        },
        {
          offset: 0.5,
          color: "#364caf",
        },
        {
          offset: 1,
          color: "#364caf",
        },
        ],
      }, {
        type: "linear",
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [{
          offset: 0,
          color: "#96cb72",
        },
        {
          offset: 0.5,
          color: "#96cb72",
        },
        {
          offset: 0.5,
          color: "#6faa47",
        },
        {
          offset: 1,
          color: "#6faa47",
        },
        ],
      }, {
        type: "linear",
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [{
          offset: 0,
          color: "#f3c851",
        },
        {
          offset: 0.5,
          color: "#f3c851",
        },
        {
          offset: 0.5,
          color: "#daad30",
        },
        {
          offset: 1,
          color: "#daad30",
        },
        ],
      }, {
        type: "linear",
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [{
          offset: 0,
          color: "#80c0df",
        },
        {
          offset: 0.5,
          color: "#80c0df",
        },
        {
          offset: 0.5,
          color: "#59a0c3",
        },
        {
          offset: 1,
          color: "#59a0c3",
        },
        ],
      }];
      var barWidth = 36;
      var constData2 = [];
      var constData3 = [];
      var constData4 = [];
      var showData = [];
      for (var i = 0; i < data1.length; i++) {
        constData2[i] = data1[i] + data2[i];
        constData3[i] = data1[i] + data2[i] + data3[i];
        constData4[i] = data1[i] + data2[i] + data3[i] + data4[i];
        if (data1[i] <= 0) {
          showData.push({
            value: 0.1,
            itemStyle: {
              normal: {
                borderColor: "#f67c20",
                borderWidth: 2,
                color: "#f67c20",
              },
            },
          });
        } else {
          if (data2[i] > 0) {
            showData.push({
              value: data1[i],
              itemStyle: {
                normal: {
                  borderColor: "#495ebd",
                  borderWidth: 2,
                  color: "#495ebd",
                },
              },
            });
          } else {
            showData.push({
              value: data1[i],
              itemStyle: {
                normal: {
                  borderColor: "#fde008",
                  borderWidth: 2,
                  color: "#fde008",
                },
              },
            });
          }
        }
      }
      var option = {
        // tooltip: {
        //   trigger: "axis"
        // },
        legend: {
          data: legends,
          selectedMode: false,
          show: false,
          right: 0,
          top: 0,
          textStyle: {
            color: '#ffffff'
          }
        },
        xAxis: {
          data: name,
          axisTick: {
            show: false
          },
          axisLine: {
            show: true
          },
          axisLabel: {
            color: '#ffffff'
          },
        },
        yAxis: {
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false,
            color: "#a4a4a4"
          }
        },
        grid: { top: '50', left: '0', right: '0', bottom: '30' },
        series: [{
          z: 1,
          name: legends[0],
          type: "bar",
          barWidth: barWidth,
          stack: "总量",
          color: color[0],
          label: {
            show: true,
            position: 'inside',
            color: '#ffffff',
            fontSize: 14,
          },
          formatter: function (params) {
            if (params.value > 0) {
              return params.value;
            } else {
              return '';
            }
          },
          data: data1,
        },
        {
          z: 2,
          name: legends[1],
          type: "bar",
          barWidth: barWidth,
          stack: "总量",
          color: color[1],
          label: {
            show: true,
            position: 'inside',
            color: '#ffffff',
            fontSize: 14,
          },
          formatter: function (params) {
            if (params.value > 0) {
              return params.value;
            } else {
              return '';
            }
          },
          data: data2
        },
        {
          z: 3,
          name: legends[2],
          type: "bar",
          barWidth: barWidth,
          stack: "总量",
          color: color[2],
          label: {
            show: true,
            position: 'inside',
            color: '#ffffff',
            fontSize: 14,
          },
          formatter: function (params) {
            if (params.value > 0) {
              return params.value;
            } else {
              return '';
            }
          },
          data: data3
        },
        {
          z: 4,
          name: legends[3],
          type: "bar",
          barWidth: barWidth,
          stack: "总量",
          color: color[3],
          label: {
            show: true,
            position: 'inside',
            color: '#ffffff',
            fontSize: 14,
          },
          formatter: function (params) {
            if (params.value > 0) {
              return params.value;
            } else {
              return '';
            }
          },
          data: data4
        },
        {
          z: 5,
          type: "pictorialBar",
          data: data1,
          symbol: "diamond",
          symbolOffset: ["0%", "50%"],
          symbolSize: [barWidth, 10],
          itemStyle: {
            normal: {
              color: color[0],
            },
          },
          tooltip: {
            show: false,
          },
        },
        {
          z: 6,
          type: "pictorialBar",
          data: constData2,
          symbolPosition: "end",
          symbol: "diamond",
          symbolOffset: ["0%", "-50%"],
          symbolSize: [barWidth, 10],
          itemStyle: {
            normal: {
              color: color[1],
            },
          },
          tooltip: {
            show: false,
          },
        },
        {
          z: 7,
          type: "pictorialBar",
          data: constData3,
          symbolPosition: "end",
          symbol: "diamond",
          symbolOffset: ["0%", "-50%"],
          symbolSize: [barWidth, 10],
          itemStyle: {
            normal: {
              color: color[2],
            },
          },
          tooltip: {
            show: false,
          },
        },
        {
          z: 8,
          type: "pictorialBar",
          data: constData4,
          symbol: "diamond",
          symbolPosition: "end",
          symbolOffset: ["0%", "-50%"],
          symbolSize: [barWidth, 10],
          itemStyle: {
            normal: {
              color: color[3],
            },
          },
          tooltip: {
            show: false,
          },
        },
        {
          z: 9,
          type: "pictorialBar",
          symbolPosition: "end",
          data: showData,
          symbol: "diamond",
          symbolOffset: ["0%", "-50%"],
          symbolSize: [barWidth - 4, (10 * (barWidth - 4)) / barWidth],
          tooltip: {
            show: false,
          },
        }
        ],
      };
      if (!this.myChartEcRightBottom1) {
        this.myChartEcRightBottom1 = echarts.init(this.$refs.ecRightBottom1, null, { width: this.GLOBAL.relPx(400), height: this.GLOBAL.relPx(300) });
        this.GLOBAL.echartsDomArray.push(this.myChartEcRightBottom1)
      }
      this.myChartEcRightBottom1.clear()
      this.myChartEcRightBottom1.off('click')
      // 使用刚指定的配置项和数据显示图表。
      this.myChartEcRightBottom1.setOption(option);
      this.myChartEcRightBottom1.on('click', function (param) {
        //param.name x轴值,param.data y轴值
        _this.openDialogRound(param.name === '通算' ? 'ts' : param.name === '智算' ? 'zs' : 'cs', 'E')
      });
    },
    initEcRightBottom2() {
      let _this = this
      var legends = ['天翼云', '华为云', '阿里云', '临港'];
      var name = ['通算', '智算', '超算'];
      var data1 = [6, { value: 0, itemStyle: { opacity: 0 } }, { value: 0, itemStyle: { opacity: 0 } }];
      var data2 = [{ value: 0, itemStyle: { opacity: 0 } }, 3, { value: 0, itemStyle: { opacity: 0 } }];
      var data3 = [{ value: 0, itemStyle: { opacity: 0 } }, 3, 3];
      var data4 = [{ value: 0, itemStyle: { opacity: 0 } }, { value: 0, itemStyle: { opacity: 0 } }, { value: 0, itemStyle: { opacity: 0 } }];
      var color = [{
        type: "linear",
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [{
          offset: 0,
          color: "#5d71c8",
        },
        {
          offset: 0.5,
          color: "#5d71c8",
        },
        {
          offset: 0.5,
          color: "#364caf",
        },
        {
          offset: 1,
          color: "#364caf",
        },
        ],
      }, {
        type: "linear",
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [{
          offset: 0,
          color: "#96cb72",
        },
        {
          offset: 0.5,
          color: "#96cb72",
        },
        {
          offset: 0.5,
          color: "#6faa47",
        },
        {
          offset: 1,
          color: "#6faa47",
        },
        ],
      }, {
        type: "linear",
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [{
          offset: 0,
          color: "#f3c851",
        },
        {
          offset: 0.5,
          color: "#f3c851",
        },
        {
          offset: 0.5,
          color: "#daad30",
        },
        {
          offset: 1,
          color: "#daad30",
        },
        ],
      }, {
        type: "linear",
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [{
          offset: 0,
          color: "#80c0df",
        },
        {
          offset: 0.5,
          color: "#80c0df",
        },
        {
          offset: 0.5,
          color: "#59a0c3",
        },
        {
          offset: 1,
          color: "#59a0c3",
        },
        ],
      }];
      var barWidth = 36;
      var constData2 = [];
      var constData3 = [];
      var constData4 = [];
      var showData = [];
      for (var i = 0; i < data1.length; i++) {
        constData2[i] = data1[i] + data2[i];
        constData3[i] = data1[i] + data2[i] + data3[i];
        constData4[i] = data1[i] + data2[i] + data3[i] + data4[i];
        if (data1[i] <= 0) {
          showData.push({
            value: 0.1,
            itemStyle: {
              normal: {
                borderColor: "#f67c20",
                borderWidth: 2,
                color: "#f67c20",
              },
            },
          });
        } else {
          if (data2[i] > 0) {
            showData.push({
              value: data1[i],
              itemStyle: {
                normal: {
                  borderColor: "#495ebd",
                  borderWidth: 2,
                  color: "#495ebd",
                },
              },
            });
          } else {
            showData.push({
              value: data1[i],
              itemStyle: {
                normal: {
                  borderColor: "#fde008",
                  borderWidth: 2,
                  color: "#fde008",
                },
              },
            });
          }
        }
      }
      var option = {
        // tooltip: {
        //   trigger: "axis"
        // },
        legend: {
          data: legends,
          selectedMode: false,
          show: true,
          right: 0,
          top: 0,
          textStyle: {
            color: '#ffffff'
          }
        },
        xAxis: {
          data: name,
          axisTick: {
            show: false
          },
          axisLine: {
            show: true
          },
          axisLabel: {
            color: '#ffffff'
          },
        },
        yAxis: {
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false,
            color: "#a4a4a4"
          }
        },
        grid: { top: '50', left: '0', right: '0', bottom: '30' },
        series: [{
          z: 1,
          name: legends[0],
          type: "bar",
          barWidth: barWidth,
          stack: "总量",
          color: color[0],
          label: {
            show: true,
            position: 'inside',
            color: '#ffffff',
            fontSize: 14,
          },
          formatter: function (params) {
            if (params.value > 0) {
              return params.value;
            } else {
              return '';
            }
          },
          data: data1,
        },
        {
          z: 2,
          name: legends[1],
          type: "bar",
          barWidth: barWidth,
          stack: "总量",
          color: color[1],
          label: {
            show: true,
            position: 'inside',
            color: '#ffffff',
            fontSize: 14,
          },
          formatter: function (params) {
            if (params.value > 0) {
              return params.value;
            } else {
              return '';
            }
          },
          data: data2
        },
        {
          z: 3,
          name: legends[2],
          type: "bar",
          barWidth: barWidth,
          stack: "总量",
          color: color[2],
          label: {
            show: true,
            position: 'inside',
            color: '#ffffff',
            fontSize: 14,
          },
          formatter: function (params) {
            if (params.value > 0) {
              return params.value;
            } else {
              return '';
            }
          },
          data: data3
        },
        {
          z: 4,
          name: legends[3],
          type: "bar",
          barWidth: barWidth,
          stack: "总量",
          color: color[3],
          label: {
            show: true,
            position: 'inside',
            color: '#ffffff',
            fontSize: 14,
          },
          formatter: function (params) {
            if (params.value > 0) {
              return params.value;
            } else {
              return '';
            }
          },
          data: data4
        },
        {
          z: 5,
          type: "pictorialBar",
          data: data1,
          symbol: "diamond",
          symbolOffset: ["0%", "50%"],
          symbolSize: [barWidth, 10],
          itemStyle: {
            normal: {
              color: color[0],
            },
          },
          tooltip: {
            show: false,
          },
        },
        {
          z: 6,
          type: "pictorialBar",
          data: constData2,
          symbolPosition: "end",
          symbol: "diamond",
          symbolOffset: ["0%", "-50%"],
          symbolSize: [barWidth, 10],
          itemStyle: {
            normal: {
              color: color[1],
            },
          },
          tooltip: {
            show: false,
          },
        },
        {
          z: 7,
          type: "pictorialBar",
          data: constData3,
          symbolPosition: "end",
          symbol: "diamond",
          symbolOffset: ["0%", "-50%"],
          symbolSize: [barWidth, 10],
          itemStyle: {
            normal: {
              color: color[2],
            },
          },
          tooltip: {
            show: false,
          },
        },
        {
          z: 8,
          type: "pictorialBar",
          data: constData4,
          symbol: "diamond",
          symbolPosition: "end",
          symbolOffset: ["0%", "-50%"],
          symbolSize: [barWidth, 10],
          itemStyle: {
            normal: {
              color: color[3],
            },
          },
          tooltip: {
            show: false,
          },
        },
        {
          z: 9,
          type: "pictorialBar",
          symbolPosition: "end",
          data: showData,
          symbol: "diamond",
          symbolOffset: ["0%", "-50%"],
          symbolSize: [barWidth - 4, (10 * (barWidth - 4)) / barWidth],
          tooltip: {
            show: false,
          },
        }
        ],
      };
      if (!this.myChartEcRightBottom2) {
        this.myChartEcRightBottom2 = echarts.init(this.$refs.ecRightBottom2, null, { width: this.GLOBAL.relPx(400), height: this.GLOBAL.relPx(300) });
        this.GLOBAL.echartsDomArray.push(this.myChartEcRightBottom2)
      }
      this.myChartEcRightBottom2.off('click')
      this.myChartEcRightBottom2.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.myChartEcRightBottom2.setOption(option);
      this.myChartEcRightBottom2.on('click', function (param) {
        //param.name x轴值,param.data y轴值
        _this.openDialogRound(param.name === '通算' ? 'ts' : param.name === '智算' ? 'zs' : 'cs', 'W')
      });
    },
    openDialogRound(type1, type2) {
      this.rightTopShow = true
      this.listFilter = type1
      this.listType = type2
    },
    rightThreeFun(index) {
      if (this.rightThreeIndex == index) {
        this.rightThreeIndex = ''
      } else {
        this.rightThreeIndex = index
      }
    },
    tableClickMap(type) {
      if (this.typeVal == type) {
        this.typeVal = ''
      } else {
        this.typeVal = type
      }
    },
    posiClickMap(name, type) {
      if (this.posiname == name && this.typeValSon == type) {
        this.typeValSon = ''
        this.posiname = ''
      } else {
        this.typeValSon = type
        this.posiname = name
      }
    },
    initListVal() {
      let list = []
      let mapList1 = []
      let mapList2 = []
      let mapList3 = []
      let mapList1_1 = []
      let mapList1_2 = []
      let mapList1_3 = []

      let listEts = this.listEts.split('、')
      listEts.forEach((ele, index) => {
        this.listE.push({
          name: ele, type1: 'ts', type2: 'E', cpuval: Math.floor(Math.random() * (30 - 15 + 1)) + 15
          , gpuval: Math.floor(Math.random() * (20 - 10 + 1)) + 10, ramval: Math.floor(Math.random() * (25 - 15 + 1)) + 15, type: 'CPU', index: 1
        })
      })
      let listEzs = this.listEzs.split('、')
      listEzs.forEach((ele, index) => {
        this.listE.push({
          name: ele, type1: 'zs', type2: 'E', cpuval: Math.floor(Math.random() * (30 - 15 + 1)) + 15
          , gpuval: Math.floor(Math.random() * (20 - 10 + 1)) + 10, ramval: Math.floor(Math.random() * (25 - 15 + 1)) + 15, type: 'CPU', index: 1
        })
      })
      let listEcs = this.listEcs.split('、')
      listEcs.forEach((ele, index) => {
        this.listE.push({
          name: ele, type1: 'cs', type2: 'E', cpuval: Math.floor(Math.random() * (30 - 15 + 1)) + 15
          , gpuval: Math.floor(Math.random() * (20 - 10 + 1)) + 10, ramval: Math.floor(Math.random() * (25 - 15 + 1)) + 15, type: 'CPU', index: 1
        })
      })

      let listWts = this.listWts.split('、')
      listWts.forEach((ele, index) => {
        this.listW.push({
          name: ele, type1: 'ts', type2: 'W', cpuval: Math.floor(Math.random() * (30 - 15 + 1)) + 15
          , gpuval: Math.floor(Math.random() * (20 - 10 + 1)) + 10, ramval: Math.floor(Math.random() * (25 - 15 + 1)) + 15, type: 'CPU', index: 1
        })
      })
      let listWzs = this.listWzs.split('、')
      listWzs.forEach((ele, index) => {
        this.listW.push({
          name: ele, type1: 'zs', type2: 'W', cpuval: Math.floor(Math.random() * (30 - 15 + 1)) + 15
          , gpuval: Math.floor(Math.random() * (20 - 10 + 1)) + 10, ramval: Math.floor(Math.random() * (25 - 15 + 1)) + 15, type: 'CPU', index: 1
        })
      })
      let listWcs = this.listWcs.split('、')
      listWcs.forEach((ele, index) => {
        this.listW.push({
          name: ele, type1: 'cs', type2: 'W', cpuval: Math.floor(Math.random() * (30 - 15 + 1)) + 15
          , gpuval: Math.floor(Math.random() * (20 - 10 + 1)) + 10, ramval: Math.floor(Math.random() * (25 - 15 + 1)) + 15, type: 'CPU', index: 1
        })
      })

      this.textArray1.forEach((ele, index) => {
        ele.forEach(ele2 => {
          let str = ele2.slice(0, 2)
          mapList1.push(str)
          list.push({
            name: ele2, cpuval: Math.floor(Math.random() * (30 - 15 + 1)) + 15
            , gpuval: Math.floor(Math.random() * (20 - 10 + 1)) + 10, ramval: Math.floor(Math.random() * (25 - 15 + 1)) + 15, type: 'CPU', index: 1
          })
        })
      })
      mapList1.forEach(str => {
        let jwdarr = []
        if (str.indexOf('上海') > -1) {
          jwdarr = [121.48054, 31.23593]
        } else if (str.indexOf('辽阳') > -1) {
          jwdarr = [123.11242, 41.21168]
        } else if (str.indexOf('中卫') > -1) {
          jwdarr = [105.20357, 37.50570]
        } else if (str.indexOf('乌鲁') > -1) {
          jwdarr = [87.41603, 43.47708]
        } else if (str.indexOf('华北') > -1) {
          jwdarr = [124.20384, 43.53417]
        } else if (str.indexOf('西南') > -1) {
          jwdarr = [103.20584, 25.22686]
        } else if (str.indexOf('南昌') > -1) {
          jwdarr = [115.95046, 28.55160]
        } else if (str.indexOf('杭州') > -1) {
          jwdarr = [120.21551, 30.25308]
        } else if (str.indexOf('南京') > -1) {
          jwdarr = [118.80242, 32.06465]
        } else if (str.indexOf('成都') > -1) {
          jwdarr = [104.07274, 30.57899]
        } else if (str.indexOf('芜湖') > -1) {
          jwdarr = [118.43943, 31.35854]
        } else if (str.indexOf('昆明') > -1) {
          jwdarr = [102.83944, 24.88627]
        } else if (str.indexOf('合肥') > -1) {
          jwdarr = [117.23344, 31.82658]
        } else if (str.indexOf('福州') > -1) {
          jwdarr = [119.30347, 26.08043]
        } else if (str.indexOf('晋中') > -1) {
          jwdarr = [112.75959, 37.69284]
        } else if (str.indexOf('内蒙') > -1) {
          jwdarr = [109.85219, 40.82046]
        } else if (str.indexOf('海口') > -1) {
          jwdarr = [110.20672, 20.05211]
        } else if (str.indexOf('武汉') > -1) {
          jwdarr = [114.31159, 30.59847]
        } else if (str.indexOf('佛山') > -1) {
          jwdarr = [113.12851, 23.02776]
        } else if (str.indexOf('南宁') > -1) {
          jwdarr = [108.37345, 22.82261]
        } else if (str.indexOf('郴州') > -1) {
          jwdarr = [113.02146, 25.77668]
        } else if (str.indexOf('九江') > -1) {
          jwdarr = [115.96066, 29.66666]
        } else if (str.indexOf('长沙') > -1) {
          jwdarr = [113.08755, 28.25182]
        } else if (str.indexOf('广州') > -1) {
          jwdarr = [113.27143, 23.13534]
        } else if (str.indexOf('重庆') > -1) {
          jwdarr = [106.55844, 29.56900]
        } else if (str.indexOf('北京') > -1) {
          jwdarr = [116.41339, 39.91092]
        } else if (str.indexOf('贵州') > -1) {
          jwdarr = [106.71447, 26.60403]
        } else if (str.indexOf('西安') > -1) {
          jwdarr = [108.94647, 34.34727]
        } else if (str.indexOf('兰州') > -1) {
          jwdarr = [103.84052, 36.06724]
        } else if (str.indexOf('太原') > -1) {
          jwdarr = [112.55640, 37.87699]
        } else if (str.indexOf('常熟') > -1) {
          jwdarr = [120.75950, 31.65954]
        } else if (str.indexOf('太仓') > -1) {
          jwdarr = [121.13560, 31.46460]
        } else if (str.indexOf('深圳') > -1) {
          jwdarr = [114.06455, 22.54846]
        } else if (str.indexOf('苏州') > -1) {
          jwdarr = [120.59241, 31.30356]
        } else if (str.indexOf('贵阳') > -1) {
          jwdarr = [106.63658, 26.65332]
        } else if (str.indexOf('中国') > -1 || str.indexOf('香港') > -1) {
          jwdarr = [106.26561, 38.47688]
        } else if (str.indexOf('廊坊') > -1) {
          jwdarr = [106.26561, 38.47688]
        }
        mapList1_1.push({ name: str, value: jwdarr, warning: 5 })
      })
      this.mapList1_1 = mapList1_1

      this.textArray2.forEach((ele, index) => {
        ele.forEach(ele2 => {
          list.push({
            name: ele2, cpuval: Math.floor(Math.random() * (30 - 15 + 1)) + 15
            , gpuval: Math.floor(Math.random() * (20 - 10 + 1)) + 10, ramval: Math.floor(Math.random() * (25 - 15 + 1)) + 15, type: 'CPU', index: 2
          })
          let str = ele2.slice(0, 2)
          mapList2.push(str)
        })
      })
      let newList = Array.from(new Set(mapList2))
      mapList2.forEach(str => {
        let jwdarr = []
        if (str.indexOf('上海') > -1) {
          jwdarr = [121.48054, 31.23593]
        } else if (str.indexOf('辽阳') > -1) {
          jwdarr = [123.11242, 41.21168]
        } else if (str.indexOf('中卫') > -1) {
          jwdarr = [105.20357, 37.50570]
        } else if (str.indexOf('乌鲁') > -1) {
          jwdarr = [87.41603, 43.47708]
        } else if (str.indexOf('华北') > -1) {
          jwdarr = [124.20384, 43.53417]
        } else if (str.indexOf('西南') > -1) {
          jwdarr = [103.20584, 25.22686]
        } else if (str.indexOf('南昌') > -1) {
          jwdarr = [115.95046, 28.55160]
        } else if (str.indexOf('杭州') > -1) {
          jwdarr = [120.21551, 30.25308]
        } else if (str.indexOf('南京') > -1) {
          jwdarr = [118.80242, 32.06465]
        } else if (str.indexOf('成都') > -1) {
          jwdarr = [104.07274, 30.57899]
        } else if (str.indexOf('芜湖') > -1) {
          jwdarr = [118.43943, 31.35854]
        } else if (str.indexOf('昆明') > -1) {
          jwdarr = [102.83944, 24.88627]
        } else if (str.indexOf('合肥') > -1) {
          jwdarr = [117.23344, 31.82658]
        } else if (str.indexOf('福州') > -1) {
          jwdarr = [119.30347, 26.08043]
        } else if (str.indexOf('晋中') > -1) {
          jwdarr = [112.75959, 37.69284]
        } else if (str.indexOf('内蒙') > -1) {
          jwdarr = [109.85219, 40.82046]
        } else if (str.indexOf('海口') > -1) {
          jwdarr = [110.20672, 20.05211]
        } else if (str.indexOf('武汉') > -1) {
          jwdarr = [114.31159, 30.59847]
        } else if (str.indexOf('佛山') > -1) {
          jwdarr = [113.12851, 23.02776]
        } else if (str.indexOf('南宁') > -1) {
          jwdarr = [108.37345, 22.82261]
        } else if (str.indexOf('郴州') > -1) {
          jwdarr = [113.02146, 25.77668]
        } else if (str.indexOf('九江') > -1) {
          jwdarr = [115.96066, 29.66666]
        } else if (str.indexOf('长沙') > -1) {
          jwdarr = [113.08755, 28.25182]
        } else if (str.indexOf('广州') > -1) {
          jwdarr = [113.27143, 23.13534]
        } else if (str.indexOf('重庆') > -1) {
          jwdarr = [106.55844, 29.56900]
        } else if (str.indexOf('北京') > -1) {
          jwdarr = [116.41339, 39.91092]
        } else if (str.indexOf('贵州') > -1) {
          jwdarr = [106.71447, 26.60403]
        } else if (str.indexOf('西安') > -1) {
          jwdarr = [108.94647, 34.34727]
        } else if (str.indexOf('兰州') > -1) {
          jwdarr = [103.84052, 36.06724]
        } else if (str.indexOf('太原') > -1) {
          jwdarr = [112.55640, 37.87699]
        } else if (str.indexOf('常熟') > -1) {
          jwdarr = [120.75950, 31.65954]
        } else if (str.indexOf('太仓') > -1) {
          jwdarr = [121.13560, 31.46460]
        } else if (str.indexOf('深圳') > -1) {
          jwdarr = [114.06455, 22.54846]
        } else if (str.indexOf('苏州') > -1) {
          jwdarr = [120.59241, 31.30356]
        } else if (str.indexOf('贵阳') > -1) {
          jwdarr = [106.63658, 26.65332]
        } else if (str.indexOf('中国') > -1) {
          jwdarr = [106.26561, 38.47688]
        }
        mapList1_2.push({ name: str, value: jwdarr, warning: 5 })
      })
      this.mapList1_2 = mapList1_2
      mapList1_2.push({ name: '青岛', value: [120.38946, 36.07223], warning: 5 })
      mapList1_2.push({ name: '延安', value: [109.76696, 36.38744], warning: 5 })
      mapList1_2.push({ name: '四川', value: [100.62736, 30.20038], warning: 5 })
      mapList1_2.push({ name: '浙江', value: [120.98189, 28.96351], warning: 5 })
      list.push(
        {
          name: '苏州智算中心2', cpuval: Math.floor(Math.random() * (30 - 15 + 1)) + 15
          , gpuval: Math.floor(Math.random() * (20 - 10 + 1)) + 10, ramval: Math.floor(Math.random() * (25 - 15 + 1)) + 15, type: 'CPU', index: 2
        },
        {
          name: '苏州智算中心1', cpuval: Math.floor(Math.random() * (30 - 15 + 1)) + 15
          , gpuval: Math.floor(Math.random() * (20 - 10 + 1)) + 10, ramval: Math.floor(Math.random() * (25 - 15 + 1)) + 15, type: 'CPU', index: 2
        },
        {
          name: '临港-国产1', cpuval: Math.floor(Math.random() * (30 - 15 + 1)) + 15
          , gpuval: Math.floor(Math.random() * (20 - 10 + 1)) + 10, ramval: Math.floor(Math.random() * (25 - 15 + 1)) + 15, type: 'CPU', index: 2
        }
      )
      this.textArray3.forEach(ele => {
        ele.forEach(str => {
          list.push({
            name: str, cpuval: Math.floor(Math.random() * (30 - 15 + 1)) + 15
            , gpuval: Math.floor(Math.random() * (20 - 10 + 1)) + 10, ramval: Math.floor(Math.random() * (25 - 15 + 1)) + 15, type: 'CPU', index: 3
          },)
          let jwdarr = []
          if (str.indexOf('上海') > -1) {
            jwdarr = [121.48054, 31.23593]
          } else if (str.indexOf('辽阳') > -1) {
            jwdarr = [123.11242, 41.21168]
          } else if (str.indexOf('中卫') > -1) {
            jwdarr = [105.20357, 37.50570]
          } else if (str.indexOf('乌鲁') > -1) {
            jwdarr = [87.41603, 43.47708]
          } else if (str.indexOf('华北') > -1) {
            jwdarr = [124.20384, 43.53417]
          } else if (str.indexOf('西南') > -1) {
            jwdarr = [103.20584, 25.22686]
          } else if (str.indexOf('南昌') > -1) {
            jwdarr = [115.95046, 28.55160]
          } else if (str.indexOf('杭州') > -1) {
            jwdarr = [120.21551, 30.25308]
          } else if (str.indexOf('南京') > -1) {
            jwdarr = [118.80242, 32.06465]
          } else if (str.indexOf('成都') > -1) {
            jwdarr = [104.07274, 30.57899]
          } else if (str.indexOf('芜湖') > -1) {
            jwdarr = [118.43943, 31.35854]
          } else if (str.indexOf('昆明') > -1) {
            jwdarr = [102.83944, 24.88627]
          } else if (str.indexOf('合肥') > -1) {
            jwdarr = [117.23344, 31.82658]
          } else if (str.indexOf('福州') > -1) {
            jwdarr = [119.30347, 26.08043]
          } else if (str.indexOf('晋中') > -1) {
            jwdarr = [112.75959, 37.69284]
          } else if (str.indexOf('海口') > -1) {
            jwdarr = [110.20672, 20.05211]
          } else if (str.indexOf('武汉') > -1) {
            jwdarr = [114.31159, 30.59847]
          } else if (str.indexOf('佛山') > -1) {
            jwdarr = [113.12851, 23.02776]
          } else if (str.indexOf('南宁') > -1) {
            jwdarr = [108.37345, 22.82261]
          } else if (str.indexOf('郴州') > -1) {
            jwdarr = [113.02146, 25.77668]
          } else if (str.indexOf('九江') > -1) {
            jwdarr = [115.96066, 29.66666]
          } else if (str.indexOf('长沙') > -1) {
            jwdarr = [113.08755, 28.25182]
          } else if (str.indexOf('广州') > -1) {
            jwdarr = [113.27143, 23.13534]
          } else if (str.indexOf('重庆') > -1) {
            jwdarr = [106.55844, 29.56900]
          } else if (str.indexOf('北京') > -1) {
            jwdarr = [116.41339, 39.91092]
          } else if (str.indexOf('贵州') > -1) {
            jwdarr = [106.71447, 26.60403]
          } else if (str.indexOf('西安') > -1) {
            jwdarr = [108.94647, 34.34727]
          } else if (str.indexOf('兰州') > -1) {
            jwdarr = [103.84052, 36.06724]
          } else if (str.indexOf('太原') > -1) {
            jwdarr = [112.55640, 37.87699]
          } else if (str.indexOf('常熟') > -1) {
            jwdarr = [120.75950, 31.65954]
          } else if (str.indexOf('太仓') > -1) {
            jwdarr = [121.13560, 31.46460]
          } else if (str.indexOf('深圳') > -1) {
            jwdarr = [114.06455, 22.54846]
          } else if (str.indexOf('苏州') > -1) {
            jwdarr = [120.59241, 31.30356]
          } else if (str.indexOf('贵阳') > -1) {
            jwdarr = [106.63658, 26.65332]
          } else if (str.indexOf('中国') > -1) {
            jwdarr = [106.26561, 38.47688]
          } else if (str.indexOf('石家庄') > -1) {
            jwdarr = [114.52153, 38.04831]
          } else if (str.indexOf('呼和浩特') > -1) {
            jwdarr = [111.75551, 40.84842]
          }
          this.mapList1_3.push({ name: str, value: jwdarr, warning: 5 })
        })
      })
      this.list = list
    },
    rightLengedFun(index) {
      if (this.rightLenged != index) {
        this.rightLenged = index
        this.newec2Fun()
      }
    },
    newec2Fun() {
      let xdata = [48, 42, 40, 39, 38]
      let ydata = ['天翼云上海32（自研）', '天翼云杭州2（自研）', '天翼云上海4（合营）', '天翼云西南1（自研）', '天翼云合肥2（自研）']
      let colors = new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
        offset: 0,
        color: "rgba(249, 202, 88,0.3)"
      },
      {
        offset: 1,
        color: "rgba(249, 202, 88,0.6)"
      }
      ])
      let colorsCenter = [{//第一节下面
        "offset": 0,
        "color": "rgba(249, 202, 88,0)"
      }, {
        "offset": 1,
        "color": "rgba(249, 202, 88,0.6)"
      }]
      if (this.rightLenged == 2) {
        xdata = [48, 32, 30, 30]
        ydata = ['华为云上海32（自研）', '华为云华北-乌兰察布1', '临港1-国产', '苏州智算中心1']
        colors = new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: "rgba(158, 210, 131,0.3)"
        },
        {
          offset: 1,
          color: "rgba(158, 210, 131,0.6)"
        }
        ])
        colorsCenter = [{//第一节下面
          "offset": 0,
          "color": "rgba(158, 210, 131,0)"
        }, {
          "offset": 1,
          "color": "rgba(158, 210, 131,0.6)"
        }]
      }

      if (this.rightLenged == 3) {
        xdata = [48, 22, 23]
        ydata = ['天翼云石家庄20(自研)', '天翼云西安5（自研）', '天翼云贵州1（合营）']
        colors = new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: "rgba(56, 97, 173,0.3)"
        },
        {
          offset: 1,
          color: "rgba(56, 97, 173,0.6)"
        }
        ])
        colorsCenter = [{//第一节下面
          "offset": 0,
          "color": "rgba(56, 97, 173,0)"
        }, {
          "offset": 1,
          "color": "rgba(56, 97, 173,0.6)"
        }]
      }

      let option = {
        textStyle: {
          fontFamily: 'Microsoft YaHei'
        },
        color: ['#74d1fd', '#009ae4', '#0071c1'],
        // 设置图表的位置
        grid: {
          x: this.GLOBAL.relPx(0), // 左间距
          y: this.GLOBAL.relPx(50), // 上间距
          x2: this.GLOBAL.relPx(30), // 右间距
          y2: this.GLOBAL.relPx(30), // 下间距
          containLabel: true // grid 区域是否包含坐标轴的刻度标签, 常用于『防止标签溢出』的场景
        },
        // 提示框组件
        tooltip: {
          show: false,
          trigger: 'axis', // 触发类型, axis: 坐标轴触发
          axisPointer: {
            // 指示器类型  'line' 直线指示器 'shadow' 阴影指示器 'none' 无指示器 'cross' 十字准星指示器。
            // 其实是种简写，表示启用两个正交的轴的 axisPointer。
            type: 'none'
          },
          textStyle: {
            color: '#cdd3ee' // 文字颜色
          },
          // 提示框浮层内容格式器，支持字符串模板和回调函数两种形式
          // 折线（区域）图、柱状（条形）图、K线图 : {a}（系列名称），{b}（类目值），{c}（数值）, {d}（无）
          formatter: '{b}<br />{a0}: {c0}万件<br />{a1}: {c1}万件'
        },
        // 图例组件
        legend: {
          show: false,
          textStyle: { // 文本样式
            fontSize: this.GLOBAL.relPx(16),
            color: '#cdd3ee'
          },
          top: this.GLOBAL.relPx(20), // 定位
          data: ['衣服', '鞋子'] // 图例的数据数组
        },
        // X轴
        xAxis: {
          type: 'value', // 坐标轴类型,   'value' 数值轴，适用于连续数据
          // 坐标轴刻度
          axisTick: {
            show: false // 是否显示坐标轴刻度 默认显示
          },
          // 坐标轴轴线
          axisLine: { // 是否显示坐标轴轴线 默认显示
            show: true, // 是否显示坐标轴轴线 默认显示
            lineStyle: {
              // color: '#4C4C4C',
            }
          },
          // 坐标轴在图表区域中的分隔线
          splitLine: {
            show: true, // 是否显示分隔线。默认数值轴显示
            lineStyle: {
              color: '#4C4C4C',
              type: 'dashed',
              opacity: 0.4
            }
          },
          // 坐标轴刻度标签
          axisLabel: {
            show: true, // 是否显示刻度标签 默认显示
            lineStyle: {
              color: '#4C4C4C',
              type: 'dashed',
              opacity: 0.4
            }
          }
        },
        yAxis: [
          // 左侧Y轴
          {
            type: 'category', // 坐标轴类型,  'category' 类目轴，适用于离散的类目数据，为该类型时必须通过 data 设置类目数据
            // 坐标轴刻度
            axisTick: {
              show: false // 是否显示坐标轴刻度 默认显示
            },
            inverse: true,
            // 坐标轴轴线
            axisLine: { // 是否显示坐标轴轴线 默认显示
              show: false, // 是否显示坐标轴轴线 默认显示
              lineStyle: { // 坐标轴线线的颜色
                color: '#cdd3ee'
              }
            },
            // 坐标轴在图表区域中的分隔线
            splitLine: {
              show: false // 是否显示分隔线。默认数值轴显示
            },
            // 坐标轴刻度标签
            axisLabel: {
              show: true, // 是否显示刻度标签 默认显示
              fontSize: this.GLOBAL.relPx(12), // 文字的字体大小
              color: '#A9A9AA', // 刻度标签文字的颜色
              // 使用字符串模板，模板变量为刻度默认标签 {value}
              formatter: '{value}'
            },
            data: ydata
          },
          // 右侧Y轴
          // {
          //   type: 'category', // 坐标轴类型
          //   // 坐标轴轴线
          //   axisLine: {
          //     show: false
          //   },
          //   // 坐标轴刻度
          //   axisTick: {
          //     show: false
          //   },
          //   // 坐标轴刻度标签
          //   axisLabel: {
          //     show: true, // 是否显示刻度标签 默认显示
          //     fontSize: 12, // 文字的字体大小
          //     color: '#A9A9AA', // 刻度标签文字的颜色
          //     // 使用字符串模板，模板变量为刻度默认标签 {value}
          //     formatter: '{value}'
          //   },
          //   data: data
          // }
        ],
        // 系列列表
        series: [
          {
            name: "超算算力",
            type: "pictorialBar",
            symbolSize: [this.GLOBAL.relPx(20), this.GLOBAL.relPx(8)],
            symbolOffset: [this.GLOBAL.relPx(-10), this.GLOBAL.relPx(0)],
            symbolRotate: 90,
            z: 12,
            itemStyle: {
              "normal": {
                color: colors
              }
            },
            data: xdata
          },
          {
            name: '超算算力',
            type: 'bar',
            barWidth: this.GLOBAL.relPx(20),
            barGap: '10%',
            label: {
              normal: {
                show: true,//开启显示
                position: 'right',//柱形上方
                textStyle: { //数值样式
                  color: '#fff'
                }
              }
            },
            itemStyle: {
              "normal": {
                "color": {
                  "x": 1,
                  "y": 0,
                  "x2": 0,
                  "y2": 0,
                  "type": "linear",
                  "global": false,
                  "colorStops": colorsCenter
                }
              }
            },
            data: xdata
          },
          {
            name: "超算算力",
            type: "pictorialBar",
            symbolSize: [this.GLOBAL.relPx(20), this.GLOBAL.relPx(8)],
            symbolOffset: [this.GLOBAL.relPx(-10), this.GLOBAL.relPx(0)],
            symbolRotate: 90,
            z: 12,
            symbolPosition: "end",
            "itemStyle": {
              "normal": {
                color: colors
              }
            },
            data: xdata
          }
        ],
      }
      // 内存泄漏 无dom 不执行
      if (!this.myChartnewec2) {
        this.myChartnewec2 = echarts.init(this.$refs.newec2, null, { width: this.GLOBAL.relPx(500), height: this.GLOBAL.relPx(350) });
        this.GLOBAL.echartsDomArray.push(this.myChartnewec2)
      }
      this.myChartnewec2.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.myChartnewec2.setOption(option);
    },
    initMap() {
      if (!this.chartsMap) {
        this.chartsMap = echarts.init(this.$refs.map, null, { width: this.GLOBAL.relPx(380), height: this.GLOBAL.relPx(350) });
        this.GLOBAL.echartsDomArray.push(this.chartsMap)
      }
      let data1 = [
        { name: '天翼云上海32（自研）', value: [108.29001, 39.85529], warning: 5 },
        { name: '天翼云杭州2（自研）', value: [99.13732, 30.87743], warning: 5 },
        { name: '天翼云上海4（合营）', value: [105.35506, 28.49958], warning: 5 },
        { name: '天翼云西南1（自研）', value: [106.99942, 24.66568], warning: 5 },
      ]
      let data2 = [
        { name: '华为云上海32（自研）', value: [98.10959, 38.18275], warning: 5 },
        { name: '华为云华北-乌兰察布1', value: [104.99535, 35.16159], warning: 5 },
        { name: '临港1-国产30', value: [119.94876, 30.12094], warning: 5 },
      ]
      let data3 = [
        { name: '上海信息园区', value: [114.86152, 39.86618], warning: 5 },
        { name: '京津冀产业园数据中心', value: [120.66817, 32.32877], warning: 5 },
        { name: '武汉未来城二期', value: [121.43897, 31.14301], warning: 5 },
      ]

      let option = {
        backgroundColor: '',
        title: {
          left: 'center',
          textStyle: {
            color: '#fff'
          }
        },
        geo: {
          map: 'china',
          roam: false,
          zoom: 1.5,
          center: [103.41888, 35.54850],
          label: {
            show: false,
            color: "rgba(255,255,255,0.6)",
            fontSize: this.GLOBAL.relPx(12),
            fontFamily: 'Microsoft YaHei',
            // textBorderColor: '#f00000',
            // textBorderWidth: 5,
            // fontWeight: 'bold'
            emphasis: {
              show: true
            }
          },
          silent: true,
          itemStyle: {
            normal: {
              areaColor: 'rgba(51, 51, 51, 0.9)',
              borderColor: '#111'
            },
            emphasis: {
              areaColor: '#2a333d'
            }
          }
        },
        series: [
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: data1,
            showEffectOn: 'render',
            tooltip: {
              show: true,
              formatter: '{a}',
              triggerOn: "click",
            },
            symbolSize(value, params) {
              // return 0
              return params.data.warning
            },
            rippleEffect: {
              scale: 4, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "rgba(255, 192, 0,0.3)"
                },
                {
                  offset: 1,
                  color: "rgba(255, 192, 0,1)"
                }
                ]),
                shadowBlur: 10,
                shadowColor: '#333'
              }
            },
            emphasis: {
              show: false,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "rgba(0, 154, 228,0.3)"
                },
                {
                  offset: 1,
                  color: "rgba(0, 154, 228,1)"
                }
                ]),
              }
            },
            zlevel: 3
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: data2,
            showEffectOn: 'render',
            tooltip: {
              show: true,
              formatter: '{a}',
              triggerOn: "click",
            },
            symbolSize(value, params) {
              // return 0
              return params.data.warning
            },
            rippleEffect: {
              scale: 4, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "rgba(158, 210, 131,0.3)"
                },
                {
                  offset: 1,
                  color: "rgba(158, 210, 131,1)"
                }
                ]),
                shadowBlur: 10,
                shadowColor: '#333'
              }
            },
            emphasis: {
              show: false,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "rgba(0, 154, 228,0.3)"
                },
                {
                  offset: 1,
                  color: "rgba(0, 154, 228,1)"
                }
                ]),
              }
            },
            zlevel: 3
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            //要有对应的经纬度才显示，先经度再维度
            data: data3,
            showEffectOn: 'render',
            tooltip: {
              show: true,
              formatter: '{a}',
              triggerOn: "click",
            },
            symbolSize(value, params) {
              // return 0
              return params.data.warning
            },
            rippleEffect: {
              scale: 4, // 波纹的最大缩放比例
              brushType: 'stroke'
            },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "rgba(0, 183, 255,0.3)"
                },
                {
                  offset: 1,
                  color: "rgba(0, 183, 255,1)"
                }
                ]),
                shadowBlur: 10,
                shadowColor: '#333'
              }
            },
            emphasis: {
              show: false,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "rgba(0, 154, 228,0.3)"
                },
                {
                  offset: 1,
                  color: "rgba(0, 154, 228,1)"
                }
                ]),
              }
            },
            zlevel: 3
          }
        ]
      };
      this.chartsMap.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsMap.setOption(option);
      this.chartsMap.on('click', function (params) {
      });
    },
    initMap2() {
      if (!this.chartsMap2) {
        this.chartsMap2 = echarts.init(this.$refs.map2, null, { width: this.GLOBAL.relPx(860), height: this.GLOBAL.relPx(660) });
        this.GLOBAL.echartsDomArray.push(this.chartsMap2)
      }
      let option = {
        backgroundColor: '',
        title: {
          left: 'center',
          textStyle: {
            color: '#fff'
          }
        },
        geo: {
          map: 'china',
          aspectScale: 0.72, //长宽比
          center: [102.51888, 35.84850],
          zoom: 1.7,
          roam: false,
          label: {
            show: false
          },
          emphasis: {
            disabled: true,
            itemStyle: {
              normal: {
                areaColor: {
                  type: 'radial',
                  x: 0.3,
                  y: 0.3,
                  r: 0.8,
                  colorStops: [{
                    offset: 0,
                    color: '#464646' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: '#464646'  // 100% 处的颜色
                  }],
                  globalCoord: true // 缺省为 false
                },
                shadowColor: '#464646',
                shadowOffsetX: 5,
                shadowOffsetY: 5
              },
            },
          },
          itemStyle: {
            normal: {
              areaColor: {
                type: 'radial',
                x: 0.3,
                y: 0.3,
                r: 0.8,
                colorStops: [{
                  offset: 0,
                  color: '#464646' // 0% 处的颜色
                }, {
                  offset: 1,
                  color: '#464646'  // 100% 处的颜色
                }],
                globalCoord: true // 缺省为 false
              },
              shadowColor: '#464646',
              shadowOffsetX: 5,
              shadowOffsetY: 5
            },
          },
          regions: [{
            name: '南海诸岛',
            itemStyle: {
              areaColor: 'rgba(0, 10, 52, 1)',
              borderColor: 'rgba(0, 10, 52, 1)',
              normal: {
                opacity: 0,
                label: {
                  show: false,
                  color: "#009cc9",
                }
              }
            },
          }],
        },
        series: [
          {
            type: 'map',
            roam: false,
            center: [103.41888, 35.74850],
            label: {
              show: true,
              textStyle: {
                color: '#ccc'
              },
            },
            // selectedMode: false,
            selectedMode: 'multiple',
            emphasis: {
              disabled: true
            },
            select: {
              disabled: true,
              itemStyle: {
                borderColor: '#787879',
                borderWidth: 1,
                areaColor: '#334053'
              },
              label: {
                color: '#ccc'
              },
            },

            itemStyle: {
              borderColor: '#787879',
              borderWidth: 1,
              areaColor: '#333'
            },
            zoom: 1.7,
            //     roam: false,
            map: 'china', //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
          },
        ]
      };
      this.chartsMap2.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsMap2.setOption(option);
    },
    posiClick(index) {
      if (index === this.allIndex) {
        this.allIndex = null
      } else {
        this.allIndex = index
      }
    },
    ecShowIndexName(index) {
      if (this.ecShowIndex === index) {
        this.ecShowIndex = null
        if (this.myChartec1) {
          this.myChartec1.clear()
        }
        if (this.myChartec2) {
          this.myChartec2.clear()
        }
        if (this.myChartec3) {
          this.myChartec3.clear()
        }
      }
    },
    ecClick(index) {
      this.ecShowIndex = index
      if (index === 1) {
        this.ec1Fun()
        if (this.myChartec2) {
          this.myChartec2.clear()
        }
        if (this.myChartec3) {
          this.myChartec3.clear()
        }
      } else if (index === 2) {
        this.ec2Fun()
        if (this.myChartec1) {
          this.myChartec1.clear()
        }
        if (this.myChartec3) {
          this.myChartec3.clear()
        }
      } else if (index === 3) {
        this.ec3Fun()
        if (this.myChartec2) {
          this.myChartec2.clear()
        }
        if (this.myChartec1) {
          this.myChartec1.clear()
        }
      }
    },
    lengedChange() {
      let option = this.myChartnewec1.getOption()
      this.showSecond = !this.showSecond
      if (this.showSecond) {
        option.legend[0].selected = {
          '超算算力': false,
          '智算算力': true,
          '通用算力': false,
        }
      } else {
        option.legend[0].selected = {
          '超算算力': true,
          '智算算力': true,
          '通用算力': true,
        }
      }
      this.myChartnewec1.clear()
      this.myChartnewec1.setOption(option);
    },
    newEc3Fun() {
      // prettier-ignore
      const hours = ['09.22', '09.23', '09.24', '09.25', '09.26', '09.27', '09.28'];
      // prettier-ignore
      const days = [
        '东数西算策略', '绿色节能策略', '就近选择策略',
        '资源匹配策略', '均衡分配策略', '智能优选策略'
      ];
      // prettier-ignore
      const data = [
        [0, 0, 5], [0, 1, 2], [0, 2, 7], [0, 3, 6], [0, 4, 3], [0, 5, 4], [0, 6, 2],
        [1, 0, 4], [1, 1, 2], [1, 2, 3], [1, 3, 4], [1, 4, 5], [1, 5, 6], [1, 6, 4],
        [2, 0, 2], [2, 1, 1], [2, 2, 5], [2, 3, 7], [2, 4, 3], [2, 5, 3], [2, 6, 2],
        [3, 0, 3], [3, 1, 3], [3, 2, 5], [3, 3, 1], [3, 4, 3], [3, 5, 4], [3, 6, 4],
        [4, 0, 4], [4, 1, 1], [4, 2, 2], [4, 3, 5], [4, 4, 2], [4, 5, 5], [4, 6, 7],
        [5, 0, 6], [5, 1, 2], [5, 2, 1], [5, 3, 7], [5, 4, 4], [5, 5, 2], [5, 6, 3],
      ];
      const color = ['#43cbc9', '#6CB590', '#94CEE4', '#467597', '#526bbf', '#5b5699'];
      const title = [];
      const singleAxis = [];
      const series = [];
      days.forEach(function (day, idx) {
        title.push({
          textBaseline: 'middle',
          top: ((idx + 0.5) * 100) / 6 + '%',
          text: day,
          textStyle: {
            color: color[idx],
            fontSize: '14px'
          }
        });
        singleAxis.push({
          left: 150,
          type: 'category',
          boundaryGap: false,
          data: hours,
          top: (idx * 100) / 6 + 5 + '%',
          height: 100 / 6 - 10 + '%',
          nameTextStyle: {
            fontSize: 20
          },
          axisLabel: {
            color: '#FFFFFF',
            axisTick: {
              show: false
            },
            show: idx === 5 ? true : false,
            interval: 0
          }
        });
        series.push({
          singleAxisIndex: idx,
          coordinateSystem: 'singleAxis',
          type: 'scatter',
          // colorBy: 'data',
          data: [],
          symbolSize: function (dataItem) {
            return dataItem[1] * 4;
          }
        });
      });
      data.forEach(function (dataItem) {
        series[dataItem[0]].data.push([dataItem[1], dataItem[2]]);
      });
      let option = {
        // tooltip: {
        //   show: false,
        //   position: 'bottom'
        // },
        color: color,
        title: title,
        singleAxis: singleAxis,
        series: series,
        // yAxis: {
        //   splitNumber: 3,
        //   min: 0,
        //   max: 150,
        // }
      };
      // 内存泄漏 无dom 不执行
      if (!this.$refs.newec3) { return false }
      if (!this.myChartnewec3) {
        this.myChartnewec3 = echarts.init(this.$refs.newec3, null, { width: this.GLOBAL.relPx(940), height: this.GLOBAL.relPx(490) });
        this.GLOBAL.echartsDomArray.push(this.myChartnewec3)
      }
      this.myChartnewec3.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.myChartnewec3.setOption(option);
    },
    newEc1Fun() {
      const color = ['#F9CA58', '#9ED283', '#3861AD']
      const title = {
        text: '',
        // show: false,
        x: 'center',
        bottom: '0%',
        textStyle: {
          color: ' rgba(255,255,255,0.6)',
          fontSize: this.GLOBAL.relPx(12),
          fontWeight: 'normal'
        }
      }
      // mock数据
      // ['阿里云', '天翼云', '华为云', '上海超级计算中心'],
      const dataArr = {
        xdata: ['阿里云', '天翼云', '华为云', '上海超级计算中心'],
        vaccination: [32, 35, 45, ''],
        unvaccinated: [70, 80, 100, 90],
        unvaccinatedTwo: [44, 52, 63, '']
      }

      // tooltip
      const tooltip = {
        show: false,
        trigger: "axis",
        textStyle: { fontSize: '100%' },
        formatter: params => {
          let rander = params.map(item => item.seriesType !== "pictorialBar" ? `<div>${item.seriesName}: ${item.seriesType !== "line" ? item.value : item.value + "%"}</div>` : '').join('')
          return `
            <div>${params[0].axisValue}</div>
            ${rander}
        `
        }
      }
      const legend = {
        data: ['通用算力', '智算算力', '超算算力'],
        textStyle: { fontSize: this.GLOBAL.relPx(12), color: 'rgba(255,255,255,0.6)' },
        itemWidth: this.GLOBAL.relPx(25),
        itemHeight: this.GLOBAL.relPx(15),
        itemGap: 15,
        right: '5%',
        top: '20',
        selectedMode: false
      }
      const grid = { top: '60', left: '5%', right: '7%', bottom: '10%' }
      // xAxis
      const xAxis = {
        axisTick: { show: true },
        axisLine: { lineStyle: { color: 'rgba(255,255,255, .2)' } },
        axisLabel: { textStyle: { fontSize: this.GLOBAL.relPx(12), color: '#A9A9AA' }, },
        data: dataArr.xdata
      }

      // yAxis
      const yAxis = [{
        splitNumber: 3,
        min: 0,
        max: 150,
        axisTick: { show: false },
        axisLine: { show: false, },
        splitLine: { lineStyle: { color: 'rgba(255,255,255, .05)' } },
        axisLabel: { textStyle: { fontSize: this.GLOBAL.relPx(12), color: '#A9A9AA' } }
      }, {
        show: true,
        max: 100,
        splitLine: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          textStyle: { fontSize: this.GLOBAL.relPx(12), color: '#A9A9AA' },
          formatter: params => {
            return `${params}%`
          }
        }
      }]

      // series
      const series = [
        {
          name: "通用算力",
          type: "pictorialBar",
          symbolSize: [this.GLOBAL.relPx(20), this.GLOBAL.relPx(10)],
          symbolOffset: [this.GLOBAL.relPx(-22), this.GLOBAL.relPx(5)],
          z: 12,
          itemStyle: {
            "normal": {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: "rgba(249,202,88,0.7)"
              },
              {
                offset: 1,
                color: "rgba(249,202,88,1)"
              }
              ])
            }
          },
          data: dataArr.vaccination
        },
        {
          name: '通用算力',
          type: 'bar',
          barWidth: this.GLOBAL.relPx(20),
          barGap: '10%',
          label: {
            normal: {
              show: false,//开启显示
              position: 'top',//柱形上方
              textStyle: { //数值样式
                color: '#fff'
              }
            }
          },
          itemStyle: {
            "normal": {
              "color": {
                "x": 0,
                "y": 0,
                "x2": 0,
                "y2": 1,
                "type": "linear",
                "global": false,
                "colorStops": [{//第一节下面
                  "offset": 0,
                  "color": "rgba(249,202,88,0)"
                }, {
                  "offset": 1,
                  "color": "rgba(249,202,88,0.7)"
                }]
              }
            }
          },
          data: dataArr.vaccination
        },
        {
          name: "通用算力",
          type: "pictorialBar",
          symbolSize: [this.GLOBAL.relPx(20), this.GLOBAL.relPx(10)],
          symbolOffset: [this.GLOBAL.relPx(-22), this.GLOBAL.relPx(5)],
          z: 12,
          symbolPosition: "end",
          "itemStyle": {
            "normal": {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: "rgba(249,202,88,0.7)"
              },
              {
                offset: 1,
                color: "rgba(249,202,88,1)"
              }
              ])
            }
          },
          data: dataArr.vaccination
        },

        {
          name: "智算算力",
          type: "pictorialBar",
          symbolSize: [this.GLOBAL.relPx(20), this.GLOBAL.relPx(10)],
          symbolOffset: [this.GLOBAL.relPx(0), this.GLOBAL.relPx(5)],
          z: 12,
          itemStyle: {
            "normal": {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: "rgba(158,210,131,0.7)"
              },
              {
                offset: 1,
                color: "rgba(158,210,131,1)"
              }
              ])
            }
          },
          data: dataArr.unvaccinated
        },
        {
          name: '智算算力',
          type: 'bar',
          barWidth: this.GLOBAL.relPx(20),
          barGap: '10%',
          label: {
            normal: {
              show: false,//开启显示
              position: 'top',//柱形上方
              textStyle: { //数值样式
                color: '#fff'
              }
            }
          },
          itemStyle: {
            "normal": {
              "color": {
                "x": 0,
                "y": 0,
                "x2": 0,
                "y2": 1,
                "type": "linear",
                "global": false,
                "colorStops": [{//第一节下面
                  "offset": 0,
                  "color": "rgba(158,210,131,0)"
                }, {
                  "offset": 1,
                  "color": "rgba(158,210,131,0.7)"
                }]
              }
            }
          },
          data: dataArr.unvaccinated
        },
        {
          name: "智算算力",
          type: "pictorialBar",
          symbolSize: [this.GLOBAL.relPx(20), this.GLOBAL.relPx(10)],
          symbolOffset: [this.GLOBAL.relPx(0), this.GLOBAL.relPx(5)],
          z: 12,
          symbolPosition: "end",
          "itemStyle": {
            "normal": {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: "rgba(158,210,131,0.7)"
              },
              {
                offset: 1,
                color: "rgba(158,210,131,1)"
              }
              ])
            }
          },
          data: dataArr.unvaccinated
        },

        {
          name: "超算算力",
          type: "pictorialBar",
          symbolSize: [this.GLOBAL.relPx(20), this.GLOBAL.relPx(10)],
          symbolOffset: [this.GLOBAL.relPx(22), this.GLOBAL.relPx(5)],
          z: 12,
          itemStyle: {
            "normal": {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: "rgba(56,97,173,0.7)"
              },
              {
                offset: 1,
                color: "rgba(56,97,173,1)"
              }
              ])
            }
          },
          data: dataArr.unvaccinatedTwo
        },
        {
          name: '超算算力',
          type: 'bar',
          barWidth: this.GLOBAL.relPx(20),
          barGap: '10%',
          label: {
            normal: {
              show: false,//开启显示
              position: 'top',//柱形上方
              textStyle: { //数值样式
                color: '#fff'
              }
            }
          },
          itemStyle: {
            "normal": {
              "color": {
                "x": 0,
                "y": 0,
                "x2": 0,
                "y2": 1,
                "type": "linear",
                "global": false,
                "colorStops": [{//第一节下面
                  "offset": 0,
                  "color": "rgba(56,97,173,0)"
                }, {
                  "offset": 1,
                  "color": "rgba(56,97,173,0.7)"
                }]
              }
            }
          },
          data: dataArr.unvaccinatedTwo
        },
        {
          name: "超算算力",
          type: "pictorialBar",
          symbolSize: [this.GLOBAL.relPx(20), this.GLOBAL.relPx(10)],
          symbolOffset: [this.GLOBAL.relPx(22), this.GLOBAL.relPx(5)],
          z: 12,
          symbolPosition: "end",
          "itemStyle": {
            "normal": {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: "rgba(56,97,173,0.7)"
              },
              {
                offset: 1,
                color: "rgba(56,97,173,1)"
              }
              ])
            }
          },
          data: dataArr.unvaccinatedTwo
        }
      ]
      let option = {
        color,
        title, tooltip, xAxis, yAxis, series, grid, legend, backgroundColor: '', textStyle: {
          fontFamily: 'Microsoft YaHei'
        },
      }
      // 内存泄漏 无dom 不执行
      if (!this.$refs.newec1) { return false }
      if (!this.myChartnewec1) {
        this.myChartnewec1 = echarts.init(this.$refs.newec1, null, { width: this.GLOBAL.relPx(940), height: this.GLOBAL.relPx(300) });
        this.GLOBAL.echartsDomArray.push(this.myChartnewec1)
      }
      this.myChartnewec1.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.myChartnewec1.setOption(option);
    },
    random(m, n) {
      return Math.random() * (n - m) + m
    },
    numberRandom() {
      this.numRandomTimer = setInterval(() => {
        this.num1 = random(45, 40).toFixed(2)
        this.num2 = random(50, 40).toFixed(2)
        this.num3 = random(45, 40).toFixed(2)

      }, this.random(3, 1) * 1000);
      this.GLOBAL.timerArraySet.push(this.numRandomTimer)
      this.numRandomTimer2 = setInterval(() => {
        this.indexEc6 += 1
        if (this.indexEc6 > 3) {
          this.indexEc6 = 1
        }
        this.title = ['GPU利用率趋势', '内存利用率趋势', 'CPU利用率趋势'][this.indexEc6 - 1]
        this.ec6Fun()
      }, 6000);
      this.GLOBAL.timerArraySet.push(this.numRandomTimer2)
    },
    ec1Fun() {
      let _this = this
      let option = {
        color: 'rgba(252, 200, 88,0.5)',
        polar: {
          radius: [0, '80%']
        },
        angleAxis: {
          startAngle: 90,
          min: 0,
          max: 60,
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.5)'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.2)'
            }
          },
        },
        radiusAxis: {
          type: 'category',
          data: ['微软云', 'AWS', '华为云', '腾讯云', '阿里云', '天翼云'],
          axisLabel: {
            interval: 0,
            color: "rgba(255,255,255,0.5)",
            fontSize: 12
          },
        },
        series: {
          type: 'bar',
          data: [4, 1, 11, 26, 48, 39],
          coordinateSystem: 'polar',
          barWidth: this.GLOBAL.relPx(6),
          label: {
            show: false,
            position: 'middle',
            formatter: '{b}: {c}'
          }
        },
        tooltip: {
          trigger: 'axis',
          position: ['100%', '0%'],
          // 设置浮层的 css 样式
          extraCssText: `width:${_this.GLOBAL.relPx(388)}px;height:auto;border: none; background-color:rgba(26, 25, 28,0.88);color:#fff;font-size:${_this.GLOBAL.relPx(14)}px;box-sizing: border-box;padding: ${_this.GLOBAL.relPx(20)}px;`,
          formatter: function (params) {
            let list = []
            if (params[0].name == '天翼云') {
              list = _this.textArray1[0]
            } else if (params[0].name == '阿里云') {
              list = _this.textArray1[1]
            } else if (params[0].name == '华为云') {
              list = _this.textArray1[2]
            } else if (params[0].name == 'AWS') {
              list = _this.textArray1[3]
            } else if (params[0].name == '腾讯云') {
              list = _this.textArray1[4]
            } else if (params[0].name == '微软云') {
              list = _this.textArray1[5]
            } else {
              list = [params[0].name]
            }
            let htmllist = ``
            list.forEach(ele => {
              htmllist += `<div class="son" style="width: 50%;margin-top: ${_this.GLOBAL.relPx(10)}px;">${ele}</div>`
            })
            let html = `<div class="">
              <div class="title" style="color: #00E4FF;">${params[0].name}</div>
              <div class="flex-box">
                ${htmllist}
              </div>
            </div>`
            return html
          },
        },
      };
      // 内存泄漏 无dom 不执行
      if (!this.myChartec1) {
        this.myChartec1 = echarts.init(this.$refs.ec1, null, { width: this.GLOBAL.relPx(190), height: this.GLOBAL.relPx(190) });
        this.GLOBAL.echartsDomArray.push(this.myChartec1)
      }
      this.myChartec1.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.myChartec1.setOption(option);
    },
    ec2Fun() {
      let _this = this
      let option = {
        color: 'rgba(146, 205, 115,1)',
        polar: {
          radius: [0, '80%']
        },
        angleAxis: {
          startAngle: 90,
          min: 0,
          max: 40,
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.5)'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.2)'
            }
          },
        },
        radiusAxis: {
          type: 'category',
          data: ['临港-国产1', '苏州智算中心1', '苏州智算中心2', '华为云', '阿里云'],
          axisLabel: {
            interval: 0,
            color: "rgba(255,255,255,0.5)",
            fontSize: this.GLOBAL.relPx(12)
          },
        },
        series: {
          type: 'bar',
          data: [1, 1, 1, 15, 25],
          coordinateSystem: 'polar',
          barWidth: this.GLOBAL.relPx(10),
          label: {
            show: false,
            position: 'middle',
            formatter: '{b}: {c}'
          },
        },
        tooltip: {
          trigger: 'axis',
          position: ['100%', '0%'],
          // 设置浮层的 css 样式
          extraCssText: `width:${_this.GLOBAL.relPx(388)}px;height:auto;border: none; background-color:rgba(26, 25, 28,0.88);color:#fff;font-size:${_this.GLOBAL.relPx(14)}px;box-sizing: border-box;padding: ${_this.GLOBAL.relPx(20)}px;`,
          formatter: function (params) {
            let list = []
            if (params[0].name == '华为云') {
              list = _this.textArray2[0]
            } else if (params[0].name == '阿里云') {
              list = _this.textArray2[1]
            } else {
              list = [params[0].name]
            }
            let htmllist = ``
            list.forEach(ele => {
              htmllist += `<div class="son" style="width: 50%;margin-top: ${_this.GLOBAL.relPx(10)}px;">${ele}</div>`
            })
            let html = `<div class="">
              <div class="title" style="color: #00E4FF;">${params[0].name}</div>
              <div class="flex-box">
                ${htmllist}
              </div>
            </div>`
            return html
          },
        },
      };
      // 内存泄漏 无dom 不执行
      if (!this.myChartec2) {
        this.myChartec2 = echarts.init(this.$refs.ec2, null, { width: this.GLOBAL.relPx(190), height: this.GLOBAL.relPx(190) });
        this.GLOBAL.echartsDomArray.push(this.myChartec2)
      }
      this.myChartec2.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.myChartec2.setOption(option);
    },
    ec3Fun() {
      let _this = this
      let option = {
        color: 'rgba(85, 111, 198,0.5)',
        polar: {
          radius: [0, '80%']
        },
        angleAxis: {
          startAngle: 90,
          min: 0,
          max: 10,
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.5)'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.2)'
            }
          },
        },
        radiusAxis: {
          type: 'category',
          data: ['阿里云', '天翼云'],
          axisLabel: {
            interval: 0,
            color: "rgba(255,255,255,0.5)",
            fontSize: this.GLOBAL.relPx(12)
          },
        },
        series: {
          type: 'bar',
          data: [1, 4],
          coordinateSystem: 'polar',
          barWidth: this.GLOBAL.relPx(20),
          label: {
            show: false,
            position: 'middle',
            formatter: '{b}: {c}'
          }
        },
        tooltip: {
          trigger: 'axis',
          position: ['100%', '0%'],
          // 设置浮层的 css 样式
          extraCssText: `width:${_this.GLOBAL.relPx(388)}px;height:auto;border: none; background-color:rgba(26, 25, 28,0.88);color:#fff;font-size:${_this.GLOBAL.relPx(14)}px;box-sizing: border-box;padding: ${_this.GLOBAL.relPx(20)}px;`,
          formatter: function (params) {
            let list = []
            if (params[0].name == '天翼云') {
              list = _this.textArray3[0]
            } else if (params[0].name == '阿里云') {
              list = _this.textArray3[1]
            }
            let htmllist = ``
            list.forEach(ele => {
              htmllist += `<div class="son" style="width: 50%;margin-top: ${_this.GLOBAL.relPx(10)}px;">${ele}</div>`
            })
            let html = `<div class="">
              <div class="title" style="color: #00E4FF;">${params[0].name}</div>
              <div class="flex-box">
                ${htmllist}
              </div>
            </div>`
            return html
          },
        },
      };
      // 内存泄漏 无dom 不执行
      if (!this.myChartec3) {
        this.myChartec3 = echarts.init(this.$refs.ec3, null, { width: this.GLOBAL.relPx(190), height: this.GLOBAL.relPx(190) });
        this.GLOBAL.echartsDomArray.push(this.myChartec3)
      }
      this.myChartec3.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.myChartec3.setOption(option);
    },
    ec4Fun() {
      let option = {
        textStyle: {
          fontFamily: 'Microsoft YaHei'
        },
        color: ['#84C330', '#DF5E3F', '#E2B048', '#2FA6D8', '#195EB5'],
        // title: [{
        //   text: '全国数据中心单机柜平均功率分布',
        //   x: 'center',
        //   bottom: '10%',
        //   textStyle: {
        //     color: 'rgba(255,255,255,0.6)',
        //     fontSize: '12px',
        //     fontWeight: 'normal'
        //   }
        // }, {
        //   text: '2022年',
        //   x: 'center',
        //   y: 'center',
        //   // bottom: '10%',
        //   textStyle: {
        //     color: 'rgba(255,255,255,0.9)',
        //     fontSize: '18px',
        //     fontWeight: 'normal'
        //   }
        // }],
        tooltip: {
          show: false,
          trigger: 'item'
        },
        legend: {
          width: '30%',
          right: '0%',
          top: 'center',
          textStyle: {
            color: 'rgba(255,255,255,0.6)'
          }
        },
        series: [
          {
            name: '2022年',
            type: 'pie',
            radius: ['45%', '60%'],
            center: ['30%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 0,
              borderColor: '#222226',
              borderWidth: this.GLOBAL.relPx(3)
            },
            left: 0,
            label: {
              show: true,
              position: 'inside',
              color: '#ffffff',
              formatter: '{d}%',
              shadowColor: '#000'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: this.GLOBAL.relPx(40),
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },

            data: [
              { value: 8, name: '图像视频处理' },
              { value: 40, name: '数据挖掘分析' },
              { value: 32, name: '语义理解' },
              { value: 8, name: '信息预测' },
              { value: 12, name: '其他' }
            ]
          }
        ]
      };
      // 内存泄漏 无dom 不执行
      if (!this.$refs.ec4) { return false }
      if (!this.myChartec4) {
        this.myChartec4 = echarts.init(this.$refs.ec4, null, { width: this.GLOBAL.relPx(400), height: this.GLOBAL.relPx(240) });
        this.GLOBAL.echartsDomArray.push(this.myChartec4)
      }
      this.myChartec4.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.myChartec4.setOption(option);
    },
    ec5Fun() {
      let option = {
        color: ['rgba(255,149,0,0.2)', 'rgba(0,228,255,0.2)', 'rgba(0,178,255,0.2)'],

        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['阿里云', '天翼云', '华为云'],
          show: true,
          right: '5%',
          textStyle: {
            color: '#ffffff'
          }
        },
        grid: {
          top: '15%',
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: ['9.25', '9.26', '9.27', '9.28'],
            splitLine: {
              lineStyle: {
                opacity: 0.4
              }
            },
          }
        ],
        yAxis: [
          {
            type: 'value',
            splitNumber: 2,
            axisLabel: {
              show: true
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                opacity: 0.2
              }
            },
            min: 200,
            max: 800
          }
        ],
        series: [
          {
            name: '阿里云',
            data: [300, 250, 400, 450],
            type: 'line',
            z: 3,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                {
                  offset: 0,
                  color: 'rgba(255,149,0,0.05)'
                },
                {
                  offset: 1,
                  color: 'rgba(255,149,0,0.2)'
                }
              ])
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: 'rgba(255,149,0,0.2)'
                }
              }
            },
            symbol: "none",
            smooth: true,
          },
          {
            name: '天翼云',
            data: [600, 800, 750, 800],
            type: 'line',
            z: 1,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                {
                  offset: 0,
                  color: 'rgba(0,228,255, 0.05)'
                },
                {
                  offset: 1,
                  color: 'rgba(0,228,255,0.2)'
                }
              ])
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: 'rgba(0,228,255,0.2)'
                }
              }
            },
            symbol: "none",
            smooth: true,
          },
          {
            name: '华为云',
            data: [400, 700, 600, 700],
            type: 'line',
            z: 2,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(0,178,255,0.05)'
                },
                {
                  offset: 1,
                  color: 'rgba(0,178,255,0.2)'
                }
              ])
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: "rgba(0,178,255,0.2)"
                }
              }
            },
            symbol: "none",
            smooth: true,
          }
        ]
      };
      // 内存泄漏 无dom 不执行
      if (!this.$refs.ec5) { return false }
      if (!this.myChartec5) {
        this.myChartec5 = echarts.init(this.$refs.ec5, null, { width: this.GLOBAL.relPx(430), height: this.GLOBAL.relPx(240) });
        this.GLOBAL.echartsDomArray.push(this.myChartec5)
      }
      this.myChartec5.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.myChartec5.setOption(option);
    },
    ec6Fun_1() {
      return {
        color: ['#FF9500', '#00E4FF'],

        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['国产', '国外'],
          show: true,
          right: '5%',
          textStyle: {
            color: '#ffffff'
          }
        },
        grid: {
          top: '15%',
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: ['9.24', '9.25', '9.26', '9.27', '9.28'],
            splitLine: {
              lineStyle: {
                opacity: 0.4
              }
            },
          }
        ],
        yAxis: [
          {
            type: 'value',
            splitNumber: 2,
            axisLabel: {
              show: true
            },
            min: 0,
            max: 100,
            splitLine: {
              lineStyle: {
                type: 'dashed',
                opacity: 0.2
              }
            },
          }
        ],
        series: [
          {
            name: '国产',
            data: [25.06, 30.14, 50.66, 90.21, 65.12],
            type: 'line',
            z: 3,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(255,149,0,0.05)'
                },
                {
                  offset: 1,
                  color: 'rgba(255,149,0,0.3)'
                }
              ])
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: 'rgba(255,149,0,0.3)'
                }
              }
            },
            symbol: "none",
            smooth: true,
          },
          {
            name: '国外',
            data: [8.15, 14.22, 25.46, 66.41, 7.25],
            type: 'line',
            z: 4,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(0,228,255, 0.05)'
                },
                {
                  offset: 1,
                  color: 'rgba(0,228,255,0.3)'
                }
              ])
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: 'rgba(0,228,255,0.3)'
                }
              }
            },
            symbol: "none",
            smooth: true,
          }
        ]
      };
    },
    ec6Fun_2() {
      return {
        grid: {
          left: '5%',
          right: '5%',
          top: '15%',
          bottom: '20%',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['9.24', '9.25', '9.26', '9.27', '9.28'],
          axisLine: {
            lineStyle: {
              width: 0.5,
            }
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 2,
          min: 0,
          max: 100,
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [
          {
            data: [60, 80, 30, 60, 80],
            type: 'line',
            areaStyle: {
              normal: {
                // 渐变填充色（线条下半部分）
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(43, 193, 255,0.3)" },
                  { offset: 1, color: "rgba(43, 193, 255,0.05)" }
                ])
              }
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: "rgba(50, 94, 252,0.3)"
                }
              }
            },
            symbol: "none",
            smooth: true,
          }
        ]
      }
    },
    ec6Fun_3() {
      return {
        grid: {
          left: '5%',
          right: '5%',
          top: '15%',
          bottom: '20%',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['9.24', '9.25', '9.26', '9.27', '9.28'],
          axisLine: {
            lineStyle: {
              width: 0.5,
            }
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 2,
          min: 0,
          max: 100,
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [
          {
            data: [50, 90, 20, 70, 90],
            type: 'line',
            areaStyle: {
              normal: {
                // 渐变填充色（线条下半部分）
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(50, 94, 252,0.3)" },
                  { offset: 1, color: "rgba(50, 94, 252,0.05)" }
                ])
              }
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  color: "rgba(50, 94, 252,0.3)"
                }
              }
            },
            symbol: "none",
            smooth: true,
          }
        ]
      }
    },
    ec6Fun() {
      let option = {}
      if (this.indexEc6 == 1) {
        option = this.ec6Fun_1()
      }
      if (this.indexEc6 == 2) {
        option = this.ec6Fun_2()
      }
      if (this.indexEc6 == 3) {
        option = this.ec6Fun_3()
      }
      // 内存泄漏 无dom 不执行
      if (!this.$refs.ec6) { return false }
      if (!this.myChartec6) {
        this.myChartec6 = echarts.init(this.$refs.ec6, null, { width: this.GLOBAL.relPx(900), height: this.GLOBAL.relPx(240) });
        this.GLOBAL.echartsDomArray.push(this.myChartec6)
      }
      this.myChartec6.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.myChartec6.setOption(option);
    },
    resetFun() {
      if (this.myChartec1) {
        this.myChartec1.dispose()
      }
      if (this.myChartec2) {
        this.myChartec2.dispose()
      }
      if (this.myChartec3) {
        this.myChartec3.dispose()
      }
      if (this.myChartec4) {
        this.myChartec4.dispose()
      }
      if (this.myChartec5) {
        this.myChartec5.dispose()
      }
      if (this.myChartec6) {
        this.myChartec6.dispose()
      }
      if (this.numRandomTimer) {
        clearInterval(this.numRandomTimer)
      }
      if (this.numRandomTimer2) {
        clearInterval(this.numRandomTimer2)
      }
    }
  },
  beforeDestroy() {
    // this.resetFun()
  },
};
</script>

<style lang="scss" scoped>
.page3 {
  .mainBox {
    margin-top: 10px;

    .leftMap {
      position: relative;

      .posiCore {
        .posiitem {
          position: absolute;
          z-index: 3;

          .core {
            width: 40px;
            height: 40px;
            position: relative;
            z-index: 6;

            .icon {
              background-size: cover;
              width: 18px;
              height: 18px;
              cursor: pointer;

              &.icon1 {
                background-image: url('~@/assets/images/1920/page3/icon1.png');
              }

              &.icon2 {
                background-image: url('~@/assets/images/1920/page3/icon2.png');
              }

              &.icon3 {
                background-image: url('~@/assets/images/1920/page3/icon3.png');
              }

              &:hover {
                transform: scale(1.1);
                // animation-play-state: paused;
              }

              &.opa {
                opacity: 0.3;
                animation-play-state: paused;
              }

              // &.ani {
              //   &.icon1 {
              //     animation: imgOpacity31 5s ease-in-out infinite forwards;
              //   }

              //   &.icon2 {
              //     animation: imgOpacity31 3s ease-in-out infinite forwards;
              //   }

              //   &.icon3 {
              //     animation: imgOpacity31 2s ease-in-out infinite forwards;
              //   }
              // }
            }
          }

          .posiCoreright {
            width: 230px;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 5;

            .line {
              background-image: url('~@/assets/images/1920/page3/line.png');
              background-size: cover;
              width: 30px;
              height: 1px;
              margin-top: 20px;
            }

            .info {
              width: 198px;
              background: rgba(36, 154, 168, 0.1);
              border: 1px solid rgba($color: #00E4FF, $alpha: 0.5);
              border-radius: 4px;
              box-sizing: border-box;

              .title {
                font-size: 14px;
                color: #00E4FF;
                text-align: center;
                padding-top: 10px;
              }

              .titleline {
                background-image: url('~@/assets/images/1920/page3/line2.png');
                background-size: cover;
                width: 123px;
                height: 1px;
                margin: 0 auto;
                margin-top: 10px;
              }

              .list {
                padding: 10px 0;

                .item {
                  margin: 8px;
                  font-size: 14px;

                  .icon {
                    margin: 0 auto;
                  }

                  div {
                    text-align: center;
                  }
                }
              }
            }

            &.ts {
              top: -10px;
              left: 20px;
            }

            &.zs {
              top: -10px;
              left: 35px;
            }

            &.cs {
              top: 8px;
              left: 25px;
            }
          }

          &.posi1 {
            // 北京
            top: 240px;
            left: 590px;
          }

          &.posi2 {
            // 天津
            top: 260px;
            left: 610px;
          }

          &.posi3 {
            // 河北
            top: 270px;
            left: 570px;
          }

          &.posi4 {
            // 山西
            top: 290px;
            left: 530px;
          }

          &.posi5 {
            // 内蒙
            top: 210px;
            left: 510px;
          }

          &.posi6 {
            // 辽宁
            top: 220px;
            left: 680px;
          }

          &.posi7 {
            // 吉林
            top: 190px;
            left: 730px;
          }

          &.posi8 {
            // 黑龙江
            top: 120px;
            left: 760px;
          }

          &.posi9 {
            // 上海
            top: 400px;
            left: 660px;
          }

          &.posi10 {
            // 江苏
            top: 370px;
            left: 640px;
          }

          &.posi11 {
            // 浙江
            top: 440px;
            left: 640px;
          }

          &.posi12 {
            // 安徽
            top: 390px;
            left: 600px;
          }

          &.posi13 {
            // 福建
            top: 490px;
            left: 610px;
          }

          &.posi14 {
            // 江西
            top: 450px;
            left: 580px;
          }

          &.posi15 {
            // 山东
            top: 310px;
            left: 620px;
          }

          &.posi16 {
            // 河南
            top: 350px;
            left: 550px;
          }

          &.posi17 {
            // 湖北
            top: 400px;
            left: 540px;
          }

          &.posi18 {
            // 湖南
            top: 460px;
            left: 530px;
          }

          &.posi19 {
            // 广东
            top: 540px;
            left: 560px;
          }

          &.posi20 {
            // 海南
            top: 610px;
            left: 500px;
          }

          &.posi21 {
            // 广西
            top: 530px;
            left: 470px;
          }

          &.posi22 {
            // 甘肃
            top: 270px;
            left: 380px;
          }

          &.posi23 {
            // 陕西
            top: 340px;
            left: 490px;
          }

          &.posi24 {
            // 新疆
            top: 230px;
            left: 210px;
          }

          &.posi25 {
            // 青海
            top: 350px;
            left: 350px;
          }

          &.posi26 {
            // 宁夏
            top: 300px;
            left: 450px;
          }

          &.posi27 {
            // 重庆
            top: 420px;
            left: 470px;
          }

          &.posi28 {
            // 四川
            top: 400px;
            left: 420px;
          }

          &.posi29 {
            // 贵州
            top: 470px;
            left: 460px;
          }

          &.posi30 {
            // 云南
            top: 530px;
            left: 400px;
          }

          &.posi31 {
            // 西藏
            top: 410px;
            left: 250px;
          }

          // 32 台湾 33 澳门
          &.posi34 {
            // 香港
            top: 560px;
            left: 570px;
          }

        }
      }

      .lengedList {
        position: absolute;
        bottom: 220px;
        left: 0;
        z-index: 1;

        .lengeditem {
          margin: 0 20px;
          margin-top: 20px;

          .color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid;
            margin-top: 5px;
            margin-right: 10px;

            .inner {
              width: 8px;
              height: 8px;
              display: block;
              margin: 0 auto;
              margin-top: 2px;
              border-radius: 50px;
            }

            &.c1 {
              border-color: #FCC858;

              .inner {
                background: #FCC858;
              }
            }

            &.c2 {
              border-color: #92CD73;

              .inner {
                background: #92CD73;
              }
            }

            &.c3 {
              border-color: #556FC6;

              .inner {
                background: #556FC6;
              }
            }
          }

          .text {
            font-size: 14px;
          }
        }
      }
    }

    .leftCore {
      width: 914px;

      .mapechartsLenged {
        position: absolute;
        top: 50px;
        right: 50px;

        .mapechartsLengedItem {
          margin-left: 20px;
          cursor: pointer;

          .color {
            width: 12px;
            height: 6px;
            margin-top: 7px;
            margin-right: 10px;
          }

          .text {
            font-size: 14px;
          }

          &.c1 {
            .color {
              background: #F9CA58;
            }
          }

          &.c2 {
            .color {
              background: #9ED283;
            }
          }

          &.c3 {
            .color {
              background: #3861AD;
            }
          }

          &.on {
            &.c1 {
              .text {
                color: #F9CA58;
              }
            }

            &.c2 {
              .text {
                color: #9ED283;
              }
            }

            &.c3 {
              .text {
                color: #3861AD;
              }
            }
          }
        }
      }

      .leftTitle {
        font-size: 14px;

        &.cur {
          cursor: pointer;
        }

        &.on {
          // margin-top: 40px;
        }
      }

      .leftBg {
        width: 100%;
        // height: 418px;
        padding: 20px 20px;
        background-color: rgba($color: #26262A, $alpha: 0.15);
        // margin-bottom: 20px;
        box-sizing: border-box;
        position: relative;

        &.on {
          margin-top: 20px;
        }
      }

      .ecCore {
        position: relative;

        .ec1 {
          background-image: url('~@/assets/images/page3/ec1.png');
          background-size: cover;
          width: 190px;
          height: 190px;

          &.on {
            background-image: url('~@/assets/images/page3/ec11.png');
            position: absolute;
            top: 0px;
            left: 0px;
            z-index: 9999;
            line-height: 190px;
            text-align: center;
            font-size: 56px;
            cursor: pointer;

            span {
              font-size: 22px;
            }
          }
        }

        .ec2 {
          background-image: url('~@/assets/images/page3/ec2.png');
          background-size: cover;
          width: 190px;
          height: 190px;

          &.on {
            background-image: url('~@/assets/images/page3/ec22.png');
            position: absolute;
            top: 0px;
            left: 0px;
            z-index: 9999;
            line-height: 190px;
            text-align: center;
            font-size: 56px;
            cursor: pointer;

            span {
              font-size: 22px;
            }
          }
        }

        .ec3 {
          background-image: url('~@/assets/images/page3/ec3.png');
          background-size: cover;
          width: 190px;
          height: 190px;

          &.on {
            background-image: url('~@/assets/images/page3/ec33.png');
            position: absolute;
            top: 0px;
            left: 0px;
            z-index: 9999;
            line-height: 190px;
            text-align: center;
            font-size: 56px;
            cursor: pointer;

            span {
              font-size: 22px;
            }
          }
        }

        .ecName {
          font-size: 14px;
          color: #ffffff;
          text-align: center;
          margin-top: 20px;

          &.c1 {
            color: #FCC858;
            cursor: pointer;
          }

          &.c2 {
            color: rgba(158, 210, 131, 1);
            cursor: pointer;
          }

          &.c3 {
            color: #556FC6;
            cursor: pointer;
          }
        }
      }

      .listBox {
        width: 840px;
        margin: 0px auto 0 auto;

        .item {
          background-image: url('~@/assets/images/page3/item.png');
          background-size: cover;
          width: 168px;
          height: 194px;
          text-align: center;

          .text1 {
            font-size: 40px;
            line-height: 40px;
            padding-top: 50px;
          }

          .text2 {
            font-size: 12px;
            line-height: 12px;
            padding-top: 10px;
          }

          .text3 {
            font-size: 18px;
            line-height: 18px;
            padding-top: 14px;
          }
        }
      }

      .bottomTitle {
        font-size: 26px;
        margin-top: 60px;
        text-align: center;
      }
    }

    .roundList {
      width: 100%;
      height: 290px;
      // overflow: hidden;
      overflow-x: visible;
      overflow-y: scroll;
      align-content: flex-start;

      &.noscroll {
        overflow: auto;
      }

      .rounditem {
        margin: 10px 20px;
        width: 160px;
        height: 130px;
        cursor: pointer;
        position: relative;

        &.hide {
          opacity: 0.3;
        }

        .roundPosi {
          width: 200px;
          height: 80px;
          background: #201F23;
          border: 1px solid #111111;
          border-radius: 8px;
          position: absolute;
          z-index: 999;
          top: 140px;
          left: 50%;
          margin-left: -100px;

          .line {
            width: 1px;
            height: 70px;
            background-image: url('~@/assets/images/page3/line2.png');
            background-size: cover;
          }

          .posiSon {
            width: calc(50% - 1px);
            text-align: center;
            padding-top: 10px;

            .tp {
              font-size: 24px;
              line-height: 36px;

              span {
                font-size: 20px;
              }
            }

            .dn {
              font-size: 16px;
              margin-top: 5px;
            }
          }
        }

        &.four {
          .roundPosi {
            left: -330px;
          }
        }

        .roundBox2 {
          margin: 0 auto;
        }

        .roundText {
          font-size: 14px;
          margin-top: 15px;
          text-align: center;
        }
      }
    }

    .rightThree {
      .item {
        box-sizing: border-box;
        width: 250px;

        .bigSix {
          background-image: url('~@/assets/images/1920/page3/item_on.png');
          width: 129px;
          height: 150px;
          position: relative;
          background-size: cover;
          margin: 40px auto;
          cursor: pointer;

          .bigSixPosiCore {
            .item {
              width: 124px;
              height: 144px;
              background-image: url('~@/assets/images/1920/page3/items.png');
              background-size: cover;
              box-sizing: border-box;
              padding-top: 35px;
              position: absolute;
              text-align: center;

              .num {
                font-size: 28px;
                line-height: 28px;
              }

              .ff {
                font-size: 10px;
                line-height: 10px;
                margin-top: 10px;
              }

              .nn {
                font-size: 12px;
                line-height: 12px;
                margin-top: 10px;
              }

              &.posi1 {
                top: -115px;
                left: -65px;
              }

              &.posi2 {
                top: -115px;
                right: -65px;
              }

              &.posi3 {
                top: 5px;
                left: -130px;
              }

              &.posi4 {
                top: 5px;
                right: -130px;
              }

              &.posi5 {
                bottom: -115px;
                left: -65px;
              }

              &.posi6 {
                bottom: -115px;
                right: -65px;
              }

              &.on {
                background-image: url('~@/assets/images/1920/page3/itemson.png');
              }

              &.on-high {
                background-image: url('~@/assets/images/1920/page3/itemshighon.png') !important;
              }
            }
          }

          .bigSixPosiCoreHighlight {
            background-image: url('~@/assets/images/1920/page3/item_high.png') !important;
          }

          .core {
            padding-top: 30px;
            text-align: center;

            .num {
              font-size: 40px;

              span {
                font-size: 28px;
                vertical-align: top;
              }
            }

            .label {
              font-size: 18px;
              margin-top: 5px;
            }

          }

          &.opa {
            background-image: url('~@/assets/images/1920/page3/item.png');
            opacity: 0.8;
          }
        }

        .tt {
          font-size: 14px;
          line-height: 16px;
        }

        .vl {
          font-size: 42px;
          line-height: 42px;
          text-align: center;
          margin-top: 10px;

          span {
            font-size: 23px;
          }
        }

        .sl {
          font-size: 16px;
          line-height: 16px;
          margin-top: 15px;
          text-align: center;
        }

        .smallline {
          width: 20px;
          height: 2px;
          background: #00B7FF;
          margin: 0 auto;
          margin-top: 20px;
        }

        .smallname {
          font-size: 14px;
          color: #ffffff;
          text-align: center;
          margin-top: 10px;
          line-height: 17px;
          cursor: pointer;

          img {
            width: 17px;
            height: 17px;
            margin-right: 10px;
          }

          &.tson {
            color: #DBB04F;
          }

          &.zson {
            color: #91CC75;
          }

          &.cson {
            color: #5470C5;
          }
        }

        .line {
          background-image: url('~@/assets/images/page3/line.png');
          background-size: cover;
          width: 250px;
          height: 1px;
          margin: 0 auto;
          margin-top: 15px;
          margin-bottom: 10px;
        }

        .son {
          width: 45%;
          line-height: 17px;
          margin-top: 10px;
          box-sizing: border-box;
          font-size: 14px;

          .ic {
            width: 17px;
            height: 17px;
            margin-right: 5px;

            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }

    .rightCore {
      width: 2250px;
      margin-right: 100px;
      display: none;


      .centerBox {

        width: 950px;
        position: relative;

        .centerBoxPosi {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 9;
          width: 950px;
          height: 660px;

          &.hide {
            z-index: -1;
            opacity: 0;
          }

          .leftBg {
            width: 100%;
            height: 100%;
            padding: 28px 20px;
            background-color: rgba($color: #26262A, $alpha: 0.15);
            // margin-bottom: 20px;
            box-sizing: border-box;
          }

          .bottomTitle {
            font-size: 26px;
            margin-top: 60px;
            text-align: center;
          }
        }

        .item {
          width: 464px;
          height: 268px;
          background: rgba($color: (
              #26262A),
            $alpha: 0.15
            );
          margin-bottom: 20px;
          box-sizing: border-box;
          padding: 18px;

          .wordCore {
            padding-top: 80px;
            text-align: center;
            font-size: 68px;
            line-height: 68px;

            span {
              font-size: 24px;
            }
          }
        }
      }



      .rightBox {
        width: 950px;
        position: relative;



        .rightBoxPosi {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 9;
          width: 100%;

          &.hide {
            z-index: -1;
            opacity: 0;
          }

          .bottomTitle {
            font-size: 26px;
            margin-top: 60px;
            text-align: center;
          }


        }

        .leftBg {
          width: 100%;
          padding: 28px 20px;
          background-color: rgba($color: #26262A, $alpha: 0.15);
          // margin-bottom: 20px;
          box-sizing: border-box;
          position: relative;
        }

        .listBox {
          width: 840px;
          margin: 0px auto 0 auto;

          .item {
            background-image: url('~@/assets/images/page3/item.png');
            background-size: cover;
            width: 168px;
            height: 194px;
            text-align: center;

            .text1 {
              font-size: 40px;
              line-height: 40px;
              padding-top: 50px;
            }

            .text2 {
              font-size: 12px;
              line-height: 12px;
              padding-top: 10px;
            }

            .text3 {
              font-size: 18px;
              line-height: 18px;
              padding-top: 14px;
            }
          }
        }

        .pointBox {
          width: 100%;
          height: 170px;
          overflow: hidden;
          margin-top: 30px;

          .le {
            width: 75%;
            height: 100%;
            position: relative;

            .perBox {
              width: 107px;
              height: 107px;
              background: #1A191C;
              border: 1px solid #BB6222;
              border-radius: 50%;
              position: absolute;
              z-index: 1;
              top: 50%;
              left: 50%;
              margin-top: -53.5px;
              margin-left: -53.5px;
              text-align: center;

              .text1 {
                font-size: 36px;
                padding-top: 15px;

                span {
                  font-size: 18px;
                }
              }

              .text2 {
                font-size: 18px;
                padding-top: 5px;
              }
            }

            .pointList {
              .son {
                width: 8px;
                height: 8px;
                background: #BB6222;
                border-radius: 50%;
                margin-right: 8px;
                margin-bottom: 8px;
              }
            }
          }

          .rt {
            width: 25%;
            height: 100%;
            position: relative;

            .perBox {
              width: 56px;
              height: 56px;
              background: #1A191C;
              border: 1px solid #4686B0;
              border-radius: 50%;
              position: absolute;
              z-index: 1;
              top: 50%;
              left: 50%;
              margin-top: -28px;
              margin-left: -28px;
              text-align: center;

              .text1 {
                font-size: 20px;
                padding-top: 10px;

                span {
                  font-size: 12px;
                }
              }

              .text2 {
                font-size: 12px;
                padding-top: 5px;
              }
            }

            .pointList {
              .son {
                width: 8px;
                height: 8px;
                background: #4686B0;
                border-radius: 50%;
                margin-right: 8px;
                margin-bottom: 8px;
              }
            }
          }
        }

        .list {
          .item {
            width: 302px;
            height: 268px;
            background: rgba($color: (
                #26262A),
              $alpha: 0.15
              );
            margin-bottom: 20px;
            box-sizing: border-box;
            padding: 18px;

            .wordCore {
              padding-top: 80px;
              text-align: center;
              font-size: 68px;
              line-height: 68px;

              span {
                font-size: 24px;
              }
            }
          }
        }

        .bottomEcBox {
          width: 947px;
          height: 268px;
          background: rgba($color: (
              #26262A),
            $alpha: 0.15
            );
          box-sizing: border-box;
          padding: 18px;

          .ec6List {
            .item {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background-color: #CCCCCC;
              margin-left: 8px;

              &.on {
                background-color: #00E4FF;
              }
            }
          }
        }
      }
    }

    .bottomWord {
      font-size: 26px;
      color: #ffffff;
      text-align: center;
      padding-top: 50px;
    }
  }

  .second {
    &.hide {
      opacity: 0;
    }
  }

  .leftTitleRight {
    font-size: 14px;
    color: rgba(0, 228, 255, 1);
    cursor: pointer;

    .icon {
      background-image: url('~@/assets/images/page3/right-icon1.png');
      width: 17px;
      height: 17px;
      background-size: contain;
      margin-top: 2px;
      margin-right: 10px;

      &.on {
        background-image: url('~@/assets/images/page3/right-icon2.png');
      }
    }

    .text {}
  }

  .numberList {
    width: 874px;
    height: 183px;
    text-align: center;
    font-size: 14px;
    background-color: rgba($color: #26262A, $alpha: 0.15);
    box-sizing: border-box;
    padding: 20px 0;
    // position: absolute;
    // bottom: 0px;
    // left: 20px;
    margin-top: 30px;

    .item {
      padding-left: 100px;

      .ss {
        width: 80px;
        line-height: 32px;
        cursor: pointer;

        .icon {
          width: 32px;
          height: 32px;
          margin-right: 10px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        &.hide {
          opacity: 0.5;
        }

        &:nth-child(8),
        &:nth-child(9) {
          width: 100px;
        }
      }
    }

    .item2 {
      margin-top: 17px;

      .name {
        width: 100px;
        line-height: 17px;
        cursor: pointer;

        img {
          margin-right: 10px;
          width: 17px;
          height: 17px;
        }
      }

      .ss {
        width: 80px;

        &.hide {
          opacity: 0.5;
        }

        &:nth-child(9),
        &:nth-child(10) {
          width: 100px;
        }
      }

      &.opa {
        opacity: 0.3;
      }
    }
  }

  .ecBox {
    padding: 20px 20px 0 20px;

    .ecRightBottom {}

    .name {
      font-size: 16px;
      text-align: center;
    }
  }

  .ecPosi {
    width: 614px;
    height: 348px;
    background: rgba($color: #201F23, $alpha: 0.9);
    border-radius: 8px;
    position: absolute;
    top: 40px;
    left: 150px;
    z-index: 5;

    .titleBox {
      padding: 10px;
    }

    .close {
      background-image: url('~@/assets/images/page5/close.png');
      background-size: cover;
      width: 25px;
      height: 25px;
      cursor: pointer;
    }
  }

  .downlist {
    .item {
      background-image: url('~@/assets/images/1920/page3/rightitem.png');
      background-size: cover;
      width: 876px;
      height: 134px;
      margin-top: 20px;
      box-sizing: border-box;
      padding: 0 20px;

      .lc {
        width: 240px;

        .top {
          padding-top: 25px;

          .ll {
            text-align: center;

            .num {
              font-size: 28px;
            }

            .label {
              font-size: 14px;
              padding: 5px 0;
            }

            .point {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              margin: 0 auto;

              &.blue {
                background: #469EE8;
              }

              &.green {
                background: #37BB78;
              }
            }
          }

          .line {
            background-image: url('~@/assets/images/1920/page3/rightitemline.png');
            background-size: cover;
            width: 1px;
            height: 55px;
            margin: 10px 30px 0 30px;
          }
        }

        .down {
          margin-top: 5px;

          .icon {
            width: 17px;
            height: 17px;
            margin-top: 3px;
            margin-right: 5px;

            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }

      .rc {
        margin-top: 20px;
      }
    }
  }
}

@keyframes imgOpacity31 {

  0% {
    opacity: 0.3;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.3;
  }
}
</style>
