<template>
  <div>
    <div class="header flex-box-between">
      <div class="logo-title flex-box">
        <!-- <div class="logo"><img src="~@/assets/images/1920/logo1.png" alt=""></div> -->
        <div class="title">长三角算力资源一体化调度示范平台</div>
      </div>
      <!-- <div class="logo-title flex-box">
        <div class="logo on"><img src="~@/assets/images/1920/logo2.png" alt=""></div>
      </div> -->
    </div>
    <div class="navigationSilder flex-box-end">
      <div class="item" v-for="(item, index) in navList" :key="index" @click.stop="routerChnage(index)"
        :class="index == navListIndex ? 'on' : ''"></div>
    </div>
    <div class="headerTitle flex-box" @click="indexClick">
      <div class="icon">
        <img v-if="icon === 0" src="~@/assets/images/common/page0.png" alt="">
        <img v-if="icon === 1" src="~@/assets/images/1920/page0/change.png" alt="">
        <!-- <img v-if="icon === 1" src="~@/assets/images/common/page1.png" alt=""> -->
        <img v-if="icon === 2" src="~@/assets/images/common/page2.png" alt="">
        <img v-if="icon === 3" src="~@/assets/images/common/page3.png" alt="">
        <img v-if="icon === 4" src="~@/assets/images/common/page4.png" alt="">
        <img v-if="icon === 5" src="~@/assets/images/common/page5.png" alt="">
        <img v-if="icon === 6" src="~@/assets/images/common/page6.png" alt="">
        <img v-if="icon === 7" src="~@/assets/images/common/page6.png" alt="">
      </div>
      <div class="name">{{ name }}</div>
    </div>
  </div>
</template>
<script>
import { getStore, setStore } from "@/common/util";

export default {
  name: 'headerCommon',
  props: {
    icon: {
      type: Number,
      default: null
    },
    name: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      navList: [],
      navListIndex: 0
    }
  },
  created() {

  },
  mounted() {
    this.navList = [
      { name: "page0", path: '/' },
      { name: "page6", path: '/page6' },
      { name: "page3", path: '/page3' },
      { name: "page14", path: '/page14' },
      { name: "page2", path: '/page2' },
      { name: "page12", path: '/page12' },
      { name: "page13", path: '/page13' },
    ]
    // this.navList = getStore('navList')
    let name = this.$route.name
    this.navList.forEach((ele, index) => {
      if (name === ele.name) {
        this.navListIndex = index
      }
    })
    if (name === 'page4') {
      this.navListIndex = 2
    }
    if (name === 'page5') {
      this.navListIndex = 1
    }
    let that = this
    window.addEventListener('keydown', this.handleKeydown)
  },
  methods: {
    indexClick() {
      this.$emit('indexClick', 'indexClick')
    },
    handleKeydown(e) {
      let that = this
      if (e.key == 'ArrowDown') {
        that.routerChnage(that.navListIndex + 1, '888111223')
      }
      if (e.key == 'ArrowUp') {
        that.routerChnage(that.navListIndex - 1, '888111223')
      }
    },
    routerChnage(index, index2) {
      let number = this.navListIndex
      if (index < 0) {
        index = this.navList.length - 1
      }
      if (index > this.navList.length - 1) {
        index = 0
      }
      let path = this.navList[index].path
      if (number != index) {
        if (index === 0) {
          this.$router.push({
            path: path,
            query: {
              isSkip: true
            }
          }).catch(() => {

          })
        } else {
          this.$router.push({
            path: path
          }).catch(() => {

          })
        }
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('keydown', this.handleKeydown)
  },
}
</script>

<style lang="scss">
.header {
  padding-top: 25px;
  // height: 140px;
  overflow: hidden;
  box-sizing: border-box;
  max-width: 100%;

  .logo-title {
    .logo {
      width: 130px;
      height: 35px;

      img {
        width: 100%;
        height: 100%;
      }

      &.on {
        width: 114px;
        height: 25px;
        margin-top: 5px;
      }
    }

    .title {
      padding-left: 10px;
      font-size: 26px;
      color: #F1F1F2;
      line-height: 35px;
    }
  }


}

.navigationSilder {
  padding-top: 25px;
  box-sizing: border-box;

  .item {
    width: 12px;
    height: 12px;
    border: 2px solid #5F5F61;
    box-shadow: 0px 0px 0px 2px rgba(11, 13, 17, 0.58), 0px 1px 1px 0px #FFFFFF;
    border-radius: 2px;
    margin-left: 15px;
    cursor: pointer;

    &.on {
      background: #CDCDCD;
      box-shadow: 0px 0px 0px 2px rgba(11, 13, 17, 0.58), 0px 1px 1px 0px #FFFFFF;
    }
  }
}

.headerTitle {
  padding-top: 25px;
  padding-left: 10px;
  cursor: pointer;

  // padding-bottom: 75px;
  .icon {
    width: 32px;
    height: 32px;
    background: #26262A;
    border-radius: 50%;
    box-sizing: border-box;
    padding-top: 5px;

    img {
      display: block;
      margin: 0 auto;
      width: 70%;
    }
  }

  .name {
    font-size: 20px;
    color: #00E4FF;
    line-height: 32px;
    padding-left: 10px;
  }
}

@media (min-width: 1920px) and (max-width: 2559px) {
  .headerTitle {
    // padding-bottom: 70px;
  }
}

@media (min-width: 2560px) and (max-width: 3839px) {
  .headerTitle {
    padding-bottom: 300px;
  }
}
</style>

