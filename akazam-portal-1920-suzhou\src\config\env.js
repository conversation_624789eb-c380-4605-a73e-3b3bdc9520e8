/**
 * 配置编译环境和线上环境之间的切换
 *
 * baseUrl: 域名地址
 * routerMode: 路由模式
 * credential: 跨域
 *
 */

// const dev = {
//   routerMode: "history", // hash
//   baseUrl: `http://test.xiuft.com:83`, // 自定义反向代理
//   credential: true,
// };

// const stage = {
//   routerMode: "history", // hash
//   baseUrl: `http://test.xiuft.com:83`, // 自定义反向代理
//   credential: true,
// };

// const prod = {
//   routerMode: "history", // hash
//   baseUrl: `https://172.17.79.16:82`,         // 自定义反向代理
//   credential: true,
// };

// export default process.env.NODE_ENV === "development" ? dev : process.env.NODE_ENV === "staging" ? stage : prod;
