<template>
  <div class="pageWork1 flex-box-between">
    <div class="leftStep flex-box">
      <div class="stepList">
        <div class="item on">作业入池</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(2)">资源池调度</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(3)">模型训练</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(4)">应用部署</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(5)">推理验证</div>
      </div>
      <div class="wordList">
        <p :class="wordAni1 ? 'ani' : ''">应用名称：图像识别</p>
        <p :class="wordAni1 ? 'ani' : ''">作业名称(1)：智算作业-图像分类学习</p>
        <p :class="wordAni4 ? 'ani' : ''" class="flex-box"><i v-if="iconAni1" class="el-icon-loading"></i><img
            v-if="iconAni2" src="~@/assets/images/page4/step1/success.png" alt="">数据集： 上传训练数据集 </p>
        <p :class="wordAni5 ? 'ani' : ''" class="flex-box"><i v-if="iconAni3" class="el-icon-loading"></i><img
            v-if="iconAni4" src="~@/assets/images/page4/step1/success.png" alt="">配置代码：上传训练代码</p>
        <p :class="wordAni2 ? 'ani' : ''">作业名称(2)：通算作业-图像应用Build </p>
        <p :class="wordAni3 ? 'ani' : ''">作业入池时间：{{ timeDate }}</p>
      </div>
    </div>
    <div class="rightBox flex-box-between">
      <div class="centerCore">

        <div class="item flex-box" :class="itemAni1 ? 'ani' : ''">
          <div class="iconBox">
            <div class="icon"><img src="~@/assets/images/page4/step1/icon-1.png" alt=""></div>
            <div class="word">智算作业</div>
          </div>
          <div class="arrow"><img src="~@/assets/images/page4/step1/arrow-1.png" alt=""></div>
          <div class="pointList">
            <VueMatrixRaindrop v-if="showCode" class="codeBox" :canvasWidth="canvasWidth" :canvasHeight="canvasHeight">
            </VueMatrixRaindrop>
            <div class="point flex-box-between">
              <div class="son" v-for="(item, index) in point" :class="ponit1Index > index ? 'on' : ''" :key="index"></div>
            </div>
            <div class="percentList">
              <div class="word flex-box-between" :style="{ 'width': `${percent > 20 ? percent : 20}%` }">
                <p>{{ percentdesc }}</p>
                <p>{{ percent }}%</p>
              </div>
              <div class="line">
                <div class="inner" :style="{ 'width': `${percent}%` }"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="item flex-box" :class="itemAni2 ? 'ani' : ''">
          <div class="iconBox">
            <div class="icon"><img src="~@/assets/images/page4/step1/icon-2.png" alt=""></div>
            <div class="word">通算作业</div>
          </div>
          <div class="arrow"><img src="~@/assets/images/page4/step1/arrow-2.png" alt=""></div>
          <div class="pointList">
            <div class="point flex-box-between">
              <div class="son s2" v-for="(item, index) in point" :class="ponit2Index > index ? 'on' : ''" :key="index">
              </div>
            </div>
            <div class="percentList">
              <div class="word flex-box-between" :style="{ 'width': `${percent2 > 15 ? percent2 : 15}%` }">
                <p>{{ percentdesc2 }}</p>
                <p>{{ percent2 }}%</p>
              </div>
              <div class="line">
                <div class="inner" :style="{ 'width': `${percent2}%` }"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="wordBottom" :class="wordBottomAni ? 'ani' : ''">{{ percentdesc3 }}</div>
      </div>
      <div class="rightCore">
        <div class="topCore flex-box-between">
          <div class="item" :class="imgItemAni ? 'ani' : ''">
            <div class="icon">
              <img src="~@/assets/images/page4/step1/rl-i1.png" alt="" :class="imgLeftAni ? 'ani' : ''">
              <div class="number">{{ time1 }}<span>s</span></div>
            </div>
            <div class="word">智算作业-图像分类学习</div>
          </div>
          <div class="item" :class="imgItemAni ? 'ani' : ''">
            <div class="icon" :class="imgLeftAni ? 'ani' : ''">
              <img src="~@/assets/images/page4/step1/rl-i2.png" alt="" :class="imgLeftAni ? 'ani' : ''">
              <div class="number">{{ time2 }}<span>s</span></div>
            </div>
            <div class="word">通算作业-图像应用Build</div>
          </div>
        </div>
        <div class="wordBottom" :class="wordBottomAni2 ? 'ani' : ''">作业完成预计所需时间</div>
      </div>
    </div>
  </div>
</template>
<script>
import { getStore, setStore } from "@/common/util";
import VueMatrixRaindrop from '../vueMatrixDigitRain/index.vue'
import VueMatrixRaindrop2 from '../vueMatrixDigitRain/index2.vue'

export default {
  name: 'workstep1',
  components: {
    VueMatrixRaindrop,
    VueMatrixRaindrop2
  },
  data() {
    return {
      point: [],
      percent: 0,
      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      wordAni4: false,
      wordAni5: false,
      iconAni1: true,
      iconAni2: false,
      iconAni3: true,
      iconAni4: false,
      showCode: false,
      timer: null,
      itemAni1: false,
      itemAni2: false,
      percent: 0,
      percent2: 0,
      percentdesc: '上传训练数据集...',
      percentdesc2: '作业入池中...',
      percentdesc3: '作业入池中...',
      wordBottomAni: false,
      imgItemAni: false,
      imgItemAni2: false,
      time1: 0,
      time2: 0,
      time1Timer: null,
      time2Timer: null,
      timeDate: '',
      imgLeftAni: false,
      imgRightAni: false,
      ponit1Index: null,
      ponit2Index: null,
      pointAnitimer1: null,
      pointAnitimer2: null,
      wordBottomAni2: null,
      canvasWidth: 770,
      canvasHeight: 90,
      isAuto:null
    }
  },
  created() {
    this.isAuto = this.GLOBAL.isAuto
    const date = new Date(); // 时间戳是一个以毫秒为单位的整数
    const year = date.getFullYear(); // 获取当前年份，比如2022
    const month = date.getMonth() + 1; // 获取当前月份，返回值为0-11，需要+1
    const day = date.getDate(); // 获取当前日期，比如12
    const hour = date.getHours(); // 获取当前小时数，0-23
    const minute = date.getMinutes(); // 获取当前分钟数，0-59
    const second = date.getSeconds(); // 获取当前秒数，0-59
    this.timeDate = `${year}/${month}/${day} ${hour}:${minute}:${second}`
    this.canvasWidth = this.GLOBAL.relPx(770)
    this.canvasHeight = this.GLOBAL.relPx(90)
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      let arr = []
      for (let i = 0; i < 96; i++) {
        arr.push(i)
      }
      this.point = arr
      let settimer1 = setTimeout(() => {
        this.wordAni1 = true
      }, 500);
      let settimer2 = setTimeout(() => {
        this.wordAni2 = true
      }, 1000);
      let settimer3 = setTimeout(() => {
        this.wordAni3 = true
      }, 1500);
      let settimer4 = setTimeout(() => {
        this.itemAni1 = true
        this.wordAni4 = true
        this.pointAni()
        this.lineAni()
      }, 2000);
      let settimer5 = setTimeout(() => {
        this.itemAni2 = true
        this.lineAni2()
        this.pointAni2()
      }, 2500);
      let settimer6 = setTimeout(() => {
        this.wordBottomAni = true
        this.imgItemAni = true
        this.imgLeftAni = true
        this.timeFun()
      }, 3000);
      let settimer7 = setTimeout(() => {
        this.imgItemAni2 = true
        this.imgRightAni = true
        this.wordBottomAni2 = true
      }, 3500);

      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
      })
    },
    timeFun() {
      this.time1Timer = setInterval(() => {
        this.time1 += 1
        if (this.time1 >= 36) {
          clearInterval(this.time1Timer)
          this.$emit('stepEnd', 1)
        }
      }, 620);
      this.time2Timer = setInterval(() => {
        this.time2 += 1
        if (this.time2 >= 20) {
          clearInterval(this.time2Timer)
        }
      }, 620);
    },
    endFun(index) {
      if (this.isAuto) {
        return false
      }
      this.$emit('endFun', index)
    },
    lineAni() {
      this.timer = setInterval(() => {
        this.percent += 1
        if (this.percent >= 70) {
          this.percentdesc = '上传训练代码'
          this.showCode = true
          this.iconAni1 = false
          this.iconAni2 = true
          this.wordAni5 = true
        }
        if (this.percent >= 100) {
          clearInterval(this.timer)
          this.percentdesc = '作业已入池'
          this.percentdesc3 = '作业已入池'
          this.iconAni3 = false
          this.iconAni4 = true
        }
      }, 210);
    },
    lineAni2() {
      this.timer2 = setInterval(() => {
        this.percent2 += 1
        if (this.percent2 >= 100) {
          clearInterval(this.timer2)
          this.percentdesc2 = '作业已入池'
        }
      }, 105);
    },
    pointAni() {
      this.ponit1Index = 0
      this.pointAnitimer1 = setInterval(() => {
        this.ponit1Index += 1
        if (this.ponit1Index > 96) {
          clearInterval(this.pointAnitimer1)
        }
      }, 150);
    },
    pointAni2() {
      this.ponit2Index = 0
      this.pointAnitimer2 = setInterval(() => {
        this.ponit2Index += 1
        if (this.ponit2Index > 96) {
          clearInterval(this.pointAnitimer2)
        }
      }, 110);
    },
    resetFun() {
      if (this.timer1) {
        clearInterval(this.timer1)
      }
      if (this.timer2) {
        clearInterval(this.timer2)
      }
      if (this.time1Timer) {
        clearInterval(this.time1Timer)
      }
      if (this.time2Timer) {
        clearInterval(this.time2Timer)
      }
      if (this.pointAnitimer1) {
        clearInterval(this.pointAnitimer1)
      }
      if (this.pointAnitimer2) {
        clearInterval(this.pointAnitimer2)
      }
    },
  },
  beforeDestroy() {
    this.resetFun()
  },
}
</script>

<style lang="scss">
.pageWork1 {

  .rightBox {
    width: 2400px;
    box-sizing: border-box;
    padding-right: 50px;

    .centerCore {
      .item {
        margin-top: 130px;
        opacity: 0;

        .iconBox {
          width: 157px;
          height: 168px;
          background: rgba($color: #26262A, $alpha: 0.3);
          border: 1px solid rgba($color: #347AFE, $alpha: 0.3);
          box-shadow: 0px 2px 14px 2px rgba(13, 14, 64, 0.3);
          border-radius: 4px;
          padding-top: 30px;
          box-sizing: border-box;

          .icon {
            width: 60px;
            height: 60px;
            margin: 0 auto;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .word {
            font-size: 20px;
            color: #ffffff;
            text-align: center;
            padding-top: 10px;
          }
        }

        .arrow {
          width: 41px;
          height: 14px;
          margin-top: 70px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .pointList {
          width: 810px;
          height: 168px;
          background: rgba($color: #2A2A2D, $alpha: 0.3);
          box-shadow: 0px 2px 14px 2px rgba(13, 14, 64, 0.3);
          border-radius: 4px;
          box-sizing: border-box;
          padding: 10px 20px;
          position: relative;

          .codeBox {
            opacity: 1;
            position: absolute;
            z-index: 1;
            top: 10px;
            left: 20px;
          }

          .point {
            width: 100%;
            // height: 72px;

            .son {
              width: 10px;
              height: 10px;
              border-radius: 50%;
              background-color: rgba($color: #7FB41C, $alpha: 0.4);
              margin: 10px 7px;

              &.on {
                background-color: rgba($color: #3174F3, $alpha: 1);
              }

              &.s2 {
                &.on {
                  background-color: rgba($color: #00C2FC, $alpha: 1);
                }
              }
            }
          }

          .percentList {
            margin-top: 10px;
            padding: 0 7px;

            .word {
              p {
                font-size: 14px;
                color: #ffffff;
                padding: 0;
                margin: 10px 0;
              }
            }

            .line {
              background-color: #1A191C;
              border-radius: 3px;

              .inner {
                background-image: url('~@/assets/images/page4/step1/percent.png');
                border-radius: 3px;
                // background-size: cover;
                width: 80%;
                height: 6px;
              }
            }
          }
        }

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }

      .wordBottom {
        font-size: 26px;
        color: #FFFFFF;
        text-align: center;
        margin-top: 150px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }

    .rightCore {
      width: 1100px;
      margin-top: 150px;

      .topCore {
        .item {
          width: 400px;
          opacity: 0;

          .icon {
            background-image: url('~@/assets/images/page4/step1/rl-bg.png');
            width: 362px;
            height: 362px;
            background-size: cover;
            position: relative;
            margin: 0 auto;


            img {
              width: 203px;
              height: 203px;
              display: block;
              position: absolute;
              top: 50%;
              left: 50%;
              margin-left: -101.5px;
              margin-top: -101.5px;

              &.ani {
                animation: imgOpacity 2s linear infinite alternate both;
              }
            }

            .number {
              font-size: 65px;
              text-align: center;
              padding-top: 150px;
              line-height: 65px;

              span {
                font-size: 44px;
              }
            }

          }

          .word {
            font-size: 20px;
            color: #ffffff;
            text-align: center;
            margin-bottom: 20px;
          }

          .time {
            font-size: 40px;
            color: #ffffff;
            text-align: center;
            font-weight: bold;
          }

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }

          &.right {
            width: 541px;

            .icon {
              width: 541px;
              height: 356px;
              margin-bottom: 90px;
              position: relative;

              .posiImg {
                position: absolute;
                top: 0;
                left: 0;
              }

              img {
                &.ani {
                  animation: imgOpacity 1s linear infinite alternate both;
                }
              }
            }
          }
        }
      }

      .wordBottom {
        font-size: 26px;
        color: #FFFFFF;
        text-align: center;
        margin-top: 180px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }
  }
}

@keyframes imgOpacity {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
</style>
