<template>
  <div class="pageWork3 flex-box-between">
    <div class="leftStep flex-box">
      <div class="stepList flex-box">
        <div class="item on al" @click="endFun(1)">需求输入</div>
        <div class="arraw on al"></div>
        <div class="item on al" @click="endFun(2)">资源池分配</div>
        <div class="arraw on al"></div>
        <div class="item on al" @click="endFun(3)">运行环境设置</div>
        <div class="arraw on al"></div>
        <div class="item on flex-box-center">
          <div class="icon"></div>
          模型训练
        </div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(5)">推理部署</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(7)">验证访问</div>
      </div>
      <div class="wordList">
        <div class="wordListItem">
          <p :class="wordAni1 ? 'ani' : ''">应用-智算作业：图像分类学习</p>
          <p :class="wordAni2 ? 'ani' : ''" class="flex-box">
            <i v-if="iconAni1" class="el-icon-loading"></i>
            <img
              v-if="iconAni2"
              src="~@/assets/images/page4/step1/success.png"
              alt=""
            />
            {{ word1 }}
          </p>
        </div>
        <div class="wordListItem">
          <p :class="wordAni3 ? 'ani' : ''">应用-通算作业：图像应用打包</p>
          <div class="flex-box">
            <p :class="wordAni4 ? 'ani' : ''" class="flex-box">
              <i v-if="iconAni3" class="el-icon-loading"></i>
              <img
                v-if="iconAni4"
                src="~@/assets/images/page4/step1/success.png"
                alt=""
              />
              数据接收完成
            </p>
            <p :class="wordAni5 ? 'ani' : ''" class="flex-box">
              <i v-if="iconAni5" class="el-icon-loading"></i>
              <img
                v-if="iconAni6"
                src="~@/assets/images/page4/step1/success.png"
                alt=""
              />
              打包完成
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="rightBox flex-box-between">
      <div class="item">
        <div class="title">模型训练</div>
        <div class="imgCore" v-show="!showImgList">
          <div class="posi posi1" :class="imgAni1 ? 'ani' : ''">
            <div class="text">图像数据集</div>
            <div class="img img1">
              <img src="~@/assets/images/1920/work/step4/img1.png" alt="" />
            </div>
          </div>
          <div class="posi posi2" :class="imgAni2 ? 'ani' : ''">
            <div class="text">数据预处理</div>
            <div class="arr arr1">
              <img src="~@/assets/images/1920/work/step4/arr1.png" alt="" />
            </div>
          </div>
          <div class="posi posi3" :class="imgAni3 ? 'ani' : ''">
            <div class="text">训练数据</div>
            <div class="img img2">
              <img src="~@/assets/images/1920/work/step4/img2.png" alt="" />
            </div>
          </div>
          <div class="posi posi4" :class="imgAni4 ? 'ani' : ''">
            <div class="text">构建分层网络模型</div>
            <div class="arr arr2">
              <img src="~@/assets/images/1920/work/step4/arr2.png" alt="" />
            </div>
          </div>
          <div class="posi posi5 flex-box" :class="imgAni5 ? 'ani' : ''">
            <div class="text">卷积神经网络</div>
            <div class="img img3">
              <img src="~@/assets/images/1920/work/step4/img3.png" alt="" />
            </div>
          </div>
          <div class="posi posi6" :class="imgAni6 ? 'ani' : ''">
            <div class="text">特征抽取</div>
            <div class="arr arr3">
              <img src="~@/assets/images/1920/work/step4/arr3.png" alt="" />
            </div>
          </div>
          <div class="posi posi7" :class="imgAni7 ? 'ani' : ''">
            <div class="img img4">
              <img src="~@/assets/images/1920/work/step4/img4.png" alt="" />
            </div>
            <div class="text flex-box-between">
              <p>分类器设计</p>
              <p>特征抽取</p>
            </div>
          </div>
          <div class="posi posi8" :class="imgAni8 ? 'ani' : ''">
            <div class="text">根据特征分类</div>
            <div class="arr arr4">
              <img src="~@/assets/images/1920/work/step4/arr4.png" alt="" />
            </div>
          </div>
          <div class="posi posi9" :class="imgAni9 ? 'ani' : ''">
            <div class="img img5">
              <img src="~@/assets/images/1920/work/step4/img5.png" alt="" />
            </div>
            <div class="text">分类模型</div>
          </div>
        </div>
        <div class="imgList" v-show="showImgList">
          <div class="son flex-box" :class="img1Ani ? 'ani' : ''">
            <div class="name">老虎</div>
            <div class="img">
              <img src="~@/assets/images/1920/work/step4/ten_1.png" alt="" />
            </div>
          </div>
          <div class="son flex-box" :class="img2Ani ? 'ani' : ''">
            <div class="name">熊猫</div>
            <div class="img">
              <img src="~@/assets/images/1920/work/step4/ten_2.png" alt="" />
            </div>
          </div>
          <div class="son flex-box" :class="img3Ani ? 'ani' : ''">
            <div class="name">鸟</div>
            <div class="img">
              <img src="~@/assets/images/1920/work/step4/ten_3.png" alt="" />
            </div>
          </div>
          <div class="son flex-box" :class="img4Ani ? 'ani' : ''">
            <div class="name">猫</div>
            <div class="img">
              <img src="~@/assets/images/1920/work/step4/ten_4.png" alt="" />
            </div>
          </div>
          <div class="son flex-box" :class="img5Ani ? 'ani' : ''">
            <div class="name">鹿</div>
            <div class="img">
              <img src="~@/assets/images/1920/work/step4/ten_5.png" alt="" />
            </div>
          </div>
          <div class="son flex-box" :class="img6Ani ? 'ani' : ''">
            <div class="name">狗</div>
            <div class="img">
              <img src="~@/assets/images/1920/work/step4/ten_6.png" alt="" />
            </div>
          </div>
          <div class="son flex-box" :class="img7Ani ? 'ani' : ''">
            <div class="name">青蛙</div>
            <div class="img">
              <img src="~@/assets/images/1920/work/step4/ten_7.png" alt="" />
            </div>
          </div>
          <div class="son flex-box" :class="img8Ani ? 'ani' : ''">
            <div class="name">马</div>
            <div class="img">
              <img src="~@/assets/images/1920/work/step4/ten_8.png" alt="" />
            </div>
          </div>
          <div class="son flex-box" :class="img9Ani ? 'ani' : ''">
            <div class="name">豹</div>
            <div class="img">
              <img src="~@/assets/images/1920/work/step4/ten_9.png" alt="" />
            </div>
          </div>
          <div class="son flex-box" :class="img10Ani ? 'ani' : ''">
            <div class="name">长颈鹿</div>
            <div class="img">
              <img src="~@/assets/images/1920/work/step4/ten_10.png" alt="" />
            </div>
          </div>
        </div>
        <div
          class="lineCore flex-box"
          v-show="!showImgList"
          :class="lineBoxAni ? 'ani' : ''"
        >
          <div class="text">模型训练</div>
          <div class="line">
            <div class="inner" :style="{ width: `${percent}%` }"></div>
          </div>
        </div>
        <div
          class="fourCore flex-box-between"
          :class="fourCoreAni1 ? 'ani' : ''"
        >
          <div class="son">
            <div class="t1">{{ num1 }}<span>%</span></div>
            <div class="t2">GPU利用率</div>
          </div>
          <div class="son">
            <div class="t1">{{ num2 }}<span>%</span></div>
            <div class="t2">CPU利用率</div>
          </div>
          <div class="son">
            <div class="t1">{{ num3 }}<span>%</span></div>
            <div class="t2">内存利用率</div>
          </div>
          <div class="son">
            <div class="t1">{{ num4 }}<span>%</span></div>
            <div class="t2">磁盘利用率</div>
          </div>
        </div>
        <div
          class="changeBox flex-box-center"
          @click="showImgList = !showImgList"
          :class="showButton ? 'ani' : ''"
        >
          <div class="text">
            <span>{{ job1SelectRegion }}</span>
          </div>
          <div class="icon">
            <img src="~@/assets/images/page4-1/change.png" alt="" />
          </div>
        </div>
      </div>
      <div class="item" v-show="tsShow">
        <div class="title">模型训练输出等待中</div>
        <div class="circleBox" :class="lineBoxAni ? 'ani' : ''">
          <circle-progressbar
            style="overflow-y: hidden"
            barColor="rgba(20, 94, 165, 1)"
            backgroundColor="rgba(51,51,51,1)"
            :radius="15"
            :width="170"
            :progress="percent1_1"
            :isAnimation="true"
          ></circle-progressbar>
          <div class="per">{{ percent1_1 }}<span>%</span></div>
          <div class="text">处理中</div>
        </div>
      </div>
      <div class="item" v-show="!tsShow">
        <div class="title">模型训练数据打包</div>
        <div class="map" ref="chinamap"></div>
        <div class="textposi">{{ speed }}ms</div>
        <!-- <div class="tsCore">
          <div class="top">
            <div class="img">
              <img class="img1 ani" src="~@/assets/images/1920/work/step4/rt-img-1.png" alt="">
              <img class="img2" src="~@/assets/images/1920/work/step4/rt-img-2.png" alt="">
            </div>
            <div class="text">互联组CG001(CN2)</div>
          </div>
          <div class="line l1" :class="heightAni ? 'ani' : ''"></div>
          <div class="line l2" :class="heightAni ? 'ani' : ''"></div>
          <div class="down flex-box-center">
            <div class="son">
              <div class="icon"><img src="~@/assets/images/1920/work/step4/rt-img-3.png" alt=""></div>
              <div class="text">
                <p>智算作业</p>
                <p>{{ job1SelectRegion }}</p>
              </div>
            </div>
            <div class="cc">
              <div class="img" :class="opaAni ? 'ani' : ''"><img src="~@/assets/images/1920/work/step4/rt-img-4.png"
                  alt=""></div>
              <div class="text">{{numnum}}MB/s</div>
            </div>
            <div class="son">
              <div class="icon"><img src="~@/assets/images/1920/work/step4/rt-img-3.png" alt=""></div>
              <div class="text">
                <p>通算作业</p>
                <p>{{ job2SelectRegion }}</p>
              </div>
            </div>
          </div>
        </div> -->
        <div class="lineCore flex-box" :class="lineAni2 ? 'ani' : ''">
          <div class="text">图像应用打包</div>
          <div class="line on">
            <div class="inner" :style="{ width: `${percent2}%` }"></div>
          </div>
        </div>
        <div
          class="fourCore flex-box-between"
          :class="fourCoreAni2 ? 'ani' : ''"
        >
          <div class="son">
            <div class="t1">{{ num5 }}<span>%</span></div>
            <div class="t2">GPU利用率</div>
          </div>
          <div class="son">
            <div class="t1">{{ num6 }}<span>%</span></div>
            <div class="t2">内存利用率</div>
          </div>
          <div class="son">
            <div class="t1">{{ num7 }}<span>%</span></div>
            <div class="t2">磁盘利用率</div>
          </div>
          <div class="son">
            <div class="t1">{{ num8 }}<span>s</span></div>
            <div class="t2">打包时间</div>
          </div>
        </div>
        <div class="changeBox flex-box-center" :class="showButton ? 'ani' : ''">
          <div class="text">
            <span>{{ job2SelectRegion }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getStore, setStore } from "@/common/util";
import { tree } from "d3";
import circleProgressbar from "vue-circleprogressbar";
import * as echarts from "echarts";
import chinaMap from "@/assets/json/geoJson.json";

export default {
  name: "workstep2",
  components: {
    circleProgressbar,
  },
  props: {
    job1SelectRegion: {
      type: String,
      default: "华为云-上海一",
    },
    job1SelectType: {
      type: String,
      default: "智能推荐",
    },
    job2SelectRegion: {
      type: String,
      default: "天翼云-苏州(合营)",
    },
    job2SelectType: {
      type: String,
      default: "智能推荐",
    },
  },
  data() {
    return {
      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      wordAni4: false,
      wordAni5: false,
      imgAni1: false,
      imgAni2: false,
      imgAni3: false,
      imgAni4: false,
      imgAni5: false,
      imgAni6: false,
      imgAni7: false,
      imgAni8: false,
      imgAni9: false,
      rightAni: false,
      num1: 80,
      num2: 76,
      num3: 68,
      num4: 73,
      num5: 92,
      num6: 86,
      num7: 61,
      num8: 0,
      timer: null,
      timer2: null,
      timer3: null,
      timer4: null,
      lineBoxAni: null,
      percent: 0,
      percent2: 0,
      timerpercent: null,
      timerpercent2: null,
      timerpercent3: null,
      img1Ani: false,
      img2Ani: false,
      img3Ani: false,
      img4Ani: false,
      img5Ani: false,
      img6Ani: false,
      img7Ani: false,
      img8Ani: false,
      img9Ani: false,
      img10Ani: false,
      showImgList: false,
      showButton: false,
      statusText: "作业执行中",
      isAuto: null,
      iconAni1: true,
      iconAni2: false,
      iconAni3: true,
      iconAni4: false,
      iconAni5: true,
      iconAni6: false,
      word1: "模型训练中",
      progress: 40,
      tsShow: true,
      fourCoreAni1: false,
      fourCoreAni2: false,
      lineAni2: false,
      percent1_1: 0,
      roundAni: false,
      heightAni: false,
      opaAni: false,
      numnum: 20,
      speed: 43,
      timerspeed: null,
    };
  },
  created() {
    echarts.registerMap("china", { geoJSON: chinaMap });
    this.isAuto = this.GLOBAL.isAuto;
  },
  mounted() {
    this.init();
    this.speedRandom();
  },
  methods: {
    speedRandom() {
      this.timerspeed = setInterval(() => {
        this.speed = parseInt(Math.random() * (14 - 11) + 11);
      }, 1000);
      this.GLOBAL.timerArraySet.push(this.timerspeed);
    },
    lineAni() {
      this.timerpercent = setInterval(() => {
        this.percent += 1;
        if (this.percent >= 100) {
          clearInterval(this.timerpercent);
        }
      }, 60);
    },
    lineAniFun2() {
      this.timerpercent2 = setInterval(() => {
        this.percent2 += 1;
        if (this.percent2 >= 100) {
          clearInterval(this.timerpercent2);
        }
      }, 60);
    },
    lineAniFun1_1() {
      this.timerpercent3 = setInterval(() => {
        this.percent1_1 += 1;
        if (this.percent1_1 >= 100) {
          clearInterval(this.timerpercent3);
          this.tsShow = false;
          this.initMap();
          this.fourCoreAni2 = true;
          this.numRandom2();
        }
      }, 60);
    },
    numRandom() {
      this.timer = setInterval(() => {
        this.num1 = parseInt(Math.random() * (85 - 75) + 75);
        this.num2 = parseInt(Math.random() * (80 - 70) + 70);
        this.num3 = parseInt(Math.random() * (75 - 65) + 65);
        this.num4 = parseInt(Math.random() * (75 - 70) + 70);
      }, 800);
      this.timer2 = setInterval(() => {
        this.num5 = parseInt(Math.random() * (95 - 85) + 85);
        this.num6 = parseInt(Math.random() * (80 - 70) + 70);
        this.num7 = parseInt(Math.random() * (65 - 55) + 55);
      }, 800);
    },
    numRandom2() {
      let _this = this;
      this.timer3 = setInterval(() => {
        this.num8 += 1;
        if (this.num8 >= 60) {
          this.num8 = 60;
          clearInterval(_this.timer3);
        }
      }, 160);
    },
    numRandom3() {
      this.timer4 = setInterval(() => {
        this.numnum = parseInt(Math.random() * (25 - 15) + 15);
      }, 800);
    },
    initMap() {
      let points = [
        {
          value: [121.48054, 31.23593],
          name: "上海",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [113.13947, 41.00075],
          name: "乌兰察布",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [114.89257, 40.77324],
          name: "张家口",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [113.27143, 23.83534],
          name: "广州",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [106.63658, 26.65332],
          name: "贵阳",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [111.70085, 40.82298],
          name: "内蒙",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [120.586, 31.297301],
          name: "苏州",
          itemStyle: { color: "#21D571" },
        },
      ];
      let chooseLngLat = [];
      let chooseLngLat2 = [];
      let points2 = [
        {
          value: [116.41339, 39.91092],
          name: "北京",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [119.41942, 32.70068],
          name: "扬州",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [121.48054, 31.23593],
          name: "上海",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [103.04954, 31.01679],
          name: "雅安",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [106.55844, 29.569],
          name: "重庆",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [106.50192, 26.4421],
          name: "贵安",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [118.09643, 24.78541],
          name: "厦门",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [113.27143, 23.83534],
          name: "广州",
          itemStyle: { color: "#158EFF" },
        },
        {
          value: [114.18732, 22.24966],
          name: "香港",
          itemStyle: { color: "#158EFF" },
        },
      ];
      let list = points.filter(
        (item) => this.job1SelectRegion.indexOf(item.name) > -1
      );
      let list2 = points.filter(
        (item) => this.job2SelectRegion.indexOf(item.name) > -1
      );
      let newlist = list.concat(list2);
      points = points.concat(list);
      chooseLngLat = list[0].value || [121.48054, 31.23593];
      chooseLngLat2 = list2[0].value || [120.586, 31.297301];
      const zoom = 10;
      const center = [120.586, 31.297301];
      let option = {
        backgroundColor: "",
        geo: {
          map: "china",
          aspectScale: 0.72, //长宽比
          zoom: zoom,
          center: center,
          roam: false,
          label: {
            show: false,
          },
          emphasis: {
            disabled: false,
            itemStyle: {
              normal: {
                areaColor: {
                  type: "radial",
                  x: 0.3,
                  y: 0.3,
                  r: 0.8,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#09132c", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#274d68", // 100% 处的颜色
                    },
                  ],
                  globalCoord: true, // 缺省为 false
                },
                shadowColor: "#618198",
                shadowOffsetX: 5,
                shadowOffsetY: 5,
              },
            },
          },
          itemStyle: {
            normal: {
              areaColor: {
                type: "radial",
                x: 0.3,
                y: 0.3,
                r: 0.8,
                colorStops: [
                  {
                    offset: 0,
                    color: "#09132c", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#274d68", // 100% 处的颜色
                  },
                ],
                globalCoord: true, // 缺省为 false
              },
              shadowColor: "#618198",
              shadowOffsetX: 5,
              shadowOffsetY: 5,
            },
          },
          regions: [
            {
              name: "南海诸岛",
              itemStyle: {
                areaColor: "rgba(0, 10, 52, 1)",
                borderColor: "rgba(0, 10, 52, 1)",
                normal: {
                  opacity: 0,
                  label: {
                    show: false,
                    color: "#009cc9",
                  },
                },
              },
            },
          ],
        },
        series: [
          {
            type: "map",
            roam: false,
            label: {
              normal: {
                show: false,
                textStyle: {
                  color: "#888",
                },
              },
              emphasis: {
                show: false,
                disabled: false,
                textStyle: {
                  color: "rgb(183,185,14)",
                },
              },
            },
            selectedMode: false,
            emphasis: {
              disabled: true,
            },

            itemStyle: {
              normal: {
                borderColor: "rgb(140, 140, 140)",
                borderWidth: 1,
                areaColor: {
                  type: "radial",
                  x: 0.5,
                  y: 0.5,
                  r: 0.8,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#09132c", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#274d68", // 100% 处的颜色
                    },
                  ],
                  globalCoord: true, // 缺省为 false
                },
              },
              emphasis: {
                show: false,
                disabled: true,
                // areaColor: 'rgb(46,229,206)',
                //    shadowColor: 'rgb(12,25,50)',
                borderWidth: 0.1,
              },
            },
            zoom: zoom,
            center: center,
            //     roam: false,
            map: "china", //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
          },
          {
            type: "effectScatter",
            coordinateSystem: "geo",
            showEffectOn: "render",
            zlevel: 1,
            rippleEffect: {
              number: 1,
              period: 1,
              scale: 3,
              brushType: "fill",
            },
            hoverAnimation: false,
            label: {
              show: true,
              formatter: "{b}",
              position: "bottom",
              offset: [0, 5],
              color: "#ffffff",
            },
            itemStyle: {
              color: "#1DE9B6",
              shadowBlur: 2,
              shadowColor: "#333",
            },
            symbolSize: 8,
            data: newlist,
          },
          //地图线的动画效果
          {
            type: "lines",
            zlevel: 2,
            polyline: false,
            effect: {
              show: true,
              period: 2, //箭头指向速度，值越小速度越快
              trailLength: 0.4, //特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: "arrow", //箭头图标
              symbolSize: 5, //图标大小
              roundTrip: true,
            },
            lineStyle: {
              color: "#158EFF",
              type: "dashed",
              width: 1, //线条宽度
              opacity: 0.1, //尾迹线条透明度
              curveness: -0.3, //尾迹线条曲直度
            },
            label: {
              show: true,
              position: "end",
              formatter: "{@val}",
              color: "#158EFF",
              fontSize: 16,
            },
            data: [{ coords: [chooseLngLat, chooseLngLat2], val: "10ms" }],
          },
        ],
      };
      if (!this.mapChart) {
        this.mapChart = echarts.init(this.$refs.chinamap, null, {
          width: this.GLOBAL.relPx(430),
          height: this.GLOBAL.relPx(310),
        });
        this.GLOBAL.echartsDomArray.push(this.mapChart);
      }
      this.mapChart.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.mapChart.setOption(option);
    },
    init() {
      let settimer1 = setTimeout(() => {
        this.wordAni1 = true;
      }, 400);
      let settimer2 = setTimeout(() => {
        this.wordAni2 = true;
      }, 800);
      let settimer3 = setTimeout(() => {
        this.imgAni1 = true;
        this.fourCoreAni1 = true;
        this.showButton = true;
        this.lineBoxAni = true;
        this.lineAni();
        this.numRandom();
        this.lineAniFun1_1();
      }, 1200);
      let settimer4 = setTimeout(() => {
        this.imgAni2 = true;
      }, 1600);
      let settimer5 = setTimeout(() => {
        this.imgAni3 = true;
      }, 2400);
      let settimer6 = setTimeout(() => {
        this.imgAni4 = true;
      }, 3200);
      let settimer7 = setTimeout(() => {
        this.imgAni5 = true;
      }, 4000);
      let settimer8 = setTimeout(() => {
        this.imgAni6 = true;
      }, 4800);
      let settimer9 = setTimeout(() => {
        this.imgAni7 = true;
      }, 5600);
      let settimer10 = setTimeout(() => {
        this.imgAni8 = true;
      }, 6400);
      let settimer11 = setTimeout(() => {
        this.imgAni9 = true;
      }, 7200);
      let settimer12 = setTimeout(() => {
        this.showImgList = true;
        this.img1Ani = true;
        this.iconAni1 = false;
        this.iconAni2 = true;
        this.lineAni2 = true;
        this.lineAniFun2();
      }, 8000);
      let settimer13 = setTimeout(() => {
        this.img2Ani = true;
      }, 8800);
      let settimer14 = setTimeout(() => {
        this.img3Ani = true;
        this.numRandom3();
        this.wordAni3 = true;
        this.wordAni4 = true;
      }, 9600);
      let settimer15 = setTimeout(() => {
        this.img4Ani = true;
      }, 10400);
      let settimer16 = setTimeout(() => {
        this.img5Ani = true;
        this.wordAni5 = true;
      }, 11200);
      let settimer17 = setTimeout(() => {
        this.img6Ani = true;
        this.iconAni3 = false;
        this.iconAni4 = true;
      }, 12000);
      let settimer18 = setTimeout(() => {
        this.img7Ani = true;
      }, 12800);
      let settimer19 = setTimeout(() => {
        this.img8Ani = true;
        this.iconAni5 = false;
        this.iconAni6 = true;
        this.$emit("stepEnd", 4);
      }, 13600);
      let settimer20 = setTimeout(() => {
        this.img9Ani = true;
      }, 14400);
      let settimer21 = setTimeout(() => {
        this.img10Ani = true;
      }, 15200);
      let settimer22 = setTimeout(() => {}, 16000);
      let settimer23 = setTimeout(() => {
        this.heightAni = true;
      }, 16800);
      let settimer24 = setTimeout(() => {
        this.opaAni = true;
      }, 17600);
      let settimer25 = setTimeout(() => {}, 18400);
      let settimer26 = setTimeout(() => {}, 19200);
      let settimer27 = setTimeout(() => {}, 20000);

      this.$once("hook:beforeDestroy", () => {
        clearTimeout(settimer1);
        settimer1 = null;
        clearTimeout(settimer2);
        settimer2 = null;
        clearTimeout(settimer3);
        settimer3 = null;
        clearTimeout(settimer4);
        settimer4 = null;
        clearTimeout(settimer5);
        settimer5 = null;
        clearTimeout(settimer6);
        settimer6 = null;
        clearTimeout(settimer7);
        settimer7 = null;
        clearTimeout(settimer8);
        settimer8 = null;
        clearTimeout(settimer9);
        settimer9 = null;
        clearTimeout(settimer10);
        settimer10 = null;
        clearTimeout(settimer11);
        settimer11 = null;
        clearTimeout(settimer12);
        settimer12 = null;
        clearTimeout(settimer13);
        settimer13 = null;
        clearTimeout(settimer14);
        settimer14 = null;
        clearTimeout(settimer15);
        settimer15 = null;
        clearTimeout(settimer16);
        settimer16 = null;
        clearTimeout(settimer17);
        settimer17 = null;
        clearTimeout(settimer18);
        settimer18 = null;
        clearTimeout(settimer19);
        settimer19 = null;
        clearTimeout(settimer20);
        settimer20 = null;
        clearTimeout(settimer21);
        settimer21 = null;
        clearTimeout(settimer22);
        settimer22 = null;
        clearTimeout(settimer23);
        settimer23 = null;
        clearTimeout(settimer24);
        settimer24 = null;
        clearTimeout(settimer25);
        settimer25 = null;
        clearTimeout(settimer26);
        settimer26 = null;
        clearTimeout(settimer27);
        settimer27 = null;
      });
    },
    endFun(index) {
      if (this.isAuto) {
        return false;
      }
      this.$emit("endFun", index);
    },
    resetFun() {
      if (this.timerpercent) {
        clearInterval(this.timerpercent);
      }
      if (this.timerpercent2) {
        clearInterval(this.timerpercent2);
      }
      if (this.timerpercent3) {
        clearInterval(this.timerpercent3);
      }
      if (this.timer) {
        clearInterval(this.timer);
      }
      if (this.timer2) {
        clearInterval(this.timer2);
      }
      if (this.timer3) {
        clearInterval(this.timer3);
      }
      if (this.timer4) {
        clearInterval(this.timer4);
      }
    },
  },
  beforeDestroy() {
    this.resetFun();
  },
};
</script>

<style lang="scss">
.pageWork3 {
  .rightBox {
    width: 1204px;
    box-sizing: border-box;

    .item {
      width: 578px;
      height: 793px;
      background: rgba($color: #26262a, $alpha: 0.15);
      border-radius: 4px;
      box-sizing: border-box;
      position: relative;

      .title {
        font-size: 18px;
        padding: 13px;
      }

      .map {
        width: 430px;
        margin: 0 auto;
      }

      .textposi {
        font-size: 16px;
        color: #158eff;
        position: absolute;
        top: 200px;
        right: 170px;
        z-index: 1;
      }

      .tsCore {
        margin-top: 43px;
        height: 270px;
        font-size: 16px;
        text-align: center;
        position: relative;

        .top {
          .img {
            position: relative;
            margin: 0 auto;
            width: 65px;
            height: 65px;
            padding-top: 17px;
            box-sizing: border-box;

            .img1 {
              width: 65px;
              height: 65px;
              position: absolute;
              top: 0;
              left: 0;

              &.ani {
                animation: rotateAni 3s linear infinite forwards;
              }
            }

            .img2 {
              width: 34px;
              height: 29px;
              margin: 0 auto;
              display: block;
            }
          }

          .text {
            margin-top: 10px;
          }
        }

        .line {
          position: absolute;
          z-index: 0;
          width: 150px;
          height: 0;
          background-size: cover;
          top: 20px;

          &.l1 {
            background-image: url("~@/assets/images/1920/work/step4/left.png");
            left: 100px;
          }

          &.l2 {
            background-image: url("~@/assets/images/1920/work/step4/right.png");
            right: 100px;
          }

          &.ani {
            animation: heightAni 0.5s linear 1 forwards;
          }
        }

        .down {
          margin-top: 50px;
          text-align: center;

          .son {
            position: relative;

            .text {
              position: absolute;
              top: 70px;
              left: -30px;
              width: 140px;

              p {
                margin: 0;
              }

              &.on {
                right: 0;
              }
            }
          }

          .cc {
            margin: 13px 5px 20px 5px;

            .img {
              &.ani {
                animation: imgOpacity 2s linear infinite forwards;
              }
            }

            .text {
              width: 100%;
            }
          }
        }
      }

      .imgCore {
        position: relative;
        margin-top: 43px;
        margin-left: 20px;
        height: 270px;

        .posi {
          position: absolute;
          z-index: 1;
          bottom: 0;
          right: 0;
          opacity: 0;

          &.ani {
            animation: imgOpacity 0.5s linear 1 forwards;
          }

          &.posi1 {
            width: 100px;
            top: 0;
            left: 0;

            .text {
              margin-bottom: 14px;
            }
          }

          &.posi2 {
            width: 86px;
            top: 38px;
            left: 115px;

            .text {
              margin-bottom: 5px;
            }
          }

          &.posi3 {
            width: 139px;
            top: 0px;
            left: 215px;

            .text {
              margin-bottom: 14px;
            }
          }

          &.posi4 {
            width: 148px;
            top: 38px;
            left: 378px;

            .text {
              margin-bottom: 14px;
            }
          }

          &.posi5 {
            width: 250px;
            top: 115px;
            left: 285px;

            .text {
              line-height: 56px;
              margin-right: 15px;
            }
          }

          &.posi6 {
            width: 146px;
            top: 160px;
            left: 380px;

            .text {
              transform: translateY(30px);
            }
          }

          &.posi7 {
            width: 165px;
            top: 185px;
            left: 200px;

            .text {
              margin-top: 10px;

              p {
                margin: 0;
              }
            }
          }

          &.posi8 {
            width: 100px;
            top: 175px;
            left: 110px;

            .text {
              margin-bottom: 10px;
            }
          }

          &.posi9 {
            width: 67px;
            top: 170px;
            left: 10px;

            .text {
              margin-top: 14px;
            }
          }
        }

        .text {
          font-size: 16px;
          text-align: center;
          line-height: 16px;
        }

        .img {
          img {
            width: 100%;
          }

          &.img1 {
            width: 100px;
            height: 62px;
          }

          &.img2 {
            width: 139px;
            height: 64px;
          }

          &.img3 {
            width: 136px;
            height: 56px;
          }

          &.img4 {
            width: 165px;
            height: 69px;
          }

          &.img5 {
            width: 67px;
            height: 66px;
          }
        }

        .arr {
          img {
            width: 100%;
          }

          &.arr1 {
            width: 86px;
            height: 12px;
          }

          &.arr2 {
            width: 148px;
            height: 41px;
          }

          &.arr3 {
            width: 146px;
            height: 46px;
          }

          &.arr4 {
            width: 83px;
            height: 10px;
          }
        }
      }

      .lineCore {
        margin-top: 50px;
        margin-left: 26px;
        opacity: 0;

        .text {
          font-size: 16px;
          line-height: 16px;
        }

        .line {
          width: 450px;
          height: 10px;
          background: rgba(17, 17, 17, 1);
          border-radius: 5px;
          margin: 0 auto;
          margin-top: 3px;
          margin-left: 10px;

          .inner {
            background-image: url("~@/assets/images/page4/step4/percent.png");
            width: 0%;
            height: 10px;
            border-radius: 5px;
          }

          &.on {
            width: 400px;
          }
        }

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }

      .fourCore {
        background-image: url("~@/assets/images/1920/work/step4/bg.png");
        background-size: cover;
        width: 577px;
        height: 247px;
        margin-top: 50px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }

        .son {
          width: 50%;
          height: 50%;
          text-align: center;

          .t1 {
            font-size: 34px;
            line-height: 34px;
            padding-top: 30px;

            span {
              font-size: 16px;
            }
          }

          .t2 {
            font-size: 16px;
          }
        }
      }

      .changeBox {
        cursor: pointer;
        opacity: 0;
        margin-top: 20px;

        .icon {
          width: 20px;
          height: 20px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .text {
          font-size: 18px;
          padding-right: 10px;
          line-height: 20px;
        }

        &.ani {
          animation: imgOpacity 0.5s linear 1 forwards;
        }
      }
    }
  }

  .circleBox {
    width: 170px;
    height: 170px;
    position: relative;
    margin: 0 auto;
    margin-top: 260px;
    opacity: 0;

    &.ani {
      animation: imgOpacity 0.5s linear 1 forwards;
    }

    .center_text {
      display: none;
    }

    .per {
      font-size: 50px;
      text-align: center;
      width: 100%;
      line-height: 170px;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 9;
      color: #fff;

      span {
        font-size: 24px;
      }
    }

    .text {
      font-size: 20px;
      text-align: center;
      margin-top: 20px;
    }
  }

  .imgList {
    position: relative;
    margin-top: 43px;
    margin-left: 10px;
    height: 335px;

    .son {
      margin-bottom: 5px;
      opacity: 0;

      .name {
        width: 100px;
        line-height: 33px;
        text-align: right;
        margin-right: 40px;
      }

      .img {
        width: 361px;
        height: 33px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      &.ani {
        animation: imgOpacity 0.5s linear 1 forwards;
      }
    }
  }
}

@keyframes imgOpacity {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes rotateAni {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes heightAni {
  0% {
    height: 0;
  }

  100% {
    height: 120px;
  }
}
</style>
