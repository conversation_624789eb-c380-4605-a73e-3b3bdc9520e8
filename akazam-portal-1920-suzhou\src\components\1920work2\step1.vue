<template>
  <div class="pageWork1_2 flex-box-between">
    <div class="leftStep">
      <div class="stepList work2 flex-box">
        <div class="item on al" @click="endFun(1)">需求输入</div>
        <div class="arraw on al"></div>
        <div class="item on flex-box-center">
          <div class="icon"></div>资源池分配
        </div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(3)">提交作业</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(4)">渲染</div>
      </div>
      <div>
        <div class="wordList">
          <div class="wordListItem">
            <p :class="wordAni1 ? 'ani' : ''">应用作业：3D应用-离线渲染</p>
            <p :class="wordAni2 ? 'ani' : ''" class="flex-box">
              <i v-if="iconAni1" class="el-icon-loading"></i>
              <img v-if="iconAni2" src="~@/assets/images/page4/step1/success.png" alt="">
              {{ word1 }}
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="rightBox flex-box-between">
      <div class="centerCore" :class="centerOpa ? 'opa' : ''">
        <div class="topList" v-show="(!showWorkSecond || isFinished) && !showWork2">
          <div v-show="!showWorkSecond || (isFinished && showWork1)">
            <div class="item flex-box-between" :class="ccAni1 ? 'ani' : ''">
              <div class="leftIcon flex-box">
                <div class="icon"><img src="~@/assets/images/page4/step1/c-icon-1.png" alt=""></div>
                <div class="text">资源需求</div>
              </div>
              <div class="rightValBox">
                <div class="cc cc1">
                  <div class="svg" id="svg1"></div>
                  <div class="smallItemList flex-box-between">
                    <div class="smallItem" :class="cwordAni1 ? 'on' : ''">CPU{{ propsdata.input2 }}核</div>
                    <div class="smallItem" :class="cwordAni2 ? 'on' : ''">内存{{ propsdata.input3 }}G</div>
                    <div class="smallItem" :class="cwordAni3 ? 'on' : ''">GPU型号{{ propsdata.input4 }}</div>
                    <div class="smallItem" :class="cwordAni4 ? 'on' : ''">{{ propsdata.input6 }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="leftLine len1" :class="ccAni2 ? 'ani' : ''"></div>
            <div class="item flex-box-between" :class="ccAni2 ? 'ani' : ''">
              <div class="leftIcon flex-box">
                <div class="icon"><img src="~@/assets/images/page4/step1/c-icon-2.png" alt=""></div>
                <div class="text">分配策略</div>
              </div>
              <div class="rightValBox">
                <div class="cc cc2">
                  <div class="sixItem flex-box-between">
                    <div class="sixItemSon" :class="propsdata.strategy == '智能推荐' ? 'on' : ''">
                      <div class="text">智能推荐</div>
                      <div class="point"></div>
                    </div>
                    <div class="sixItemSon" :class="propsdata.strategy == '效率优先' ? 'on' : ''">
                      <div class="text">效率优先</div>
                      <div class="point"></div>
                    </div>
                    <div class="sixItemSon" :class="propsdata.strategy == '价格优先' ? 'on' : ''">
                      <div class="text">价格优先</div>
                      <div class="point"></div>
                    </div>
                  </div>
                  <div class="roomList" :class="roomAni1 ? 'ani' : ''">
                    <div class="roomitem flex-box-between ani" v-for="(item, index) in propsdata.strategyList"
                      :key="index" v-show="item.strategy.indexOf(propsdata.strategy) > -1"
                      :class="item.name == propsdata.businesscity ? 'on' : ''">
                      <div class="le">
                        <div class="text">{{ propsdata.input4 }}</div>
                        <div class="line"></div>
                        <div class="text">{{ item.name }}</div>
                      </div>
                      <div class="center">{{ item.spec }}</div>
                      <div class="rt">
                        <div class="text">{{ item.desc }}</div>
                        <div class="line"></div>
                        <div class="text">{{ item.price }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="leftLine len3" :class="ccAni3 ? 'ani' : ''"></div>
          </div>
          <div class="item flex-box-between" :class="ccAni3 ? 'ani' : ''">
            <div class="leftIcon flex-box">
              <div class="icon"><img src="~@/assets/images/page4/step1/c-icon-4.png" alt=""></div>
              <div class="text">选择资源池</div>
              <div class="arrow" :class="showWork1 ? 'on' : ''" v-if="isFinished && showWork1" @click="openDown(1)"></div>
            </div>
            <div class="rightValBox">
              <div class="cc cc4">
                <div class="flex-box">
                  <div class="left">
                    <p class="ss">{{ propsdata.businesscity }}</p>
                  </div>
                  <div class="right">
                    <span>{{ propsdata.input4 }}</span><span>{{ propsdata.input2 }}核{{ propsdata.input3
                    }}G</span>
                  </div>
                </div>
                <div class="usepercent flex-box-between">
                  <div class="useitem">
                    <round-common :value="propsdata.otherNow.cpu"></round-common>
                    <div class="useitemtext">CPU使用率</div>
                  </div>
                  <div class="useitem">
                    <round-common :value="propsdata.otherNow.gpu"></round-common>
                    <div class="useitemtext">GPU使用率</div>
                  </div>
                  <div class="useitem">
                    <round-common :value="propsdata.otherNow.ram"></round-common>
                    <div class="useitemtext">内存使用率</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="rightCore" :class="rightAni ? 'ani' : ''">
        <div class="top">
          <div class="title">{{ propsdata.businesscity }}</div>
          <div class="anchorBox">
            <div class="anchor" ref="anchor"></div>
            <div class="core">
              <div class="text1">已使用</div>
              <div class="text2">{{ propsdata.otherNow.use }}G/{{ propsdata.otherNow.max }}G</div>
            </div>
          </div>
          <div class="fourCore flex-box-between" :class="fourCoreAni1 ? 'ani' : ''">
            <div class="son">
              <div class="t1">
                {{ propsdata.otherNow.num1 }}
              </div>
              <div class="t2">正在渲染作业</div>
            </div>
            <div class="son">
              <div class="t1">
                {{ propsdata.otherNow.num2 }}
              </div>
              <div class="t2">等待渲染作业</div>
            </div>
            <div class="son">
              <div class="t1">
                {{ propsdata.otherNow.num3 }}
              </div>
              <div class="t2">已完成作业</div>
            </div>
            <div class="son">
              <div class="t1">
                {{ propsdata.otherNow.num4 }}
              </div>
              <div class="t2">失败作业</div>
            </div>
          </div>
        </div>
        <div class="down">
          <div class="title">推荐资源池的水位</div>
          <div class="usepercent flex-box-between">
            <div class="useitem" v-for="(item, index) in propsdata.otherTwo" :key="index"  @click="otherTwoIndexFun(index)">
              <round-common :value="item.gpu"></round-common>
              <div class="useitemtext">GPU</div>
              <div class="useitemtext">{{ item.name }}</div>
              <div class="roundPosi flex-box-center" v-show="index === otherTwoIndex">
                <div class="posiSon">
                  <div class="tp">{{ item.cpu }}<span>%</span></div>
                  <div class="dn">CPU</div>
                </div>
                <div class="line"></div>
                <div class="posiSon">
                  <div class="tp">{{ item.ram }}<span>%</span></div>
                  <div class="dn">内存</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getStore, setStore } from "@/common/util";
import VueMatrixRaindrop2 from '../vueMatrixDigitRain/index2.vue';
import * as d3 from 'd3' // d3
import roundCommon from '../round/Index'
import * as echarts from 'echarts';

export default {
  name: 'workstep1',
  components: {
    VueMatrixRaindrop2,
    roundCommon
  },
  props: {
    propsdata: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      list1: [],
      list2: [],
      list3: [],
      point: [],
      percent: 0,
      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      wordAni4: false,
      wordAni5: false,
      wordAni6: false,
      iconAni1: true,
      iconAni2: false,
      iconAni3: true,
      iconAni4: false,
      showCode: false,
      timer: null,
      itemAni1: false,
      itemAni2: false,
      percent: 0,
      percent2: 0,
      percentdesc: '上传训练数据集...',
      percentdesc2: '作业入池中...',
      percentdesc3: '作业入池中...',
      wordBottomAni: false,
      imgItemAni: false,
      imgItemAni2: false,
      time1: 0,
      time2: 0,
      time1Timer: null,
      time2Timer: null,
      timeDate: '',
      imgLeftAni: false,
      imgRightAni: false,
      ponit1Index: null,
      ponit2Index: null,
      pointAnitimer1: null,
      pointAnitimer2: null,
      wordBottomAni2: null,
      isAuto: null,
      centerOpa: false,
      rightOpa: false,

      cwordAni1: false,
      cwordAni2: false,
      cwordAni3: false,
      cwordAni4: false,
      cwordAni5: false,
      ccAni1: false,
      ccAni2: false,
      ccAni3: false,
      ccAni4: false,
      sixItemSonAni1: false,
      sixItemSonAni2: false,
      sixItemSonAni3: false,
      sixItemSonAni4: false,
      sixItemSonAni5: false,
      sixItemSonAni6: false,
      rightValBoxitem1: false,
      rightValBoxitem2: false,
      rightValBoxitem3: false,
      rightValBoxitem4: false,
      rightValBoxitem5: false,
      rightValBoxitem6: false,
      rightValBoxitem7: false,
      rightValBoxitem8: false,
      rightValBoxitemChoose: false,

      cwordAni1_2: false,
      cwordAni2_2: false,
      cwordAni3_2: false,
      cwordAni4_2: false,
      cwordAni5_2: false,
      ccAni1_2: false,
      ccAni2_2: false,
      ccAni3_2: false,
      ccAni4_2: false,
      sixItemSonAni1_2: false,
      sixItemSonAni2_2: false,
      sixItemSonAni3_2: false,
      sixItemSonAni4_2: false,
      sixItemSonAni5_2: false,
      sixItemSonAni6_2: false,
      rightValBoxitem1_2: false,
      rightValBoxitem2_2: false,
      rightValBoxitem3_2: false,
      rightValBoxitem4_2: false,
      rightValBoxitem5_2: false,
      rightValBoxitem6_2: false,
      rightValBoxitem7_2: false,
      rightValBoxitem8_2: false,
      rightValBoxitemChoose_2: false,

      rightOpa1: false,
      rightOpa2: false,
      word1: '资源分配中',
      word1_2: '资源分配中',
      showWorkSecond: false,
      bottomTitleAni1: false,
      bottomTitleAni2: false,
      isFinished: false,
      roomAni1: false,
      roomAni1_2: false,
      showWork1: false,
      showWork2: false,
      poorAni1: false,
      poorAni2: false,
      poorAni3: false,
      coreAni1: false,

      num1: 0,
      num2: 0,
      num3: 0,
      num4: 0,
      chartsanchor: null,
      fourCoreAni1: true,
      rightAni: false,
      otherTwoIndex: null
    }
  },
  created() {
    for (let i = 0; i < 27; i++) {
      this.list1.push(i)
      if (i < 26) {
        this.list2.push(i)
      }
      if (i < 16) {
        this.list3.push(i)
      }
    }
    this.isAuto = this.GLOBAL.isAuto
  },
  mounted() {
    this.init()
    this.initanchor()
  },
  methods: {
    otherTwoIndexFun(index) {
      if (this.otherTwoIndex === index) {
        this.otherTwoIndex = ''
      } else {
        this.otherTwoIndex = index
      }
    },
    openDown(index) {
      if (index === 1) {
        this.showWork1 = !this.showWork1
        this.showWork2 = false
      }
      if (index === 2) {
        this.showWork2 = !this.showWork2
        this.showWork1 = false
      }
    },
    // 通用添加小球方法
    addBallNext() {
      var height = 63
      var width = 967
      var svg = d3.select('#svg1').append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 1000; i++) {
        svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (60) + 2).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 950 - (i - 30)).ease(d3.easeLinear)
      }
    },
    // 通用添加小球方法
    addBallNext2() {
      var height = 63
      var width = 967
      var svg = d3.select('#svg2').append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 1000; i++) {
        svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (60) + 2).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 950 - (i - 30)).ease(d3.easeLinear)
      }
    },
    init() {
      let settimer1 = setTimeout(() => {
        this.wordAni1 = true
      }, 400);
      let settimer2 = setTimeout(() => {
        this.wordAni2 = true
        this.centerOpa = true
      }, 600);
      let settimer3 = setTimeout(() => {
        this.ccAni1 = true
        // this.addBallNext()
      }, 800);
      let settimer4 = setTimeout(() => {
        this.cwordAni1 = true
      }, 1000);
      let settimer5 = setTimeout(() => {
        this.cwordAni2 = true
      }, 1300);
      let settimer6 = setTimeout(() => {
        this.cwordAni3 = true
      }, 1600);
      let settimer7 = setTimeout(() => {
        this.cwordAni4 = true
      }, 1900);
      let settimer9 = setTimeout(() => {
        this.ccAni2 = true
        this.$emit('step2Loading', 'step2Loading')
      }, 2200);
      let settimer10 = setTimeout(() => {
      }, 2500);
      let settimer15 = setTimeout(() => {
        this.roomAni1 = true
        this.coreAni1 = true
        this.poorAni1 = true
        this.poorAni2 = true
      }, 2800);
      let settimer11 = setTimeout(() => {
        this.poorAni3 = true
      }, 3100);
      let settimer12 = setTimeout(() => {
        this.ccAni3 = true
        this.sixItemSonAni1 = true
        this.sixItemSonAni2_2 = true
      }, 3400);
      let settimer13 = setTimeout(() => {
        this.rightAni = true
      }, 3700);
      let settimer14 = setTimeout(() => {
        this.iconAni1 = false
        this.iconAni2 = true
        this.word1 = '资源分配完成'
        // this.showWorkSecond = true
        // this.init2()
      }, 4000);
      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
        // clearTimeout(settimer8)
        // settimer8 = null;
        clearTimeout(settimer9)
        settimer9 = null;
        clearTimeout(settimer10)
        settimer10 = null;
        clearTimeout(settimer11)
        settimer11 = null;
        clearTimeout(settimer12)
        settimer12 = null;
        clearTimeout(settimer13)
        settimer13 = null;
        clearTimeout(settimer14)
        settimer14 = null;
        clearTimeout(settimer15)
        settimer15 = null;
      })
    },
    init2() {
      let settimer1 = setTimeout(() => {
        this.wordAni4 = true
      }, 500);
      let settimer2 = setTimeout(() => {
        this.wordAni5 = true
        this.centerOpa = true
      }, 1000);
      let settimer3 = setTimeout(() => {
        this.ccAni1_2 = true
      }, 1500);
      let settimer4 = setTimeout(() => {
        this.cwordAni1_2 = true
      }, 2000);
      let settimer5 = setTimeout(() => {
        this.cwordAni2_2 = true
      }, 2500);
      let settimer6 = setTimeout(() => {
        this.cwordAni3_2 = true
      }, 3000);
      let settimer7 = setTimeout(() => {
        this.cwordAni4_2 = true
      }, 3500);
      let settimer9 = setTimeout(() => {
        this.ccAni2_2 = true
      }, 4000);
      let settimer10 = setTimeout(() => {
        this.roomAni1_2 = true
      }, 4500);
      let settimer11 = setTimeout(() => {
        this.sixItemSonAni1_2 = true
      }, 5500);
      let settimer12 = setTimeout(() => {
        this.ccAni3_2 = true
      }, 5500);
      let settimer13 = setTimeout(() => {
        this.iconAni3 = false
        this.iconAni4 = true
        this.word2 = '资源分配完成'
      }, 6500);
      let settimer14 = setTimeout(() => {
        this.isFinished = true
        this.$emit('stepEnd', 2)
      }, 8500);
      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
        // clearTimeout(settimer8)
        // settimer8 = null;
        clearTimeout(settimer9)
        settimer9 = null;
        clearTimeout(settimer10)
        settimer10 = null;
        clearTimeout(settimer11)
        settimer11 = null;
        clearTimeout(settimer12)
        settimer12 = null;
        clearTimeout(settimer13)
        settimer13 = null;
        clearTimeout(settimer14)
        settimer14 = null;
      })
    },
    endFun(index) {
      // this.$emit('endFun', index)
    },
    resetFun() {
    },
    initanchor(isData) {
      let otherNow = this.propsdata.otherNow
      let option = {
        tooltip: {
          show: false,
          formatter: '{a} <br/>{b} : {c}%'
        },
        grid: {
          top: 0
        },
        series: [
          {
            name: 'Pressure',
            type: 'gauge',
            splitNumber: 1,
            min: otherNow.min,
            max: otherNow.max,
            radius: 125,
            startAngle: 210,
            endAngle: -30,
            center: ['50%', '65%'],
            pointer: {
              itemStyle: {
                color: '#158EFF',
              }
            },
            title: {
              show: false
            },
            progress: {
              show: true,
              itemStyle: {
                color: '#158EFF',
              }
            },
            detail: {
              show: false,
              valueAnimation: false,
              formatter: '{value}'
            },
            anchor: {
              show: true,
              itemStyle: {
                size: 20
              }
            },
            itemStyle: {
              borderWidth: 20
            },
            data: [
              {
                value: otherNow.use,
                name: '已使用'
              }
            ]
          }
        ]
      };
      // 内存泄漏 无dom 不执行
      if (!this.chartsanchor) {
        this.chartsanchor = echarts.init(this.$refs.anchor, null, { width: 250, height: 200 });
      }
      this.chartsanchor.clear()
      // 使用刚指定的配置项和数据显示图表。
      this.chartsanchor.setOption(option);
    },
  },
  beforeDestroy() {
    this.resetFun()
  },
}
</script>

<style lang="scss">
.pageWork1_2 {
  .fourCore {
    background-image: url('~@/assets/images/1920/work/step4/bg.png');
    background-size: contain;
    background-position: center center;
    width: 577px;
    height: 280px;
    // opacity: 0;

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }

    .son {
      width: 50%;
      height: 50%;
      text-align: center;

      .t1 {
        font-size: 34px;
        line-height: 34px;
        padding-top: 30px;

        span {
          font-size: 16px;
        }
      }

      .t2 {
        font-size: 16px;
        margin-top: 15px;
      }
    }
  }

  .recommon {
    width: 576px;
    height: 380px;
    box-sizing: border-box;
    padding: 32px 15px 0 15px;
    background-color: rgba(38, 38, 42, 0.15);
    // margin-top: 30px;
    opacity: 0;

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }

    .title {
      font-size: 18px;
    }

    .recommonlist {
      text-align: center;

      .recommonitem {
        width: 33%;
        margin: 25px 0;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }
  }

  .centerCore {
    opacity: 0;
    width: 605px;

    .topList {
      position: relative;

      .item {
        opacity: 0;

        .leftIcon {
          position: relative;

          .icon {
            width: 34px;
            height: 34px;
            margin-right: 23px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .text {
            font-size: 16px;
            line-height: 34px;
            width: 90px;
          }

          .arrow {
            background-image: url('~@/assets/images/1920/work/step2/arrow.png');
            background-size: contain;
            width: 16px;
            height: 10px;
            position: absolute;
            z-index: 2;
            top: 45px;
            left: 8px;
            cursor: pointer;

            &.on {
              transform: rotate(180deg);
            }
          }

          &:first-child {}

          &.long {
            margin-top: 45px;
          }
        }

        .cc {
          position: relative;
          background-color: rgba($color: #26262A, $alpha: 0.15);
          border-radius: 4px;
          margin-bottom: 30px;
        }

        .cc1 {
          width: 458px;
          height: 138px;

          .smallItemList {
            box-sizing: border-box;
            padding: 21px 17px;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            width: 100%;
            height: 100%;

            .smallItem {
              width: 203px;
              height: 36px;
              // background: rgba($color: #26262A, $alpha: 0.5);
              border: 1px solid #333;
              border-radius: 4px;
              font-size: 16px;
              text-align: center;
              line-height: 34px;
              opacity: 0;
              margin-bottom: 20px;

              &.on {
                animation: imgOpacity 1s linear 1 forwards;
              }
            }
          }
        }

        .cc2 {
          width: 458px;
          // height: 63px;

          .sixItem {
            padding: 14px 40px 14px 40px;

            .sixItemSon {
              .text {
                font-size: 18px;
                line-height: 16px;
              }

              .point {
                width: 10px;
                height: 10px;
                background: #666666;
                border-radius: 50%;
                margin: 0 auto;
                margin-top: 10px;
              }

              &.on {
                .point {
                  background: #21D571;
                }
              }

              &.hide {}
            }

          }

          .roomList {
            opacity: 0;
            padding: 30px 0 5px 0;

            &.ani {
              animation: imgOpacity 1s linear 1 forwards;
            }

            .roomitem {
              padding: 0 15px 0 34px;
              margin-bottom: 23px;
              font-size: 16px;
              // line-height: 22px;
              // opacity: 0;
              border-left: 2px solid transparent;

              .line {
                background-image: url('~@/assets/images/1920/work2/line111.png');
                background-size: cover;
                width: 111px;
                height: 1px;
                margin: 0 auto;
              }

              &.ani {
                animation: imgOpacity 1s linear 1 forwards;
              }

              .text {
                line-height: 39px;
              }

              .center {
                line-height: 80px;
              }

              .rt {
                color: #FF6A00;
                text-align: right;
              }

              .le {}

              .icon {
                background-image: url('~@/assets/images/1920/work/step2/item.png');
                background-size: contain;
                width: 21px;
                height: 22px;
                margin-right: 10px;
              }

              .text1 {
                margin-right: 60px;
              }

              &.on {
                color: #00E4FF;
                background-color: rgba($color: #ffffff, $alpha: 0.04);
                border-left-color: #00E4FF;
              }
            }
          }
        }

        .cc3 {
          width: 967px;
          height: 126px;

          .bg {
            background-image: url('~@/assets/images/page4/step1/cc3-bg.png');
            background-size: cover;
            width: 93px;
            height: 103px;
            position: absolute;
            z-index: 0;
            top: 50%;
            left: 50%;
            margin-top: -51.5px;
            margin-left: -46.5px;
          }

          .rightValBoxitem {
            width: 25%;
            height: 50%;
            box-sizing: border-box;
            text-align: center;
            border-left: 1px dashed rgba($color: #ffffff, $alpha: 0.3);
            border-bottom: 1px dashed rgba($color: #ffffff, $alpha: 0.3);
            padding-top: 20px;

            p {
              margin: 0;
              font-size: 18px;
              line-height: 18px;

              span {
                font-size: 14px;
              }

              &:first-child {
                margin-bottom: 8px;
              }
            }

            &:nth-child(1),
            &:nth-child(5) {
              border-left: none;
            }

            &:nth-child(5),
            &:nth-child(6),
            &:nth-child(7),
            &:nth-child(8) {
              border-bottom: none;
            }

            &.on {
              color: #00E3FB;
            }

            &.hide {
              p {
                animation: imgOpacity2 0.5s linear 1 forwards;
              }
            }
          }
        }

        .cc4 {
          width: 458px;

          .usepercent {
            padding-top: 37px;

            .useitem {
              width: 136px;
              height: 106px;
              background: rgba($color: #26262A, $alpha: 0.15);
              border-radius: 4px;
              box-sizing: border-box;
              padding-top: 6px;

              .useitemtext {
                font-size: 14px;
                text-align: center;
                padding-top: 10px;
              }

              &.on {
                margin-right: 26px;
              }
            }
          }

          .left {
            width: 126px;
            height: 40px;
            background: #2689E0;
            border-radius: 4px;
            font-size: 18px;
            text-align: center;
            line-height: 40px;

            p {
              margin: 0;

              &.ss {
                font-size: 14px;
              }
            }

            &.on {
              background: #26A6E0;
            }
          }

          .right {
            padding-left: 30px;
            font-size: 16px;
            line-height: 40px;

            span {
              margin-right: 20px;
            }
          }
        }

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }

      .leftLine {
        width: 1px;
        border-left: 1px dashed rgba($color: #16D0FF, $alpha: 0.5);
        opacity: 0.5;
        left: 17px;
        position: absolute;
        z-index: 1;
        opacity: 0;

        &.len1 {
          height: 129px;
          top: 37px;
        }

        &.len2 {
          height: 118px;
          top: 187px;
        }

        &.len3 {
          height: 390px;
          top: 208px;
        }

        &.len4 {
          height: 170px;
          top: 37px;
        }

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }

    .bottomTitle {
      font-size: 28px;
      text-align: center;
      padding-top: 10px;
      opacity: 0;

      &.ani {
        animation: imgOpacity 1s linear 1 forwards;
      }
    }

    &.opa {
      opacity: 1;
    }

    &.left {
      width: 276px;
    }

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .rightBox {
    width: 1222px;
    box-sizing: border-box;
  }

  .wordCore {
    box-sizing: border-box;
    margin-top: 30px;
    padding-top: 15px;
    text-align: center;
    text-align: center;
    width: 276px;
    height: 194px;
    background: rgba($color: #26262A, $alpha: 0.15);
    border-radius: 4px;

    .t1 {
      font-size: 46px;
      line-height: 46px;
      color: #ffffff;
      width: 130px;
      padding-top: 20px;

      p {
        font-size: 16px;
        margin: 0;
      }
    }

    .line {
      width: 30px;
      height: 3px;
      background: #1B81DF;
      margin: 0 auto;
      margin-top: 20px;
    }

    .t2 {
      font-size: 18px;
      margin-top: 10px;
    }

    .linec {
      background-image: url('~@/assets/images/1920/work2/line.png');
      background-size: cover;
      width: 1px;
      height: 98px;
    }

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .rightCore {
    opacity: 0;

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }

    .top {
      width: 576px;
      height: 579px;
      background: rgba($color: #26262A, $alpha: 0.2);
      border-radius: 4px;
    }

    .down {
      width: 576px;
      height: 182px;
      background: rgba($color: #26262A, $alpha: 0.2);
      border-radius: 4px;
      margin-top: 30px;
    }

    .title {
      padding: 13px;
      font-size: 18px;
    }

    .usepercent {
      padding: 0 30px;

      .useitem {
        width: 50%;
        border-radius: 4px;
        box-sizing: border-box;
        padding-top: 6px;
        position: relative;
        cursor: pointer;
        .useitemtext {
          font-size: 14px;
          text-align: center;
        }

        .roundBox {
          margin-bottom: 10px;
        }

        &.on {
          margin-right: 26px;
        }
      }
    }

    .anchorBox {
      .anchor {
        width: 250px;
        margin: 0 auto;
      }

      .core {
        text-align: center;

        .text1 {
          font-size: 16px;
        }

        .text2 {
          font-size: 24px;
          margin-top: 10px;
        }
      }
    }
  }

  .roundPosi {
    width: 280px;
    height: 90px;
    background: #201F23;
    border: 1px solid #111111;
    border-radius: 8px;
    position: absolute;
    z-index: 999;
    top: 0;
    left: -200px;

    .line {
      width: 1px;
      height: 80px;
      background-image: url('~@/assets/images/page3/line2.png');
      background-size: cover;
    }

    .posiSon {
      width: calc(50% - 1px);
      text-align: center;
      padding-top: 10px;

      .tp {
        font-size: 24px;
        line-height: 36px;

        span {
          font-size: 20px;
        }
      }

      .dn {
        font-size: 16px;
        margin-top: 10px;
      }
    }
  }
}

@keyframes imgOpacity {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes imgOpacity2 {

  0% {
    opacity: 1;
  }

  100% {
    opacity: 0.2;
  }
}
</style>
