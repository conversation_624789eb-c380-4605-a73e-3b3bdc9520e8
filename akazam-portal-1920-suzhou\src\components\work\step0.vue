<template>
  <div class="pageWork0 flex-box-between">
    <div class="leftStep flex-box">
      <div class="stepList">
        <div class="item on">基础配置</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(2)">资源池分配</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(3)">运行设置</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(4)">模型训练</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(5)">推理部署</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(6)">访问验证</div>
      </div>
      <div class="wordList">
        <p :class="wordAni1 ? 'ani' : ''">应用名称：图像识别-app16na84</p>
        <p :class="wordAni2 ? 'ani' : ''">数据选取：导入-图像分类</p>
        <p :class="wordAni3 ? 'ani' : ''" class="flex-box">
          <i v-if="iconAni1" class="el-icon-loading"></i>
          <img v-if="iconAni2" src="~@/assets/images/page4/step1/success.png" alt="">
          数据集： 上传训练数据集
        </p>

        <p :class="wordAni4 ? 'ani' : ''">算力要求：应用作业1-智算作业-图像分类学习</p>
        <p :class="wordAni5 ? 'ani' : ''" class="flex-box">
          <i v-if="iconAni3" class="el-icon-loading"></i>
          <img v-if="iconAni4" src="~@/assets/images/page4/step1/success.png" alt="">
          配置需求输入完成
        </p>

        <p :class="wordAni6 ? 'ani' : ''">算力要求：应用作业2-通算作业-图像应用Build</p>
        <p :class="wordAni7 ? 'ani' : ''" class="flex-box">
          <i v-if="iconAni5" class="el-icon-loading"></i>
          <img v-if="iconAni6" src="~@/assets/images/page4/step1/success.png" alt="">
          配置需求输入完成
        </p>
      </div>
    </div>
    <div class="rightBox flex-box-between">
      <div class="centerCore">
        <div class="item flex-box" :class="itemAni0 ? 'ani' : ''">
          <div class="iconBox">
            <div class="icon"><img src="~@/assets/images/page4/step1/icon-0.png" alt=""></div>
            <div class="word">数据选取</div>
          </div>
          <div class="arrow"><img src="~@/assets/images/page4/step1/arrow-0.png" alt=""></div>
          <div class="pointList">
            <VueMatrixRaindrop class="codeBox" :backgroundColor="`rgba(42,42,45,0.2)`" :canvasWidth="canvasWidth"
              :canvasHeight="canvasHeight">
            </VueMatrixRaindrop>
            <div class="percentList">
              <div class="word flex-box-between" :style="{ 'width': `${percent > 20 ? percent : 20}%` }">
                <p>{{ percentdesc }}</p>
                <p>{{ percent }}%</p>
              </div>
              <div class="line">
                <div class="inner" :style="{ 'width': `${percent}%` }"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="item flex-box" :class="itemAni1 ? 'ani' : ''">
          <div class="iconBox">
            <div class="icon"><img src="~@/assets/images/page4/step1/icon-1.png" alt=""></div>
            <div class="word">智算作业</div>
          </div>
          <div class="arrow"><img src="~@/assets/images/page4/step1/arrow-1.png" alt=""></div>
          <div class="pointList on">
            <div class="svg" id="svg1"></div>
            <div class="smallItemList flex-box">
              <div class="smallItem" :class="cwordAni1 ? 'on' : ''">CPU≥8核</div>
              <div class="smallItem" :class="cwordAni2 ? 'on' : ''">内存≥64G</div>
              <div class="smallItem" :class="cwordAni3 ? 'on' : ''">GPU: NVIDIA V100</div>
              <div class="smallItem" :class="cwordAni4 ? 'on' : ''">GPU卡数量≥1个</div>
              <!-- <div class="smallItem" :class="cwordAni5 ? 'on' : ''">GPU单卡显存≥8G</div> -->
              <!-- <div class="smallItem" :class="cwordAni6 ? 'on' : ''">网络连接：专线</div> -->
            </div>
          </div>
        </div>
        <div class="item flex-box" :class="itemAni2 ? 'ani' : ''">
          <div class="iconBox">
            <div class="icon"><img src="~@/assets/images/page4/step1/icon-2.png" alt=""></div>
            <div class="word">通算作业</div>
          </div>
          <div class="arrow"><img src="~@/assets/images/page4/step1/arrow-2.png" alt=""></div>
          <div class="pointList on">
            <div class="svg" id="svg2"></div>
            <div class="smallItemList flex-box">
              <div class="smallItem" :class="cwordAni1_2 ? 'on' : ''">CPU≥2核</div>
              <div class="smallItem" :class="cwordAni2_2 ? 'on' : ''">内存≥4G</div>
              <!-- <div class="smallItem trans" :class="cwordAni3_2 ? 'on' : ''">数据存储≥10G</div> -->
              <!-- <div class="smallItem" :class="cwordAni4_2 ? 'on' : ''">网络带宽≥2M</div> -->
              <div class="smallItem" :class="cwordAni3_2 ? 'on' : ''">网络连接：互联网</div>
            </div>
          </div>
        </div>
        <div class="wordBottom" :class="wordBottomAni ? 'ani' : ''">作业需求配置</div>
      </div>
      <div class="rightCore">
        <div class="topCore flex-box-between">
          <div class="item" :class="imgItemAni ? 'ani' : ''">
            <div class="icon">
              <img src="~@/assets/images/page4/step1/rl-i1.png" alt="" :class="imgLeftAni ? 'ani' : ''">
              <div class="number">{{ time1 }}<span>s</span></div>
            </div>
            <div class="word">智算作业-图像分类学习</div>
          </div>
          <div class="item" :class="imgItemAni ? 'ani' : ''">
            <div class="icon" :class="imgLeftAni ? 'ani' : ''">
              <img src="~@/assets/images/page4/step1/rl-i2.png" alt="" :class="imgLeftAni ? 'ani' : ''">
              <div class="number">{{ time2 }}<span>s</span></div>
            </div>
            <div class="word">通算作业-图像应用Build</div>
          </div>
        </div>
        <div class="wordBottom" :class="wordBottomAni2 ? 'ani' : ''">作业完成预计所需时间</div>
      </div>
    </div>
  </div>
</template>
<script>
import { getStore, setStore } from "@/common/util";
import VueMatrixRaindrop from '../vueMatrixDigitRain/index.vue'
import * as d3 from 'd3' // d3

export default {
  name: 'workstep1',
  components: {
    VueMatrixRaindrop,
  },
  data() {
    return {
      point: [],
      percent: 0,
      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      wordAni4: false,
      wordAni5: false,
      wordAni6: false,
      wordAni7: false,
      iconAni1: true,
      iconAni2: false,
      iconAni3: true,
      iconAni4: false,
      iconAni5: true,
      iconAni6: false,
      showCode: false,
      timer: null,
      itemAni1: false,
      itemAni2: false,
      percent: 0,
      percent2: 0,
      percentdesc: '上传训练数据集...',
      percentdesc2: '作业入池中...',
      percentdesc3: '作业入池中...',
      wordBottomAni: false,
      imgItemAni: false,
      imgItemAni2: false,
      time1: 0,
      time2: 0,
      time1Timer: null,
      time2Timer: null,
      timeDate: '',
      imgLeftAni: false,
      imgRightAni: false,
      ponit1Index: null,
      ponit2Index: null,
      pointAnitimer1: null,
      pointAnitimer2: null,
      wordBottomAni2: null,
      canvasWidth: 770,
      canvasHeight: 90,
      isAuto: null,
      cwordAni1: false,
      cwordAni2: false,
      cwordAni3: false,
      cwordAni4: false,
      cwordAni5: false,
      cwordAni6: false,
      cwordAni1_2: false,
      cwordAni2_2: false,
      cwordAni3_2: false,
      cwordAni4_2: false,
      cwordAni5_2: false,
      itemAni0: false,
    }
  },
  created() {
    this.isAuto = this.GLOBAL.isAuto
    const date = new Date(); // 时间戳是一个以毫秒为单位的整数
    const year = date.getFullYear(); // 获取当前年份，比如2022
    const month = date.getMonth() + 1; // 获取当前月份，返回值为0-11，需要+1
    const day = date.getDate(); // 获取当前日期，比如12
    const hour = date.getHours(); // 获取当前小时数，0-23
    const minute = date.getMinutes(); // 获取当前分钟数，0-59
    const second = date.getSeconds(); // 获取当前秒数，0-59
    this.timeDate = `${year}/${month}/${day} ${hour}:${minute}:${second}`
    this.canvasWidth = this.GLOBAL.relPx(790)
    this.canvasHeight = this.GLOBAL.relPx(60)
  },
  mounted() {
    this.init()
  },
  methods: {
    // 通用添加小球方法
    addBallNext() {
      var height = 138
      var width = 830
      var svg = d3.select('#svg1').append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 1000; i++) {
        svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (136) + 2).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 820 - (i - 30)).ease(d3.easeLinear)
      }
    },
    // 通用添加小球方法
    addBallNext2() {
      var height = 138
      var width = 830
      var svg = d3.select('#svg2').append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 1000; i++) {
        svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (136) + 2).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 820 - (i - 30)).ease(d3.easeLinear)
      }
    },
    init() {
      let arr = []
      for (let i = 0; i < 96; i++) {
        arr.push(i)
      }
      this.point = arr
      let settimer1 = setTimeout(() => {
        this.wordAni1 = true
      }, 500);
      let settimer2 = setTimeout(() => {
        this.wordAni2 = true
      }, 1000);
      let settimer3 = setTimeout(() => {
        this.wordAni3 = true
        this.itemAni0 = true
      }, 1500);
      let settimer4 = setTimeout(() => {
        this.itemAni1 = true
        this.wordAni4 = true
        this.lineAni()
      }, 2000);
      let settimer5 = setTimeout(() => {
        this.wordAni5 = true
        this.itemAni2 = true
        this.addBallNext()
      }, 2500);
      let settimer6 = setTimeout(() => {
        this.wordAni6 = true
        this.wordBottomAni = true
        this.imgItemAni = true
        this.imgLeftAni = true
        this.addBallNext2()
        this.timeFun()
        this.cwordAni1 = true
      }, 3000);
      let settimer7 = setTimeout(() => {
        this.wordAni7 = true
        this.imgItemAni2 = true
        this.imgRightAni = true
        this.wordBottomAni2 = true
        this.cwordAni2 = true
      }, 3500);
      let settimer8 = setTimeout(() => {
        this.cwordAni3 = true
        this.cwordAni1_2 = true
      }, 4000);
      let settimer9 = setTimeout(() => {
        this.cwordAni4 = true
        this.cwordAni2_2 = true
      }, 4500);
      let settimer10 = setTimeout(() => {
        this.cwordAni5 = true
        this.cwordAni3_2 = true
      }, 5000);
      let settimer11 = setTimeout(() => {
        this.cwordAni6 = true
        this.cwordAni4_2 = true
        this.iconAni3 = false
        this.iconAni4 = true
      }, 5500);
      let settimer12 = setTimeout(() => {
        this.cwordAni5_2 = true
        this.iconAni5 = false
        this.iconAni6 = true
      }, 6000);

      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
        clearTimeout(settimer8)
        settimer8 = null;
        clearTimeout(settimer9)
        settimer9 = null;
        clearTimeout(settimer10)
        settimer10 = null;
        clearTimeout(settimer11)
        settimer11 = null;
        clearTimeout(settimer12)
        settimer12 = null;
      })
    },
    timeFun() {
      this.time1Timer = setInterval(() => {
        this.time1 += 1
        if (this.time1 >= 36) {
          clearInterval(this.time1Timer)
          this.$emit('stepEnd', 1)
        }
      }, 200);
      this.time2Timer = setInterval(() => {
        this.time2 += 1
        if (this.time2 >= 20) {
          clearInterval(this.time2Timer)
        }
      }, 200);
    },
    endFun(index) {
      if (this.isAuto) {
        return false
      }
      this.$emit('endFun', index)
    },
    lineAni() {
      this.timer = setInterval(() => {
        this.percent += 1
        if (this.percent >= 100) {
          clearInterval(this.timer)
          this.percentdesc = '上传完成（存储位置：华为云-内蒙3(合营)）'
          this.percentdesc3 = '作业已入池'
          this.iconAni1 = false
          this.iconAni2 = true
        }
      }, 30);
    },
    pointAni() {
      this.ponit1Index = 0
      this.pointAnitimer1 = setInterval(() => {
        this.ponit1Index += 1
        if (this.ponit1Index > 96) {
          clearInterval(this.pointAnitimer1)
        }
      }, 150);
    },
    pointAni2() {
      this.ponit2Index = 0
      this.pointAnitimer2 = setInterval(() => {
        this.ponit2Index += 1
        if (this.ponit2Index > 96) {
          clearInterval(this.pointAnitimer2)
        }
      }, 110);
    },
    resetFun() {
      if (this.timer1) {
        clearInterval(this.timer1)
      }
      if (this.timer2) {
        clearInterval(this.timer2)
      }
      if (this.time1Timer) {
        clearInterval(this.time1Timer)
      }
      if (this.time2Timer) {
        clearInterval(this.time2Timer)
      }
      if (this.pointAnitimer1) {
        clearInterval(this.pointAnitimer1)
      }
      if (this.pointAnitimer2) {
        clearInterval(this.pointAnitimer2)
      }
    },
  },
  beforeDestroy() {
    this.resetFun()
  },
}
</script>

<style lang="scss">
.pageWork0 {
  

  .rightBox {
    width: 2500px;
    box-sizing: border-box;
    padding-right: 50px;
    padding-top: 120px;

    .centerCore {
      .item {
        margin-bottom: 60px;
        opacity: 0;

        .iconBox {
          width: 157px;
          height: 138px;
          background: rgba($color: #26262A, $alpha: 0.3);
          border-radius: 4px;
          padding-top: 27px;
          box-sizing: border-box;

          .icon {
            width: 45px;
            height: 45px;
            margin: 0 auto;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .word {
            font-size: 18px;
            color: #ffffff;
            text-align: center;
            padding-top: 10px;
          }
        }

        .arrow {
          width: 41px;
          height: 14px;
          margin-top: 60px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .pointList {
          width: 830px;
          height: 138px;
          background: rgba($color: #2A2A2D, $alpha: 0.3);
          box-shadow: 0px 2px 14px 2px rgba(13, 14, 64, 0.3);
          border-radius: 4px;
          box-sizing: border-box;
          padding: 10px 20px;
          position: relative;

          .codeBox {
            // opacity: 1;
            // position: absolute;
            z-index: 1;
            top: 10px;
            left: 20px;
          }

          .point {
            width: 100%;
            // height: 72px;

            .son {
              width: 10px;
              height: 10px;
              border-radius: 50%;
              background-color: rgba($color: #7FB41C, $alpha: 0.4);
              margin: 10px 7px;

              &.on {
                background-color: rgba($color: #3174F3, $alpha: 1);
              }

              &.s2 {
                &.on {
                  background-color: rgba($color: #00C2FC, $alpha: 1);
                }
              }
            }
          }

          .percentList {
            margin-top: 5px;
            padding: 0 7px;

            .word {
              p {
                font-size: 14px;
                color: #ffffff;
                padding: 0;
                margin: 10px 0;
              }
            }

            .line {
              background-color: #1A191C;
              border-radius: 3px;

              .inner {
                background-image: url('~@/assets/images/page4/step1/percent.png');
                border-radius: 3px;
                // background-size: cover;
                width: 80%;
                height: 6px;
              }
            }
          }

          &.on {
            padding: 0;
          }
        }

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }

      .wordBottom {
        font-size: 26px;
        color: #FFFFFF;
        text-align: center;
        margin-top: 70px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }

    .rightCore {
      width: 1100px;
      margin-top: 30px;

      .topCore {
        .item {
          width: 400px;
          opacity: 0;

          .icon {
            background-image: url('~@/assets/images/page4/step1/rl-bg.png');
            width: 362px;
            height: 362px;
            background-size: cover;
            position: relative;
            margin: 0 auto;


            img {
              width: 203px;
              height: 203px;
              display: block;
              position: absolute;
              top: 50%;
              left: 50%;
              margin-left: -101.5px;
              margin-top: -101.5px;

              &.ani {
                animation: imgOpacity 2s linear infinite alternate both;
              }
            }

            .number {
              font-size: 65px;
              text-align: center;
              padding-top: 150px;
              line-height: 65px;

              span {
                font-size: 44px;
              }
            }

          }

          .word {
            font-size: 20px;
            color: #ffffff;
            text-align: center;
            margin-bottom: 20px;
          }

          .time {
            font-size: 40px;
            color: #ffffff;
            text-align: center;
            font-weight: bold;
          }

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }

          &.right {
            width: 541px;

            .icon {
              width: 541px;
              height: 356px;
              margin-bottom: 90px;
              position: relative;

              .posiImg {
                position: absolute;
                top: 0;
                left: 0;
              }

              img {
                &.ani {
                  animation: imgOpacity 1s linear infinite alternate both;
                }
              }
            }
          }
        }
      }

      .wordBottom {
        font-size: 26px;
        color: #FFFFFF;
        text-align: center;
        margin-top: 160px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }
  }

  .svg {
    width: 100%;
    height: 100%;
  }

  .smallItemList {
    box-sizing: border-box;
    padding: 0;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;

    .smallItem {
      width: 146px;
      height: 36px;
      background: rgba($color: #26262A, $alpha: 0.5);
      border: 1px solid #26A6E0;
      border-radius: 4px;
      font-size: 14px;
      text-align: center;
      line-height: 34px;
      margin: 16px 64px;
      opacity: 0;

      &.trans {
        transform: translateY(30px);
      }

      &.on {
        animation: imgOpacity 1s linear 1 forwards;
      }
    }
  }
}

@keyframes imgOpacity {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}</style>
