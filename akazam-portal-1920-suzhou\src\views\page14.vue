<template>
  <div class="pageCommon page14">
    <!-- 头部-s -->
    <header-common
      :icon="6"
      :name="'作业应用-算力资源-开通资源'"
    ></header-common>
    <!-- 头部-e -->
    <!-- <div class="openMenu" @click="showMenu = !showMenu"></div> -->
    <div class="menu flex-box-between newMenu" v-show="showMenu">
      <div class="item" :class="showItem ? 'ani' : ''" @click="goWork(1)">
        <img class="img1" src="~@/assets/images/page4/icon-1_on.png" alt="" />
        <img class="img2" src="~@/assets/images/page4/icon-1.png" alt="" />
        <p>人工智能</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''" @click="goWork(2)">
        <img class="img1" src="~@/assets/images/page4/icon-2_on.png" alt="" />
        <img class="img2" src="~@/assets/images/page4/icon-2.png" alt="" />
        <p>人脸识别</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''">
        <img src="~@/assets/images/page4/icon-3.png" alt="" />
        <p>自动驾驶</p>
      </div>
      <div class="item" :class="[showItem ? 'ani' : '']" @click="goWork(4)">
        <img class="img1" src="~@/assets/images/page4/icon-4_on.png" alt="" />
        <img class="img2" src="~@/assets/images/page4/icon-4.png" alt="" />
        <p>云渲染</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''">
        <img src="~@/assets/images/page4/icon-5.png" alt="" />
        <p>工业仿真</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''">
        <img src="~@/assets/images/page4/icon-6.png" alt="" />
        <p>预测分析</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''">
        <img src="~@/assets/images/page4/icon-7.png" alt="" />
        <p>智慧城市</p>
      </div>
      <div class="item" :class="[showItem ? 'ani' : '']" @click="goWork(8)">
        <img class="img1" src="~@/assets/images/page4/icon-8_on.png" alt="" />
        <img class="img2" src="~@/assets/images/page4/icon-8.png" alt="" />
        <p>图像识别</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''" @click="goWork(9)">
        <img class="img1" src="~@/assets/images/page4/icon-9_on.png" alt="" />
        <img class="img2" src="~@/assets/images/page4/icon-9.png" alt="" />
        <p>文生图</p>
      </div>
    </div>
    <div class="loading flex-box-center" v-if="step == 0">
      <div class="icon"><i class="el-icon-loading"></i></div>
      <div class="text">等待作业指令中</div>
    </div>
    <div class="akazamSz" :class="isfull ? 'full' : ''">
      <div class="full" @click.stop="fullFun"></div>
      <div class="akazamWeb" v-if="step === 0">
        <div class="top flex-box">
          <div class="item on">算力资源</div>
          <div class="item">算力应用</div>
        </div>
        <div class="down">
          <div class="titlebox flex-box">
            <div class="color"></div>
            <div class="text">智算资源</div>
          </div>
          <div class="item flex-box-between on" @click="endFun(99)">
            <div class="icon">
              <img src="~@/assets/images/1920/work4/icon1.png" alt="" />
            </div>
            <div class="textcore">
              <div class="title">GPU云主机</div>
              <div class="desc">
                提供配置GPU算力卡云主机实例服务，可按需、按时订购，支撑人工智能(微调、推理等)渲染等场景
              </div>
              <div class="tags flex-box">
                <div class="son">天翼云</div>
                <div class="son">吴江算力中心</div>
                <div class="son">苏州社会算力</div>
                <div class="son">第三方云商算力</div>
              </div>
            </div>
          </div>
          <div class="item flex-box-between">
            <div class="icon">
              <img src="~@/assets/images/1920/work4/icon2.png" alt="" />
            </div>
            <div class="textcore">
              <div class="title">GPU裸金属服务器</div>
              <div class="desc">
                提供配置GPU算力卡裸金属服务器或集群服务，可按需订购租用，支撑人工智能(训练、微调等)、渲染等场鼎
              </div>
              <div class="tags flex-box">
                <div class="son">天翼云</div>
                <div class="son">吴江算力中心</div>
                <div class="son">苏州社会算力</div>
                <div class="son">第三方云商算力</div>
              </div>
            </div>
          </div>
          <div class="titlebox flex-box">
            <div class="color"></div>
            <div class="text">超算资源</div>
          </div>
          <div class="item flex-box-between">
            <div class="icon">
              <img src="~@/assets/images/1920/work4/icon3.png" alt="" />
            </div>
            <div class="textcore">
              <div class="title">天翼云EHPC服务</div>
              <div class="desc">
                提供天翼云EHPC超算算力资源，可按需、按时使用，支撑石油勘探、量子力学、气模拟癌症研究、基因组学、分子动力学、飞机和航天器空气动力学、量化金融、人工智能等应用
              </div>
              <div class="tags flex-box">
                <div class="son">天翼云-上海15</div>
              </div>
            </div>
          </div>
          <div class="item flex-box-between">
            <div class="icon">
              <img src="~@/assets/images/1920/work4/icon4.png" alt="" />
            </div>
            <div class="textcore">
              <div class="title">超算云主机</div>
              <div class="desc">
                提供配置超算规格云主机实例，可按需订购租用，支撑石油探、量子力学、气拟、癌症研究、基因组学、分子动力学、飞机和航天器空气动力学、量化金融、人工智能等应用
              </div>
              <div class="tags flex-box">
                <div class="son">天翼云</div>
                <div class="son">第三方云商算力</div>
              </div>
            </div>
          </div>
          <div class="titlebox flex-box">
            <div class="color"></div>
            <div class="text">通算资源</div>
          </div>
          <div class="item flex-box-between">
            <div class="icon">
              <img src="~@/assets/images/1920/work4/icon5.png" alt="" />
            </div>
            <div class="textcore">
              <div class="title">通算云主机</div>
              <div class="desc">
                提供配置普通规格云主机实例服务，可按需、按时订购，支撑用户应用研发调测、应用部署等
              </div>
              <div class="tags flex-box">
                <div class="son">天翼云</div>
                <div class="son">第三方云商算力</div>
              </div>
            </div>
          </div>
          <div class="item flex-box-between">
            <div class="icon">
              <img src="~@/assets/images/1920/work4/icon6.png" alt="" />
            </div>
            <div class="textcore">
              <div class="title">通算裸金属服务器</div>
              <div class="desc">
                提供配置通算裸金属服务器或集群服劳，可按需订购租用，支撑用户隔离要求高、性能要求较高的应用场录
              </div>
              <div class="tags flex-box">
                <div class="son">天翼云</div>
                <div class="son">第三方云商算力</div>
              </div>
            </div>
          </div>
          <div class="titlebox flex-box">
            <div class="color"></div>
            <div class="text">算力网络</div>
          </div>
          <div class="iconlist flex-box">
            <div class="son flex-box">
              <div class="icon">
                <img src="~@/assets/images/1920/work4/icon-d1.png" alt="" />
              </div>
              <div class="label">本地入云</div>
            </div>
            <div class="son flex-box">
              <div class="icon">
                <img src="~@/assets/images/1920/work4/icon-d2.png" alt="" />
              </div>
              <div class="label">云云互联</div>
            </div>
            <div class="son flex-box">
              <div class="icon">
                <img src="~@/assets/images/1920/work4/icon-d3.png" alt="" />
              </div>
              <div class="label">跨域互联</div>
            </div>
            <div class="son flex-box">
              <div class="icon">
                <img src="~@/assets/images/1920/work4/icon-d4.png" alt="" />
              </div>
              <div class="label">VPC专网</div>
            </div>
            <div class="son flex-box">
              <div class="icon">
                <img src="~@/assets/images/1920/work4/icon-d5.png" alt="" />
              </div>
              <div class="label">弹性公网IP</div>
            </div>
          </div>
        </div>
        <div class="footer flex-box-end">
          <el-button type="primary" v-show="showWeather" @click="endFun(1)"
            >下一步</el-button
          >
        </div>
      </div>
      <div class="akazamWeb" v-if="step === 99">
        <div class="form">
          <div class="formitem flex-box">
            <div class="titlesss flex-box">
              <div class="color"></div>
              <div class="text">资源需求</div>
            </div>
            <div class="rightBox">
              <div class="list flex-box-between">
                <div class="leftLabel">类型</div>
                <div class="rightCore flex-box">
                  <div class="flex-box">
                    <div
                      class="son"
                      v-for="(item, index) in datastep.options_cputype"
                      :key="index"
                      :class="datastep.cputype == item ? 'choose' : ''"
                      @click="cputypeFun(item)"
                    >
                      <div class="word">{{ item }}型</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="list flex-box-between">
                <div class="leftLabel">CPU >=</div>
                <div class="rightCore flex-box">
                  <div class="son longnopadding">
                    <div class="input">
                      <el-input v-model="datastep.s99Options_input1_value">
                        <template slot="append">核</template>
                      </el-input>
                    </div>
                  </div>
                </div>
              </div>
              <div class="list flex-box-between">
                <div class="leftLabel">内存 >=</div>
                <div class="rightCore flex-box">
                  <div class="son longnopadding">
                    <div class="input">
                      <el-input v-model="datastep.s99Options_input2_value">
                        <template slot="append">G</template>
                      </el-input>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="list flex-box-between"
                v-if="datastep.cputype == 'GPU'"
              >
                <div class="leftLabel">GPU型号</div>
                <div class="rightCore flex-box">
                  <div class="son longnopadding">
                    <div class="select">
                      <el-select
                        v-model="datastep.s99Options_input3_value"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="(item, index) in datastep.s99Options_input3"
                          :key="index"
                          :label="item"
                          :value="item"
                        >
                        </el-option>
                      </el-select>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="list flex-box-between"
                v-if="datastep.cputype == 'GPU'"
              >
                <div class="leftLabel">显卡数量 >=</div>
                <div class="rightCore flex-box">
                  <div class="son longnopadding">
                    <div class="input">
                      <el-input v-model="datastep.s99Options_input4_value">
                        <template slot="append">个</template>
                      </el-input>
                    </div>
                  </div>
                </div>
              </div>
              <div class="list flex-box-between">
                <div class="leftLabel">网络连接</div>
                <div class="rightCore flex-box">
                  <div class="son longnopadding">
                    <div class="select">
                      <el-select
                        v-model="datastep.s99Options_input5_value"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="(item, index) in datastep.s99Options_input5"
                          :key="index"
                          :label="item"
                          :value="item"
                        >
                        </el-option>
                      </el-select>
                    </div>
                  </div>
                </div>
              </div>
              <div class="list flex-box-between">
                <div class="leftLabel">付费方式</div>
                <div class="rightCore flex-box">
                  <div class="son choose"><div class="word">包年包月</div></div>
                </div>
              </div>
              <div class="list flex-box-between">
                <div class="leftLabel">购买时长</div>
                <div class="rightCore flex-box">
                  <div class="son longnopadding">
                    <div class="select">
                      <el-select
                        v-model="datastep.options3_value"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="(item, index) in datastep.options3"
                          :key="index"
                          :label="item"
                          :value="item"
                        >
                        </el-option>
                      </el-select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="footer flex-box-end">
          <!-- <el-button type="primary" @click="endFun(0)">重置应用</el-button> -->
          <el-button type="primary" @click="endFun(1)"
            >下一步：资源池分配</el-button
          >
        </div>
      </div>
      <div class="akazamWeb" v-if="step === 1">
        <div class="form">
          <div class="formitem flex-box">
            <div class="titlesss flex-box">
              <div class="color"></div>
              <div class="text">策略</div>
            </div>
            <div class="rightBox">
              <div class="list flex-box-between">
                <div class="leftLabel">&nbsp;</div>
                <div class="rightCore flex-box">
                  <div class="flex-box">
                    <div
                      class="son"
                      v-for="(item, index) in datastep.options_three"
                      :key="index"
                      :class="
                        datastep.options_three_value == item ? 'choose' : ''
                      "
                      @click="optionsThreeFun(item)"
                    >
                      <div class="word">{{ item }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="lod"
            v-loading="datastep.loadingCl"
            element-loading-text="资源限制匹配"
            element-loading-spinner="el-icon-loading"
            element-loading-background="RGBA(58, 60, 71, 1)"
          >
            <div class="formitem flex-box">
              <div class="titlesss flex-box">
                <div class="color"></div>
                <div class="text">云平台信息</div>
              </div>
              <div class="rightBox">
                <div class="list flex-box-between">
                  <div class="leftLabel">地域</div>
                  <div class="rightCore flex-box">
                    <div
                      class="son"
                      v-for="(item, index) in datastep.options1"
                      :key="index"
                      :class="datastep.options1_value == item ? 'choose' : ''"
                      @click="options1_valueFun(item)"
                    >
                      <div class="word">{{ item }}</div>
                    </div>
                  </div>
                </div>
                <div class="list flex-box-between">
                  <div class="leftLabel">可用区</div>
                  <div class="rightCore flex-box">
                    <div
                      class="son"
                      v-for="(item, index) in datastep.options2"
                      :key="index"
                      :class="datastep.options2_value == item ? 'choose' : ''"
                      @click="options2_valueFun(item)"
                    >
                      <div class="word">{{ item }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="formitem flex-box">
              <div class="titlesss flex-box">
                <div class="color"></div>
                <div class="text">规格</div>
              </div>
              <div class="rightBox">
                <div class="list flex-box-between">
                  <div class="leftLabel">&nbsp;</div>
                  <div class="rightCore flex-box">
                    <div class="flex-box">
                      <div
                        class="son"
                        v-for="(item, index) in datastep.options_cputype"
                        :key="index"
                        :class="datastep.cputype == item ? 'choose' : ''"
                        @click="cputypeFun(item)"
                      >
                        <div class="word">{{ item }}型</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="list flex-box-between">
                  <div class="leftLabel">&nbsp;</div>
                  <div class="rightCore flex-box">
                    <div class="son longnopadding">
                      <div class="select" v-if="datastep.cputype === 'vCPU'">
                        <el-select
                          v-model="datastep.options_vcpu_value"
                          placeholder="请选择"
                        >
                          <el-option
                            v-for="(item, index) in datastep.options_vcpu"
                            :key="index"
                            :label="item.name"
                            :value="item.name"
                          >
                          </el-option>
                        </el-select>
                      </div>
                      <div class="select" v-else>
                        <el-select
                          v-model="datastep.options_gpu_value"
                          placeholder="请选择"
                        >
                          <el-option
                            v-for="(item, index) in datastep.options_gpu"
                            :key="index"
                            :label="item.name"
                            :value="item.name"
                          >
                          </el-option>
                        </el-select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="formitem flex-box">
              <div class="titlesss flex-box">
                <div class="color"></div>
                <div class="text">系统镜像</div>
              </div>
              <div class="rightBox">
                <div class="list flex-box-between">
                  <div class="leftLabel">&nbsp;</div>
                  <div class="rightCore flex-box">
                    <div class="son longnopadding">
                      <div class="select">
                        <el-select
                          v-model="datastep.options_os_value"
                          placeholder="请选择"
                        >
                          <el-option
                            v-for="(item, index) in datastep.options_os"
                            :key="index"
                            :label="item"
                            :value="item"
                          >
                          </el-option>
                        </el-select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="footer flex-box-end">
          <el-button type="primary" @click="endFun(99)"
            >上一步：输入需求</el-button
          >
          <el-button type="primary" @click="endFun(2)"
            >下一步：网络&存储</el-button
          >
        </div>
      </div>
      <div class="akazamWeb" v-if="step === 2">
        <div class="form">
          <div class="formitem flex-box">
            <div class="titlesss flex-box">
              <div class="color"></div>
              <div class="text">分配策略</div>
            </div>
            <div class="rightBox">
              <div class="list flex-box-between">
                <div class="leftLabel">专用网络</div>
                <div class="rightCore flex-box">
                  <div class="son long">
                    <div class="leftword">vpc-default</div>
                  </div>
                </div>
              </div>
              <div class="list flex-box-between">
                <div class="leftLabel">交换机</div>
                <div class="rightCore flex-box">
                  <div class="son long">
                    <div class="leftword">vswitch-default</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="formitem flex-box">
            <div class="titlesss flex-box">
              <div class="color"></div>
              <div class="text">公网宽带</div>
            </div>
            <div class="rightBox">
              <div class="list flex-box-between">
                <div class="leftLabel">&nbsp;</div>
                <div class="rightCore">
                  <div class="checkbox flex-box">
                    <div class="icon"></div>
                    <div class="label">启用公网宽带</div>
                  </div>
                  <div class="flex-box">
                    <div class="son choose">
                      <div class="word">按固定带宽</div>
                    </div>
                  </div>
                  <div class="slider" style="margin-bottom: 10px">
                    <el-slider
                      v-model="datastep.speed"
                      :marks="marks"
                      :format-tooltip="formatTooltip"
                      :min="1"
                      :max="100"
                    >
                    </el-slider>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="formitem flex-box">
            <div class="titlesss flex-box">
              <div class="color"></div>
              <div class="text">存储</div>
            </div>
            <div class="rightBox">
              <div class="list flex-box-between">
                <div class="leftLabel">系统盘</div>
                <div class="rightCore flex-box">
                  <div class="son">
                    <div class="select">
                      <el-select
                        v-model="datastep.options_type_value"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="(item, index) in datastep.options_type"
                          :key="index"
                          :label="item"
                          :value="item"
                        >
                        </el-option>
                      </el-select>
                    </div>
                  </div>
                  <div class="son nobg">
                    <div class="slider">
                      <el-slider
                        v-model="datastep.sysSize"
                        :marks="marks2"
                        :format-tooltip="formatTooltip2"
                        :min="40"
                        :max="500"
                        :step="10"
                      >
                      </el-slider>
                    </div>
                  </div>
                </div>
              </div>
              <div class="list flex-box-between">
                <div class="leftLabel">数据盘</div>
                <div class="rightCore flex-box">
                  <div class="son">
                    <div class="wordadd">
                      <div @click.stop="addSys">添加数据盘</div>
                      <div class="posi">最多只能选择5个数据盘</div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="list flex-box-between"
                v-for="(item, index) in datastep.sysList"
                :key="index"
              >
                <div class="leftLabel"></div>
                <div class="rightCore flex-box">
                  <div class="son">
                    <div class="select">
                      <el-select v-model="item.type" placeholder="请选择">
                        <el-option
                          v-for="(item, index) in datastep.options_type"
                          :key="index"
                          :label="item"
                          :value="item"
                        >
                        </el-option>
                      </el-select>
                    </div>
                  </div>
                  <div class="son nobg">
                    <div class="slider">
                      <el-slider
                        v-model="item.size"
                        :marks="marks3"
                        :format-tooltip="formatTooltip2"
                        :min="10"
                        :max="500"
                        :step="10"
                      >
                      </el-slider>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="footer flex-box-end">
          <!-- <el-button type="primary" @click="endFun(0)">重置应用</el-button> -->
          <el-button type="primary" @click="endFun(1)"
            >上一步：基础配置</el-button
          >
          <el-button type="primary" @click="endFun(3)"
            >下一步：启动配置</el-button
          >
        </div>
      </div>
      <div class="akazamWeb" v-if="step === 3">
        <div class="form" v-if="!page2Finished && !step3loadAni">
          <div class="formitem flex-box">
            <div class="titlesss flex-box">
              <div class="color"></div>
              <div class="text">启动方式</div>
            </div>
            <div class="rightBox">
              <div class="list flex-box-between">
                <div class="leftLabel">&nbsp;</div>
                <div class="rightCore flex-box">
                  <div
                    class="son"
                    :class="datastep.kptype == 'password' ? 'choose' : ''"
                    @click="kptypeFun('password')"
                  >
                    <div class="word">密码登录</div>
                  </div>
                  <div
                    class="son"
                    :class="datastep.kptype == 'keyPair' ? 'choose' : ''"
                    @click="kptypeFun('keyPair')"
                  >
                    <div class="word">keyPair登录</div>
                  </div>
                </div>
              </div>
              <div class="list flex-box-between">
                <div class="leftLabel">&nbsp;</div>
                <div class="rightCore flex-box">
                  <div
                    class="son longnopadding"
                    v-if="datastep.kptype == 'keyPair'"
                  >
                    <div class="select">
                      <el-select
                        v-model="datastep.options_keypair_value"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="(item, index) in datastep.options_keypair"
                          :key="index"
                          :label="item"
                          :value="item"
                        >
                        </el-option>
                      </el-select>
                    </div>
                  </div>
                  <div
                    class="son longnopadding"
                    v-if="datastep.kptype == 'password'"
                  >
                    <div class="input">
                      <el-input
                        v-model="datastep.username"
                        placeholder="请输入账号"
                      ></el-input>
                    </div>
                  </div>
                  <div
                    class="son longnopadding"
                    v-if="datastep.kptype == 'password'"
                  >
                    <div class="input">
                      <el-input
                        v-model="datastep.password"
                        type="password"
                        placeholder="请输入密码"
                      ></el-input>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="formitem flex-box">
            <div class="titlesss flex-box">
              <div class="color"></div>
              <div class="text">安全组</div>
            </div>
            <div class="rightBox">
              <div class="list flex-box-between">
                <div class="leftLabel">&nbsp;</div>
                <div class="rightCore flex-box">
                  <div class="son choose">
                    <div class="word">选择安全组</div>
                  </div>
                </div>
              </div>
              <div class="list flex-box-between">
                <div class="leftLabel">&nbsp;</div>
                <div class="rightCore flex-box">
                  <div class="son longnopadding">
                    <div class="select">
                      <el-select
                        v-model="datastep.options_safity_value"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="(item, index) in datastep.options_safity"
                          :key="index"
                          :label="item"
                          :value="item"
                        >
                        </el-option>
                      </el-select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="formitem flex-box">
            <div class="titlesss flex-box">
              <div class="color"></div>
              <div class="text">主机信息</div>
            </div>
            <div class="rightBox">
              <div class="list flex-box-between">
                <div class="leftLabel">主机名称</div>
                <div class="rightCore flex-box">
                  <div class="son longnopadding">
                    <div class="input">
                      <el-input
                        v-model="datastep.hostname"
                        placeholder="请输入"
                      ></el-input>
                    </div>
                  </div>
                </div>
              </div>
              <div class="list flex-box-between">
                <div class="leftLabel">主机数量</div>
                <div class="rightCore flex-box">
                  <div class="son longnopadding">
                    <div class="select">
                      <el-select
                        v-model="datastep.options_nums_value"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="(item, index) in datastep.options_nums"
                          :key="index"
                          :label="item"
                          :value="item"
                        >
                        </el-option>
                      </el-select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="footer flex-box-end">
            <el-button type="primary" @click="s2Start()">启动</el-button>
          </div>
        </div>
        <div class="loadcore" v-if="step3loadAni && !page2Finished">
          <div class="icon"><i class="el-icon-loading"></i></div>
          <div class="text">开通中</div>
        </div>
        <div class="form" v-if="page2Finished">
          <div class="formitem">
            <div class="titlesss flex-box">
              <div class="color"></div>
              <div class="text">资源开通完成</div>
            </div>
            <div class="tables">
              <div class="top flex-box-between">
                <div class="name">probe-dx-sz-0419888</div>
                <div class="buttons flex-box">
                  <div>远程连接</div>
                  <div>重启</div>
                  <div>停止</div>
                  <div class="w">启动</div>
                  <div>重置密码</div>
                  <div>更多</div>
                </div>
              </div>
              <div class="guige">
                {{ datastep.options_nums_value }}{{ datastep.cputype }} |
                {{ twotablesData.io }}GB
                {{
                  datastep.cputype == "vCPU"
                    ? datastep.options_vcpu_value
                    : datastep.options_gpu_value
                }}
                {{ datastep.options_os_value }}
              </div>
              <div class="lv flex-box">
                <div class="label">地域</div>
                <div class="value">
                  {{ datastep.options1_value }} - {{ datastep.options2_value }}
                </div>
              </div>
              <div class="lv flex-box">
                <div class="label">创建时间</div>
                <div class="value">{{ createTime }}</div>
              </div>
              <div class="lv flex-box">
                <div class="label">IP地址:</div>
                <div class="value">
                  <p>*************(弾性IP) {{ datastep.speed }}M</p>
                  <p>************* (私有IP)</p>
                </div>
              </div>
              <div class="lv flex-box">
                <div class="label">到期时间</div>
                <div class="value">{{ overTime }}到期</div>
              </div>
            </div>
            <!-- <div class="twotables flex-box">
              <div class="son flex-box">
                <div class="label">主机名称:</div>
                <div class="value">{{ datastep.hostname }}</div>
              </div>
              <div class="son flex-box">
                <div class="label">主机数量:</div>
                <div class="value">{{ datastep.options_nums_value }}</div>
              </div>
              <div class="son flex-box">
                <div class="label">规格:</div>
                <div class="value">
                  {{
                    datastep.cputype == "vCPU"
                      ? datastep.options_vcpu_value
                      : datastep.options_gpu_value
                  }}
                </div>
              </div>
              <div class="son flex-box">
                <div class="label">带宽:</div>
                <div class="value">{{ datastep.speed }}M</div>
              </div>
              <div class="son flex-box">
                <div class="label">vCPUs:</div>
                <div class="value">{{ twotablesData.cpu }}核</div>
              </div>
              <div class="son flex-box">
                <div class="label">内存:</div>
                <div class="value">{{ twotablesData.io }}GB</div>
              </div>
              <div class="son flex-box" v-if="datastep.cputype != 'vCPU'">
                <div class="label">GPU型号:</div>
                <div class="value">{{ twotablesData.gpu }}</div>
              </div>
              <div class="son flex-box" v-if="datastep.cputype != 'vCPU'">
                <div class="label">显存:</div>
                <div class="value">{{ twotablesData.xc }}GB</div>
              </div>
              <div class="son flex-box">
                <div class="label">购买时长:</div>
                <div class="value">{{ datastep.options3_value }}</div>
              </div>
              <div class="son flex-box">
                <div class="label">价格(元):</div>
                <div class="value">{{ twotablesData.price }}</div>
              </div>
            </div> -->
            <div class="footer flex-box-end">
              <!-- <el-button type="primary" @click="endFun(0)">重置应用</el-button> -->

              <el-button type="primary" @click="endFun(2)"
                >上一步：网络&存储</el-button
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- step1 -->
    <div class="relative">
      <step99
        v-if="step == 99"
        @endFun="endFun"
        @stepEnd="stepEnd"
        :propsdata="datastep"
      ></step99>
      <step0
        v-if="step == 1"
        ref="step0"
        @endFun="endFun"
        @stepEnd="stepEnd"
        :propsdata="datastep"
      ></step0>
      <step1
        v-if="step == 2"
        @endFun="endFun"
        @stepEnd="stepEnd"
        :propsdata="datastep"
        ref="step1"
        @step2Loading="step2Loading"
      ></step1>
      <step2
        v-if="step == 3"
        @endFun="endFun"
        @stepEnd="stepEnd"
        :propsdata="datastep"
        ref="step2"
        @step3Loading="step3Loading"
      ></step2>
    </div>
  </div>
</template>

<script>
import headerCommon from "../components/header/Index";
import step99 from "../components/1920work4/step99.vue";
import step0 from "../components/1920work4/step0.vue";
import step1 from "../components/1920work4/step1.vue";
import step2 from "../components/1920work4/step2.vue";
import { getWork } from "@/api/common";

export default {
  components: {
    headerCommon,
    step99,
    step0,
    step1,
    step2,
  },
  data() {
    return {
      value: 10,
      marks: {
        1: "1M",
        100: "100M",
      },
      marks2: {
        40: "40GB",
        500: "500GB",
      },
      marks3: {
        10: "10GB",
        500: "500GB",
      },
      menuIndex: null,
      showMenu: false,
      step: 0,
      showBg: false,
      showItem: false,
      isAuto: null,
      workTimer: null,
      isfull: false,
      step3loadAni: false,
      datastep: {
        loadingCl: true,
        s99Options_type: ["vCPU", "GPU"],
        s99Options_type_value: "GPU",
        s99Options_input1_value: "8",
        s99Options_input2_value: "64",
        s99Options_input3: ["NVIDIA V100"],
        s99Options_input3_value: "NVIDIA V100",
        s99Options_input4_value: "1",
        s99Options_input5: ["互联网连接"],
        s99Options_input5_value: "互联网连接",
        options1: ["天翼云华东1(4.0)", "天翼云苏州(合营)", "华为云上海1"],
        options1_value: "天翼云苏州(合营)",
        options2: ["可用区1", "可用区2", "可用区3"],
        options2_value: "可用区1",
        options3: [
          "1个月",
          "2个月",
          "3个月",
          "4个月",
          "5个月",
          "6个月",
          "7个月",
          "8个月",
          "9个月",
          "1年",
          "2年",
          "3年",
        ],
        options3_value: "1个月",
        options_cputype: ["vCPU", "GPU"],
        cputype: "GPU",
        options_three: ["智能推荐", "绿色优先", "成本优先"],
        options_three_value: "智能推荐",
        options_vcpu: [
          { name: "s7n.medium.4", cpu: 1, io: 4, price: 138 },
          { name: "s7n.large.2", cpu: 2, io: 4, price: 184 },
          { name: "s7n.large.4", cpu: 2, io: 8, price: 249 },
          { name: "s7n.xlarge.2", cpu: 4, io: 8, price: 341 },
          { name: "s7n.2xlarge.2", cpu: 8, io: 16, price: 654 },
          { name: "s3.4xlarge.2", cpu: 16, io: 32, price: 899 },
        ],
        options_vcpu_value: "s7n.xlarge.2",
        options_gpu: [
          {
            name: "g5.4xlarge.4",
            cpu: 16,
            io: 64,
            gpu: "NVIDIA V100 *1",
            price: 5291,
            xc: 8,
          },
          {
            name: "g6.9xlarge.7",
            cpu: 36,
            io: 256,
            gpu: "NVIDIA T4  *1",
            price: 7828,
            xc: 16,
          },
          {
            name: "p2v.2xlarge.8",
            cpu: 8,
            io: 64,
            gpu: "NVIDIA V100 *1",
            price: 7918,
            xc: 16,
          },
          {
            name: "p2v.4xlarge.8",
            cpu: 16,
            io: 128,
            gpu: "NVIDIA V100 *2",
            price: 15808,
            xc: 16,
          },
          {
            name: "p2v.8xlarge.8",
            cpu: 32,
            io: 256,
            gpu: "NVIDIA V100 *4",
            price: 31587,
            xc: 16,
          },
          {
            name: "p2v.16xlarge.8",
            cpu: 64,
            io: 512,
            gpu: "NVIDIA V100 *8",
            price: 63146,
            xc: 16,
          },
        ],
        options_gpu_value: "p2v.4xlarge.8",
        options_os: [
          "CentoS 7.5 with HSS",
          "CentoS 7.6 with HSS",
          "CentoS 8.2 with HSS",
          "Ubuntu14.04 64位",
          "Ubuntu16.04 64位",
          "Ubuntu20.04 64位",
          "Windows 2008 Enterprise R2 64位中文版",
          "Windows 2008 Enterprise R2 64位英文版",
          "Windows 2016 DataCenter 64位中文版",
        ],
        options_os_value: "CentoS 7.5 with HSS",
        options_type: ["SSD", "高IO"],
        options_type_value: "SSD",
        options_keypair: ["keyPair-4311"],
        options_keypair_value: "keyPair-4311",
        options_safity: ["default(4686edac-8536-487c-a433-ac3e8319cc3d)"],
        options_safity_value: "default(4686edac-8536-487c-a433-ac3e8319cc3d)",
        options_nums: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
        options_nums_value: 1,
        hostname: "instance1kcrox",
        username: "root",
        password: "!@#$admin.",
        kptype: "keyPair",
        speed: 10,
        sysSize: 40,
        sysList: [],
        options4: ["离线存储", "实时"],
        options5: ["3DS MAX"],
        input1: "3D应用",
        input2: "8",
        input3: "64",
        input4: "RTX3060",
        input5: "10",
        input6: "专线网络连接",
        input7: "100",
        input8: "离线存储",
        input9: "3DS MAX",
        strategyTabs: ["智能分配", "指定资源池"],
        strategy: "指定资源池",
        strategyList: [
          {
            name: "天翼云-上海一",
            spec: "6C16G 4*12.74TFlops",
            desc: "2.26元/路/时",
            price: "0.18元/GB",
            gpu: 15,
            cpu: 18,
            ram: 35,
            strategy: ["效率优先", "智能分配"],
            min: 0,
            max: 200,
            use: 120,
            num1: 1,
            num2: 1,
            num3: 6,
            num4: 0,
            delay: 1,
          },
          {
            name: "上海气象局专属资源池",
            spec: "8C16G 12TFSP及以上",
            desc: "1.25元/路/时",
            price: "0.15元/GB",
            gpu: 20,
            cpu: 22,
            ram: 26,
            strategy: ["指定资源池", "智能分配"],
            min: 0,
            max: 400,
            use: 210,
            num1: 0,
            num2: 0,
            num3: 8,
            num4: 0,
            delay: 0.5,
          },
          {
            name: "天翼云-上海二",
            spec: "8C16G 12TFSP及以上  ",
            desc: "1.40元/路/时",
            price: "0.15元/GB",
            gpu: 32,
            cpu: 23,
            ram: 28,
            strategy: ["指定资源池", "智能分配"],
            min: 0,
            max: 400,
            use: 80,
            num1: 1,
            num2: 2,
            num3: 2,
            num4: 0,
            delay: 1,
          },
          {
            name: "天翼云-上海三",
            spec: "6C16G 4*12.74TFlops",
            desc: "2.50元/路/时",
            price: "0.2元/GB",
            gpu: 17,
            cpu: 14,
            ram: 21,
            strategy: ["指定资源池", "效率优先"],
            min: 0,
            max: 300,
            use: 180,
            num1: 0,
            num2: 0,
            num3: 1,
            num4: 0,
            delay: 1,
          },
          {
            name: "天翼云-厦门",
            spec: "8C16G 12TFSP及以上",
            desc: "2.50元/路/时",
            price: "0.2元/GB",
            gpu: 40,
            cpu: 31,
            ram: 18,
            strategy: ["效率优先"],
            min: 0,
            max: 200,
            use: 140,
            num1: 0,
            num2: 0,
            num3: 5,
            num4: 0,
            delay: 1,
          },
        ],
        businesscity: "上海气象局专属资源池",
        otherNow: {
          name: "上海气象局专属资源池",
          spec: "8C16G 12TFSP及以上",
          desc: "1.25元/路/时",
          price: "0.15元/GB",
          gpu: 20,
          cpu: 22,
          ram: 26,
          strategy: ["指定资源池", "智能分配"],
          min: 0,
          max: 400,
          use: 210,
          num1: 0,
          num2: 0,
          num3: 8,
          num4: 0,
          delay: 0.5,
        },
        otherTwo: [
          {
            name: "天翼云-上海二",
            spec: "8C16G 12TFSP及以上  ",
            desc: "1.40元/路/时",
            price: "0.15元/GB",
            gpu: 32,
            cpu: 23,
            ram: 28,
            strategy: ["指定资源池", "智能分配"],
            min: 0,
            max: 400,
            use: 80,
            num1: 1,
            num2: 2,
            num3: 2,
            num4: 0,
            delay: 1,
          },
          {
            name: "天翼云-上海三",
            spec: "6C16G 4*12.74TFlops",
            desc: "2.50元/路/时",
            price: "0.2元/GB",
            gpu: 17,
            cpu: 14,
            ram: 21,
            strategy: ["指定资源池", "效率优先"],
            min: 0,
            max: 300,
            use: 180,
            num1: 0,
            num2: 0,
            num3: 1,
            num4: 0,
            delay: 1,
          },
        ],
        options3_1: ["Windows", "Linux"],
        options3_2: ["3DS MAX", "Cinema 4D", "Blender"],
        options3_3: [
          "vray6.00.01",
          "vray 5.20.06",
          "vray 5.20.03",
          "Octane",
          "Arnold",
        ],
        options3_1_val: "Windows",
        options3_2_val: "3DS MAX",
        options3_3_val: "vray6.00.01",
        step4Loading: false,
        step4Finished: false,
      },
      step2loadAni: false,

      showWeather: false,
      page2Finished: false,
      twotablesData: {},
      createTime: "",
      overTime: "",
    };
  },
  filters: {},
  mounted() {
    // setTimeout(() => {
    //   this.step = 0
    // }, 1000);
    // this.endFun(3)
  },
  created() {},
  methods: {
    formatDate(timestamp) {
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`;
    },
    formatDate2(timestamp) {
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}年${month}月${day}日 23:59:59`;
    },
    s2Start() {
      this.$refs.step2.step2Start();
      this.createTime = this.formatDate(new Date());
      let timeunit = this.datastep.options3_value.split("")[0];
      if (this.datastep.options3_value.indexOf("年") > -1) {
        timeunit *= 12;
      }
      console.log(timeunit, 'timeunit')
      let d = new Date();
      this.overTime = this.formatDate2(
        new Date().setMonth(d.getMonth() + Number(timeunit))
      );
      console.log(this.overTime);
      setTimeout(() => {
        this.$refs.step2.step2End();
      }, 3000);
    },
    options99TypeFun(val) {
      if (this.datastep.s99Options_type_value != val) {
        this.datastep.s99Options_type_value = val;
      }
    },
    optionsThreeFun(val) {
      if (this.datastep.options_three_value != val) {
        this.datastep.options_three_value = val;
        if (val == "智能推荐") {
          this.datastep.options_vcpu_value = this.datastep.options_vcpu[3].name;
          this.datastep.options_gpu_value = this.datastep.options_gpu[3].name;
        }
        if (val == "绿色优先") {
          this.datastep.options_vcpu_value = this.datastep.options_vcpu[1].name;
          this.datastep.options_gpu_value = this.datastep.options_gpu[1].name;
        }
        if (val == "成本优先") {
          this.datastep.options_vcpu_value = this.datastep.options_vcpu[0].name;
          this.datastep.options_gpu_value = this.datastep.options_gpu[0].name;
        }
        this.datastep.loadingCl = true;
        this.$refs.step0.resetFun();
        setTimeout(() => {
          this.datastep.loadingCl = false;
          this.$refs.step0.init2();
        }, 3000);
      }
    },
    kptypeFun(val) {
      if (this.datastep.kptype != val) {
        this.datastep.kptype = val;
      }
    },
    addSys() {
      if (this.datastep.sysList && this.datastep.sysList.length < 5)
        this.datastep.sysList.push({ type: "SSD", size: 40 });
    },
    options1_valueFun(val) {
      if (this.datastep.options1_value != val) {
        this.datastep.options1_value = val;
      }
    },
    options2_valueFun(val) {
      if (this.datastep.options2_value != val) {
        this.datastep.options2_value = val;
      }
    },
    cputypeFun(val) {
      if (this.datastep.cputype != val) {
        this.datastep.cputype = val;
      }
    },
    formatTooltip(val) {
      return val + "M";
    },
    formatTooltip2(val) {
      return val + "GB";
    },
    step4Finished() {
      this.datastep.step4Finished = true;
    },
    step4Loading() {
      this.datastep.step4Loading = false;
    },
    businesscityInput(e) {
      let businesscity = this.datastep.businesscity;
      let strategy = this.datastep.strategy;
      if (strategy == "智能分配") {
        if (businesscity == "天翼云-上海") {
          this.datastep.otherTwo = [
            this.datastep.strategyList[1],
            this.datastep.strategyList[2],
          ];
        } else if (businesscity == "天翼云-雅安") {
          this.datastep.otherTwo = [
            this.datastep.strategyList[0],
            this.datastep.strategyList[2],
          ];
        } else if (businesscity == "天翼云-贵安") {
          this.datastep.otherTwo = [
            this.datastep.strategyList[0],
            this.datastep.strategyList[1],
          ];
        }
      } else if (strategy == "效率优先") {
        if (businesscity == "天翼云-上海") {
          this.datastep.otherTwo = [
            this.datastep.strategyList[3],
            this.datastep.strategyList[4],
          ];
        } else if (businesscity == "天翼云-扬州") {
          this.datastep.otherTwo = [
            this.datastep.strategyList[0],
            this.datastep.strategyList[4],
          ];
        } else if (businesscity == "天翼云-厦门") {
          this.datastep.otherTwo = [
            this.datastep.strategyList[0],
            this.datastep.strategyList[3],
          ];
        }
      } else if (strategy == "指定资源池") {
        if (businesscity == "天翼云-雅安") {
          this.datastep.otherTwo = [
            this.datastep.strategyList[2],
            this.datastep.strategyList[3],
          ];
        } else if (businesscity == "天翼云-贵安") {
          this.datastep.otherTwo = [
            this.datastep.strategyList[1],
            this.datastep.strategyList[3],
          ];
        } else if (businesscity == "天翼云-扬州") {
          this.datastep.otherTwo = [
            this.datastep.strategyList[1],
            this.datastep.strategyList[2],
          ];
        }
      }
      this.datastep.otherNow = this.datastep.strategyList.filter(
        (item) => item.name == businesscity
      )[0];
      this.$refs.step1.initanchor();
    },
    strategyChoose(val) {
      return false;
      if (this.datastep.strategy != val) {
        this.datastep.strategy = val;
        // if (val == '指定资源池') {
        //   this.datastep.businesscity = '天翼云-雅安'
        // } else {
        //   this.datastep.businesscity = '天翼云-上海'
        // }
        // this.businesscityInput()
      }
    },
    goWork(index) {
      this.showMenu = false;
      this.showIframe = true;
      if (index === 4) {
        this.$router.push({
          path: "/page12",
        });
      } else if (index === 8) {
        this.$router.push({
          path: "/page2",
        });
      }
    },
    fullFun() {
      this.isfull = !this.isfull;
    },
    step2Loading() {
      this.step2loadAni = true;
    },
    step3Loading() {
      this.step3loadAni = true;
    },
    stepEnd(val) {
      if (this.isAuto) {
        // this.isfull = true
        this.setGetwork();
      }
      if (val == 3) {
        this.page2Finished = true;
        console.log(this.datastep);
        let data = this.datastep;
        let dataObj = [];
        if (data.cputype == "vCPU") {
          dataObj = data.options_vcpu.filter(
            (item) => item.name == data.options_vcpu_value
          );
        } else {
          dataObj = data.options_gpu.filter(
            (item) => item.name == data.options_gpu_value
          );
        }
        let totalPriceOne = 0;
        let priceBase = dataObj[0].price; // 基础套餐价格
        let sysDataPrice = 0;
        if (data.options_type_value == "SSD") {
          sysDataPrice = ((data.sysSize - 40) / 10) * 7;
        } else {
          sysDataPrice = ((data.sysSize - 40) / 10) * 4;
          // 高IO 减12
          priceBase -= 12;
        }
        totalPriceOne += priceBase;
        totalPriceOne += sysDataPrice;
        let dataprice = 0;
        data.sysList.forEach((element) => {
          console.log(element, "element");
          if (element.type == "SSD") {
            dataprice += (element.size / 10) * 7;
          } else {
            dataprice += (element.size / 10) * 4;
          }
        });
        totalPriceOne += dataprice;
        totalPriceOne += data.speed * 20;
        let totalPrice = 0;
        let timeunit = data.options3_value.split("")[0];
        if (data.options3_value.indexOf("年") > -1) {
          timeunit *= 12;
        }
        totalPrice = totalPriceOne * timeunit * data.options_nums_value;
        this.twotablesData = {
          cpu: dataObj[0].cpu,
          io: dataObj[0].io,
          gpu: dataObj[0].gpu || "-",
          xc: dataObj[0].xc || "-",
          price: totalPrice,
        };
      }
    },
    setGetwork() {
      let _this = this;
      _this.workTimer = setInterval(() => {
        _this._getWork();
      }, 1000);
      _this.GLOBAL.timerArraySet.push(this.workTimer);
    },
    _getWork() {
      getWork().then((res) => {
        if (res.status === 200 && res.result && res.result.step) {
          let step = res.result.step;
          if (step == 6) {
            if (res.result.predictResult) {
              this.predictResult = res.result.predictResult;
            }
          }
          if (step != this.step) {
            this.step = step;
            clearInterval(this.workTimer);
            // this.isfull = false
          }
          if (step == 6) {
            clearInterval(this.workTimer);
            // this.isfull = false
          }
        } else {
        }
      });
    },
    endFun(index) {
      this.step = index;
      if (index == 1) {
        console.log(this.$refs.step0, " this.$refs.step0");
        setTimeout(() => {
          this.datastep.loadingCl = false;
          this.$refs.step0.init2();
        }, 3000);
      }
      if (index == 2) {
        this.page2Finished = false;
        this.step3loadAni = false;
      }
      if (index == 4) {
        this.datastep.step4Loading = true;
        this.datastep.step4Finished = false;
      }
    },
    endFunBefore() {
      this.showWeather = true;
    },
  },
  destroyed() {},
};
</script>

<style lang="scss" scoped>
.page14 {
  .weather {
    .son {
      width: 288px;
      height: 100px;
      background: #414350;
      border-radius: 12px;
      padding: 15px 28px;
      margin-top: 25px;
      box-sizing: border-box;
      border: 2px solid #414350;
      position: relative;
      .choose {
        background-image: url("~@/assets/images/1920/work3/choose.png");
        background-size: cover;
        width: 38px;
        height: 38px;
        position: absolute;
        bottom: -1px;
        right: -1px;
        z-index: 1;
      }
      .info {
        padding-left: 20px;

        p {
          margin: 0;
          font-size: 16px;
          opacity: 0.3;
          padding-top: 5px;
        }
      }
      .icon {
        width: 61px;
        height: 61px;
        img {
          width: 100%;
          height: 100%;
        }
      }

      &.on {
        border-color: rgba(221, 128, 35, 1);
        .info {
          p {
            opacity: 1;
          }
        }
      }
    }
  }

  .bg {
    background-image: url("~@/assets/images/1920/work/step1/listbg.png");
    background-size: cover;
    width: 760px;
    height: 303px;
    position: absolute;
    bottom: 46px;
    left: 0;
    z-index: 0;
    // opacity: 0;

    &.ani {
      animation: transopacity 1s ease-in 1 forwards;
    }
  }

  .openMenu {
    width: 250px;
    height: 32px;
    position: absolute;
    top: 130px;
    left: 40px;
    cursor: pointer;
    opacity: 0;
  }

  .menu {
    box-sizing: border-box;

    .item {
      width: 193px;
      height: 181px;
      background-color: rgba($color: #ffffff, $alpha: 0.1);
      box-sizing: border-box;
      padding-top: 33px;

      img {
        width: 70px;
        height: 70px;
        margin: 0 auto;
        display: block;
      }

      .img1 {
        display: none;
      }

      p {
        text-align: center;
        font-size: 24px;
        color: #ffffff;
      }

      &.on {
        background-color: #28a2ce;
      }

      &.ani {
        animation: transopacity 1s ease-in 1 forwards;
      }

      &:nth-child(8),
      &:nth-child(4) {
        cursor: pointer;

        &:hover {
          background-color: #28a2ce;

          .img2 {
            display: none;
          }

          .img1 {
            display: block;
          }
        }
      }
    }

    &.newMenu {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 642px;
      height: 672px;
      z-index: 999;
      background-color: #3a3c47;
      overflow: hidden;
      box-sizing: border-box;
      padding: 15px;
    }
  }

  .relative {
    position: relative;
    z-index: 2;
    padding-top: 60px;
  }

  .loading {
    position: fixed;
    // background-color: rgba($color: #000000, $alpha: 0.5);
    // width: 100vw;
    // height: 100vh;
    z-index: 98;
    bottom: 0;
    left: 0;
    right: 0;
    box-sizing: border-box;
    padding-bottom: 45vh;
    box-sizing: border-box;

    .icon {
      font-size: 40px;
    }

    .text {
      font-size: 40px;
      margin-left: 30px;
    }
  }
}

.akazamSz {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 642px;
  height: 672px;
  z-index: 99;
  background-color: RGBA(58, 60, 71, 1);
  overflow: hidden;
  box-sizing: border-box;

  .akazamWeb {
    width: 100%;
    box-sizing: border-box;
    padding: 13px;
    height: 100%;
    overflow: hidden;
    overflow-y: scroll;
    padding-bottom: 50px;
    // position: relative;

    .loadcore {
      text-align: center;
      padding-top: 240px;
      color: #ffffff;

      .icon {
        font-size: 80px;
        line-height: 80px;
      }

      .text {
        font-size: 24px;
        margin-top: 20px;
      }
    }

    .down {
      margin-top: 30px;

      .titlebox {
        margin-top: 30px;
        .color {
          width: 4px;
          height: 14px;
          background: #848691;
          margin-right: 7px;
        }

        .text {
          font-size: 16px;
          font-weight: bold;
          line-height: 16px;
        }
      }

      .item {
        width: 600px;
        background: rgba(58, 59, 71, 1);
        border-radius: 12px;
        box-sizing: border-box;
        padding: 15px 20px;
        margin-top: 25px;
        border: 1px solid rgba(79, 81, 96, 1);
        .icon {
          width: 90px;
          height: 90px;
          margin-top: 10px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .textcore {
          width: 450px;
          .desc {
            color: #999999;
            margin-top: 10px;
            font-size: 14px;
          }
        }
        .tags {
          margin-top: 10px;
          .son {
            height: 24px;
            background: #5c6471;
            border-radius: 2px;
            border: 1px solid #e48724;
            padding: 0 10px;
            font-size: 14px;
            color: #e48724;
            line-height: 24px;
            text-align: center;
            margin-right: 10px;
          }
        }
        &.on {
          cursor: pointer;
        }
      }
      .iconlist {
        margin-top: 20px;
        .son {
          width: 190px;
          height: 64px;
          background: #3a3b47;
          border-radius: 4px;
          border: 1px solid #4f5160;
          box-sizing: border-box;
          padding: 10px;
          margin: 0 10px 15px 0;
          .icon {
            width: 44px;
            height: 44px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .label {
            font-size: 16px;
            color: #ffffff;
            line-height: 14px;
            line-height: 44px;
            padding-left: 18px;
          }
        }
      }
    }

    .imgnav {
      margin-top: 25px;

      .left {
        .item {
          width: 110px;
          height: 236px;
          background: #414350;
          border-radius: 12px;
          box-sizing: border-box;
          padding-top: 70px;

          .son {
            opacity: 0.5;

            .icon {
              width: 58px;
              height: 58px;
              margin: 0 auto;

              img {
                width: 100%;
                height: 100%;
              }
            }

            .text {
              font-size: 16px;
              text-align: center;
              margin-top: 12px;
            }
          }
        }
      }

      .right {
        width: 475px;
        margin-left: 15px;

        .item {
          width: 110px;
          height: 110px;
          background: #414350;
          border-radius: 12px;
          box-sizing: border-box;
          padding-top: 10px;
          margin-bottom: 15px;

          .arrow {
            width: 19px;
            height: 32px;
            margin: 0 auto;
            margin-top: 30px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .son {
            opacity: 0.5;

            .icon {
              width: 58px;
              height: 58px;
              margin: 0 auto;

              img {
                width: 100%;
                height: 100%;
              }
            }

            .text {
              font-size: 16px;
              text-align: center;
              margin-top: 12px;
            }
          }

          &:nth-child(3) {
            cursor: pointer;

            .son {
              opacity: 0.95;
            }

            &:hover {
              .son {
                opacity: 1;
              }
            }
          }
        }
      }
    }

    .top {
      .item {
        padding: 7px 20px;
        font-size: 16px;
        background: #40424e;
        border-radius: 4px;
        line-height: 16px;
        margin-right: 10px;

        &.on {
          font-weight: bold;
          background: rgba(255, 102, 51, 1);
        }
      }
    }

    .footer {
      width: 100%;
      height: 44px;
      background: #414350;
      border: 1px solid #262832;
      border-radius: 0px 0px 5px 5px;
      position: absolute;
      bottom: 0;
      left: 0;
      box-sizing: border-box;
      padding-right: 20px;
      padding-top: 6px;

      .el-button {
        margin-left: 20px;
      }
    }

    .form {
      line-height: 40px;
      font-size: 16px;
      .tables {
        background-color: #414350;
        border-radius: 4px;
        padding: 15px;
        .top {
          .name {
            font-size: 16px;
            font-weight: bold;
            line-height: 16px;
          }
          .buttons {
            div {
              font-size: 14px;
              height: 14px;
              margin-left: 5px;
              padding-left: 5px;
              color: #006cf8;
              border-left: 1px solid rgba($color: #6e6e6e, $alpha: 0.2);
              line-height: 14px;
              cursor: pointer;
              &.w {
                color: rgba($color: #fff, $alpha: 0.5);
                cursor: default;
              }
              &:first-child {
                border-left: none;
              }
            }
          }
        }
        .guige {
          font-size: 14px;
          color: #fff;
          line-height: 14px;
          margin-top: 10px;
          margin-bottom: 20px;
        }
        .lv {
          padding: 10px 0;
          font-size: 14px;
          line-height: 14px;
          .label {
            color: rgba($color: #fff, $alpha: 0.5);
            width: 80px;
          }
          .value {
            color: #fff;
            p {
              padding: 0;
              margin: 0;
              line-height: 14px;
              margin-bottom: 10px;
            }
          }
        }
      }
      .twotables {
        background-color: #414350;
        border-radius: 4px;
        padding: 15px 0;
        .son {
          width: 50%;
          box-sizing: border-box;
          padding: 0 25px;
          .label {
            width: 30%;
            color: #ccc;
          }
          .value {
            width: 70%;
          }
        }
      }
      .tableOne {
        width: 100%;
        background-color: #414350;
        padding: 10px 15px;
        font-size: 16px;
        line-height: 16px;
        border-radius: 4px;
        box-sizing: border-box;

        .list {
          .item {
            padding: 10px 0;

            &:first-child {
              color: #cccccc;
              border-bottom: 1px solid rgba($color: #ffffff, $alpha: 0.2);
            }

            div {
              &:nth-child(1) {
                width: 120px;
              }

              &:nth-child(2) {
                width: 120px;
              }

              &:nth-child(3) {
                width: 80px;
              }

              &:nth-child(4) {
                width: 60px;
              }
            }
          }
        }
      }

      .formitem {
        margin: 8px 0;

        .titlesss {
          width: 110px;
          margin-top: 5px;
          .color {
            width: 4px;
            height: 14px;
            background: #848691;
            margin-top: 13px;
            margin-right: 8px;
          }

          .text {
            font-size: 16px;
            font-weight: bold;
          }
        }
        .rightBox {
          .list {
            width: 490px;
            margin: 5px 0;
            .leftLabel {
              width: 100px;
              font-size: 16px;
            }
            .rightCore {
              width: 390px;
              .son {
                width: 170px;
                margin: 0 0 10px 20px;
                background-color: rgba(65, 67, 80, 1);
                border-radius: 4px;
                cursor: pointer;
                box-sizing: border-box;

                .word {
                  text-align: center;
                }
                .leftword {
                  text-align: left;
                }
                .wordadd {
                  position: relative;
                  color: #ff6633;
                  text-align: center;
                  .posi {
                    position: absolute;
                    top: 0px;
                    right: -160px;
                    font-size: 14px;
                    color: #999999;
                  }
                }
                .radiocore {
                  .icon {
                    background-image: url("~@/assets/images/1920/work4/radio.png");
                    background-size: cover;
                    width: 19px;
                    height: 19px;
                    margin: 12px 10px 0 15px;
                    &.on {
                      background-image: url("~@/assets/images/1920/work4/radio-on.png");
                    }
                  }
                  .value {
                  }
                }

                &.long {
                  width: 360px;
                  padding: 0 20px;
                }
                &.longnopadding {
                  width: 360px;
                }
                &.choose {
                  background-color: #5e606b;
                }
                &.nobg {
                  background: none;
                }
              }
              .checkbox {
                .icon {
                  background-image: url("~@/assets/images/1920/work4/checkbox-checked.png");
                  background-size: cover;
                  width: 20px;
                  height: 20px;
                  margin: 11px 10px 0 20px;
                }
              }
              .slider {
                padding: 0 30px;
                transform: translateY(-10px);
                // padding-bottom: 10px;
              }
            }
          }
        }

        .sonBox {
          padding-left: 18px;

          .son {
            padding-bottom: 16px;

            .longtitle {
              width: calc(642px - 180px);
              background-color: #414350;
              border-radius: 4px;
              box-sizing: border-box;
              padding: 0 10px;

              .r {
                width: 73px;
                height: 26px;
                background: #4d4f5b;
                border-radius: 4px;
                line-height: 26px;
                margin-top: 6px;
                text-align: center;
              }
            }

            .elcore {
              width: calc(642px - 290px);

              .tables {
                width: calc(100% - 20px);
                background-color: #414350;
                padding: 10px;
                font-size: 16px;
                line-height: 16px;

                .title {
                  color: #cccccc;
                  padding-bottom: 15px;
                  border-bottom: 1px solid rgba($color: #ffffff, $alpha: 0.2);
                }

                .list {
                  .item {
                    padding: 10px 0;

                    .le {
                      .check {
                        margin-right: 10px;
                      }
                    }

                    // &:first-child {
                    //   color: #CCCCCC;
                    // }
                  }
                }
              }

              .load {
                padding-top: 20px;
                font-size: 16px;
                line-height: 20px;

                i {
                  display: block;
                  width: 20px;
                  height: 20px;
                  font-size: 20px;
                  margin-right: 10px;
                }

                &.on {
                  color: #05cbc4;
                }
              }

              .elAni {
                opacity: 0;

                &.ani {
                  animation: transopacity 1s linear 1 forwards;
                }
              }

              .namebox {
                width: 100%;
                height: 34px;
                background: #414350;
                border-radius: 4px;
                box-sizing: border-box;
                padding: 0 8px;

                .word {
                  line-height: 34px;
                }

                .rt {
                  width: 73px;
                  height: 26px;
                  background: #4d4f5b;
                  border-radius: 4px;
                  line-height: 26px;
                  margin-top: 4px;
                  text-align: center;
                }
              }

              .tabs {
                .item {
                  width: 30%;
                  height: 34px;
                  background: #414350;
                  border-radius: 4px;
                  text-align: center;
                  line-height: 34px;
                  cursor: pointer;
                  margin-right: 10px;

                  &.on {
                    background: #5e606b;
                  }
                }
              }

              &.on {
                border-radius: 4px;
                width: calc(642px - 160px);
              }
              &.onon {
                border-radius: 4px;
                width: calc(642px - 180px);
              }
            }

            .text {
              width: 100px;
            }
          }
        }
      }
    }
  }

  .full {
    position: absolute;
    left: 7px;
    bottom: 7px;
    cursor: pointer;
    z-index: 2;
    background-image: url("~@/assets/images/1920/ICON.png");
    width: 28px;
    height: 28px;
    background-size: cover;

    &.on {
      // background-image: url('~@/assets/images/page4/screen.png');
    }

    // background-color: rgba($color: #2A2A2D, $alpha: 0.5);
    // background-size: 80%;
    // background-position: center center;
    // border-radius: 8px;
    // background-repeat: no-repeat;
  }

  &.full {
    width: 1056px;
    height: 990px;

    .akazamWeb {
      .form {
        .formitem {
          .sonBox {
            .son {
              .elcore {
                width: calc(1056px - 260px);
              }
            }
          }
        }
      }
    }
  }
}

.longtables {
  width: 100%;
  background: #414350;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 10px;
  .label {
    font-size: 16px;
    color: rgba(204, 204, 204, 1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }
  .value {
    font-size: 16px;
    color: rgba(255, 255, 255, 1);
  }
  .w1 {
    width: 180px;
  }
  .w2 {
    width: 300px;
  }
  .w3 {
    width: 100px;
  }
}

@keyframes transopacity {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
</style>

<style lang="scss">
.akazamSz {
  .el-input,
  .el-select {
    width: 100%;
  }

  .el-input,
  .el-select,
  .el-radio,
  .el-button {
    font-size: 16px;
  }

  .el-input-group__append {
    background-color: #414350;
    color: #999999;
  }

  .el-input__inner {
    background-color: #414350;
  }

  .el-button {
    padding: 0 10px;
    margin: 0;
    height: 30px;
    line-height: 30px;
  }

  .elradio {
    .el-radio {
      display: block;
      margin-top: 20px;
    }
  }
  .el-loading-spinner i {
    font-size: 40px;
    color: #fff;
  }
  .el-loading-spinner .el-loading-text {
    font-size: 14px;
    color: #fff;
  }
}
</style>
