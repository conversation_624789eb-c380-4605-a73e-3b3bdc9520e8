{"name": "power", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build:stage": "vue-cli-service build --mode staging", "build:prod": "vue-cli-service build --mode production", "lint": "vue-cli-service lint"}, "dependencies": {"@observablehq/runtime": "^5.5.1", "axios": "^1.5.0", "compression-webpack-plugin": "5.0.2", "core-js": "^3.6.5", "d3": "^7.8.3", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "element-ui": "^2.15.14", "image-mosaic": "^1.0.4", "postcss-px2rem": "^0.3.0", "px2rem-loader": "^0.1.9", "three": "^0.141.0", "tween": "^0.9.0", "video.js": "^8.5.2", "vue": "^2.6.11", "vue-circleprogressbar": "^1.4.0", "vue-router": "^3.2.0", "vue-seamless-scroll": "^1.1.23", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.19", "@vue/cli-plugin-eslint": "~4.5.19", "@vue/cli-plugin-router": "~4.5.19", "@vue/cli-plugin-vuex": "~4.5.19", "@vue/cli-service": "~4.5.19", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "node-sass": "^6.0.1", "sass-loader": "^10.0.0", "vue-template-compiler": "^2.6.11"}}