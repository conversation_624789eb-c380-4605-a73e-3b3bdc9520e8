<template>
  <div class="pageWork1 flex-box-between">
    <div class="leftStep flex-box">
      <div class="stepList">
        <div class="item on al" @click="endFun(1)">基础配置</div>
        <div class="arraw on al"></div>
        <div class="item on">资源池分配</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(3)">运行设置</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(4)">模型训练</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(5)">推理部署</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(6)">访问验证</div>
      </div>
      <div>
        <div class="wordList">
          <p :class="wordAni1 ? 'ani' : ''">应用作业1-智算作业-图像分类学习</p>
          <p :class="wordAni2 ? 'ani' : ''" class="flex-box">
            <i v-if="iconAni1" class="el-icon-loading"></i>
            <img v-if="iconAni2" src="~@/assets/images/page4/step1/success.png" alt="">
            {{ word1 }}
          </p>
          <p :class="wordAni3 ? 'ani' : ''">分配资源池：华为云-上海一</p>
        </div>
        <div class="wordList">
          <p :class="wordAni4 ? 'ani' : ''">应用作业2-通算作业-图像应用Build</p>
          <p :class="wordAni5 ? 'ani' : ''" class="flex-box">
            <i v-if="iconAni3" class="el-icon-loading"></i>
            <img v-if="iconAni4" src="~@/assets/images/page4/step1/success.png" alt="">
            {{ word1_2 }}
          </p>
          <p :class="wordAni6 ? 'ani' : ''">分配资源池：天翼云-内蒙3(合营)</p>
        </div>
      </div>
    </div>
    <div class="rightBox flex-box-between">
      <div class="centerCore" :class="centerOpa ? 'opa' : ''">
        <div class="topList" v-show="!showWorkSecond">
          <div class="item flex-box-between" :class="ccAni1 ? 'ani' : ''">
            <div class="leftIcon flex-box">
              <div class="icon"><img src="~@/assets/images/page4/step1/c-icon-1.png" alt=""></div>
              <div class="text">算力要求</div>
            </div>
            <div class="rightValBox">
              <div class="cc cc1">
                <div class="svg" id="svg1"></div>
                <div class="smallItemList flex-box-between">
                  <div class="smallItem" :class="cwordAni1 ? 'on' : ''">CPU≥8核</div>
                  <div class="smallItem" :class="cwordAni2 ? 'on' : ''">内存≥64G</div>
                  <div class="smallItem" :class="cwordAni3 ? 'on' : ''">GPU: NVIDIA V100</div>
                  <div class="smallItem" :class="cwordAni4 ? 'on' : ''">GPU卡数量≥1个</div>
                  <!-- <div class="smallItem" :class="cwordAni5 ? 'on' : ''">GPU卡数量≥1个</div> -->
                </div>
              </div>
            </div>
          </div>
          <div class="leftLine len1" :class="ccAni2 ? 'ani' : ''"></div>
          <div class="item flex-box-between" :class="ccAni2 ? 'ani' : ''">
            <div class="leftIcon flex-box">
              <div class="icon"><img src="~@/assets/images/page4/step1/c-icon-2.png" alt=""></div>
              <div class="text">分配策略</div>
            </div>
            <div class="rightValBox">
              <div class="cc cc2">
                <div class="sixItem flex-box-between">
                  <div class="sixItemSon" :class="sixItemSonAni1 ? 'on' : ''">
                    <div class="text">智能推荐</div>
                    <div class="point"></div>
                  </div>
                  <div class="sixItemSon" :class="sixItemSonAni2 ? 'on' : ''">
                    <div class="text">绿色优先</div>
                    <div class="point"></div>
                  </div>
                  <div class="sixItemSon" :class="sixItemSonAni3 ? 'on' : ''">
                    <div class="text">成本优先</div>
                    <div class="point"></div>
                  </div>
                  <div class="sixItemSon" :class="sixItemSonAni4 ? 'on' : ''">
                    <div class="text">指定资源池</div>
                    <div class="point"></div>
                  </div>
                  <!-- <div class="sixItemSon" :class="sixItemSonAni5 ? 'on' : ''">
                    <div class="text">就近选择</div>
                    <div class="point"></div>
                  </div>
                  <div class="sixItemSon" :class="sixItemSonAni6 ? 'on' : ''">
                    <div class="text">智能选择</div>
                    <div class="point"></div>
                  </div> -->
                </div>
              </div>
            </div>
          </div>
          <div class="leftLine len2" :class="ccAni3 ? 'ani' : ''"></div>
          <div class="item flex-box-between" :class="ccAni3 ? 'ani' : ''">
            <div class="leftIcon flex-box long">
              <div class="icon"><img src="~@/assets/images/page4/step1/c-icon-3.png" alt=""></div>
              <div class="text">推荐资源池</div>
            </div>
            <div class="rightValBox">
              <div class="cc cc3 flex-box-between">
                <div class="rightValBoxitem" :class="rightValBoxitem1 ? 'hide' : ''">
                  <p>华为云贵阳一</p>
                </div>
                <div class="rightValBoxitem" :class="rightValBoxitem2 ? 'hide' : ''">
                  <p>华为云乌兰察布一</p>
                </div>
                <div class="rightValBoxitem" :class="[rightValBoxitem3 ? 'hide' : '', rightValBoxitemChoose ? 'on' : '']">
                  <p>华为云-上海一</p>
                </div>
                <div class="rightValBoxitem" :class="rightValBoxitem4 ? 'hide' : ''">
                  <p>青浦云湖数据中心</p>
                </div>
                <div class="rightValBoxitem" :class="rightValBoxitem5 ? 'hide' : ''">
                  <p>临港-国产1</p>
                </div>
                <div class="rightValBoxitem" :class="rightValBoxitem6 ? 'hide' : ''">
                  <p>园区算力</p>
                </div>
                <div class="rightValBoxitem" :class="rightValBoxitem7 ? 'hide' : ''">
                  <p>天翼云中卫5</p>
                </div>
                <div class="rightValBoxitem" :class="rightValBoxitem8 ? 'hide' : ''">
                  <p>阿里云华北3</p>
                </div>
                <div class="bg"></div>
              </div>
            </div>
          </div>
          <div class="leftLine len3" :class="ccAni4 ? 'ani' : ''"></div>
          <div class="item flex-box-between" :class="ccAni4 ? 'ani' : ''">
            <div class="leftIcon flex-box">
              <div class="icon"><img src="~@/assets/images/page4/step1/c-icon-4.png" alt=""></div>
              <div class="text">选择资源池</div>
            </div>
            <div class="rightValBox">
              <div class="cc cc4 flex-box">
                <div class="left">
                  <p>智算作业</p>
                  <p class="ss">华为云-上海一</p>
                </div>
                <div class="right">
                  <span>实例规格：m1.large</span> <span>GPU: 1*NVIDIA-V100(32GB)</span> <span>CPU: 8 核 64GB</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="topList" v-show="showWorkSecond">
          <div class="item flex-box-between" :class="ccAni1_2 ? 'ani' : ''">
            <div class="leftIcon flex-box">
              <div class="icon"><img src="~@/assets/images/page4/step1/c-icon-1.png" alt=""></div>
              <div class="text">算力要求</div>
            </div>
            <div class="rightValBox">
              <div class="cc cc1">
                <div class="svg" id="svg2"></div>
                <div class="smallItemList flex-box-between">
                  <div class="smallItem" :class="cwordAni1_2 ? 'on' : ''">CPU≥2核</div>
                  <div class="smallItem" :class="cwordAni2_2 ? 'on' : ''">内存≥4G</div>
                  <div class="smallItem" :class="cwordAni3_2 ? 'on' : ''">网络连接：互联网</div>
                  <!-- <div class="smallItem" :class="cwordAni4_2 ? 'on' : ''">网络带宽≥2M</div> -->
                </div>
              </div>
            </div>
          </div>
          <div class="leftLine len1" :class="ccAni2_2 ? 'ani' : ''"></div>
          <div class="item flex-box-between" :class="ccAni2_2 ? 'ani' : ''">
            <div class="leftIcon flex-box">
              <div class="icon"><img src="~@/assets/images/page4/step1/c-icon-2.png" alt=""></div>
              <div class="text">分配策略</div>
            </div>
            <div class="rightValBox">
              <div class="cc cc2">
                <div class="sixItem flex-box-between">
                  <div class="sixItemSon" :class="sixItemSonAni1_2 ? 'on' : ''">
                    <div class="text">智能推荐</div>
                    <div class="point"></div>
                  </div>
                  <div class="sixItemSon" :class="sixItemSonAni2_2 ? 'on' : ''">
                    <div class="text">绿色优先</div>
                    <div class="point"></div>
                  </div>
                  <div class="sixItemSon" :class="sixItemSonAni3_2 ? 'on' : ''">
                    <div class="text">成本优先</div>
                    <div class="point"></div>
                  </div>
                  <div class="sixItemSon" :class="sixItemSonAni4_2 ? 'on' : ''">
                    <div class="text">指定资源池</div>
                    <div class="point"></div>
                  </div>
                  <!-- <div class="sixItemSon" :class="sixItemSonAni5_2 ? 'on' : ''">
                    <div class="text">就近选择</div>
                    <div class="point"></div>
                  </div>
                  <div class="sixItemSon" :class="sixItemSonAni6_2 ? 'on' : ''">
                    <div class="text">智能选择</div>
                    <div class="point"></div>
                  </div> -->
                </div>
              </div>
            </div>
          </div>
          <div class="leftLine len2" :class="ccAni3_2 ? 'ani' : ''"></div>
          <div class="item flex-box-between" :class="ccAni3_2 ? 'ani' : ''">
            <div class="leftIcon flex-box long">
              <div class="icon"><img src="~@/assets/images/page4/step1/c-icon-3.png" alt=""></div>
              <div class="text">推荐资源池</div>
            </div>
            <div class="rightValBox">
              <div class="cc cc3 flex-box-between">
                <div class="rightValBoxitem"
                  :class="[rightValBoxitem1_2 ? 'hide' : '', rightValBoxitemChoose_2 ? 'on' : '']">
                  <p>天翼云-内蒙3(合营)</p>
                </div>
                <div class="rightValBoxitem" :class="rightValBoxitem2_2 ? 'hide' : ''">
                  <p>天翼云 上海32（自研）</p>
                </div>
                <div class="rightValBoxitem" :class="rightValBoxitem3_2 ? 'hide' : ''">
                  <p>天翼云 华北2（自研）</p>
                </div>
                <div class="rightValBoxitem" :class="rightValBoxitem4_2 ? 'hide' : ''">
                  <p>阿里云 华北2（北京）</p>
                </div>
                <div class="rightValBoxitem" :class="rightValBoxitem5_2 ? 'hide' : ''">
                  <p>华为云 华北-北京四</p>
                </div>
                <div class="rightValBoxitem" :class="rightValBoxitem6_2 ? 'hide' : ''">
                  <p>腾讯云 华东地区（上海）</p>
                </div>
                <div class="rightValBoxitem" :class="rightValBoxitem7_2 ? 'hide' : ''">
                  <p>天翼云 成都4（自研）</p>
                </div>
                <div class="rightValBoxitem" :class="rightValBoxitem8_2 ? 'hide' : ''">
                  <p>天翼云 西南1（自研）</p>
                </div>
                <div class="bg"></div>
              </div>
            </div>
          </div>
          <div class="leftLine len3" :class="ccAni4_2 ? 'ani' : ''"></div>
          <div class="item flex-box-between" :class="ccAni4_2 ? 'ani' : ''">
            <div class="leftIcon flex-box">
              <div class="icon"><img src="~@/assets/images/page4/step1/c-icon-4.png" alt=""></div>
              <div class="text">选择资源池</div>
            </div>
            <div class="rightValBox">
              <div class="cc cc4 flex-box">
                <div class="left">
                  <p>通算作业</p>
                  <p class="ss">天翼云-内蒙3(合营)</p>
                </div>
                <div class="right">
                  <span>实例规格：m1.large</span> <span>(2核4G）</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bottomTitle" :class="bottomTitleAni1 ? 'ani' : ''">作业资源匹配</div>
      </div>
      <div class="rightCore">
        <div class="core flex-box-between">
          <div class="left" :class="rightOpa1 ? 'ani' : ''">
            <div class=" flex-box-between">
              <div class="item">
                <div class="title">作业队列</div>
                <div class="wordCore">
                  <div class="value"><span>6</span>/20</div>
                  <div class="small">排队中</div>
                </div>
              </div>
              <div class="item">
                <div class="title">GPU使用率</div>
                <round-common :value="86"></round-common>
              </div>
              <div class="item on">
                <div class="title">CPU使用率</div>
                <round-common :value="35"></round-common>
              </div>
              <div class="item on">
                <div class="title">内存使用率</div>
                <round-common :value="24"></round-common>
              </div>
            </div>
            <div class="leftTitle">华为云-上海一</div>
          </div>
          <div class="left" :class="rightOpa2 ? 'ani' : ''">
            <div class=" flex-box-between">
              <div class="item long">
                <div class="title">作业队列</div>
                <div class="wordCore">
                  <div class="value"><span>8</span>/37</div>
                  <div class="small">排队中</div>
                </div>
              </div>
              <div class="item on">
                <div class="title">CPU使用率</div>
                <round-common :value="45"></round-common>
              </div>
              <div class="item on">
                <div class="title">内存使用率</div>
                <round-common :value="22"></round-common>
              </div>
            </div>
            <div class="leftTitle">天翼云-内蒙3(合营)</div>
          </div>
        </div>
        <div class="bottomTitle" :class="bottomTitleAni2 ? 'ani' : ''">资源池水位</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getStore, setStore } from "@/common/util";
import VueMatrixRaindrop2 from '../vueMatrixDigitRain/index2.vue';
import * as d3 from 'd3' // d3
import roundCommon from '../round/Index'

export default {
  name: 'workstep1',
  components: {
    VueMatrixRaindrop2,
    roundCommon
  },
  data() {
    return {
      point: [],
      percent: 0,
      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      wordAni4: false,
      wordAni5: false,
      wordAni6: false,
      iconAni1: true,
      iconAni2: false,
      iconAni3: true,
      iconAni4: false,
      showCode: false,
      timer: null,
      itemAni1: false,
      itemAni2: false,
      percent: 0,
      percent2: 0,
      percentdesc: '上传训练数据集...',
      percentdesc2: '作业入池中...',
      percentdesc3: '作业入池中...',
      wordBottomAni: false,
      imgItemAni: false,
      imgItemAni2: false,
      time1: 0,
      time2: 0,
      time1Timer: null,
      time2Timer: null,
      timeDate: '',
      imgLeftAni: false,
      imgRightAni: false,
      ponit1Index: null,
      ponit2Index: null,
      pointAnitimer1: null,
      pointAnitimer2: null,
      wordBottomAni2: null,
      isAuto: null,
      centerOpa: false,
      rightOpa: false,

      cwordAni1: false,
      cwordAni2: false,
      cwordAni3: false,
      cwordAni4: false,
      cwordAni5: false,
      ccAni1: false,
      ccAni2: false,
      ccAni3: false,
      ccAni4: false,
      sixItemSonAni1: false,
      sixItemSonAni2: false,
      sixItemSonAni3: false,
      sixItemSonAni4: false,
      sixItemSonAni5: false,
      sixItemSonAni6: false,
      rightValBoxitem1: false,
      rightValBoxitem2: false,
      rightValBoxitem3: false,
      rightValBoxitem4: false,
      rightValBoxitem5: false,
      rightValBoxitem6: false,
      rightValBoxitem7: false,
      rightValBoxitem8: false,
      rightValBoxitemChoose: false,

      cwordAni1_2: false,
      cwordAni2_2: false,
      cwordAni3_2: false,
      cwordAni4_2: false,
      cwordAni5_2: false,
      ccAni1_2: false,
      ccAni2_2: false,
      ccAni3_2: false,
      ccAni4_2: false,
      sixItemSonAni1_2: false,
      sixItemSonAni2_2: false,
      sixItemSonAni3_2: false,
      sixItemSonAni4_2: false,
      sixItemSonAni5_2: false,
      sixItemSonAni6_2: false,
      rightValBoxitem1_2: false,
      rightValBoxitem2_2: false,
      rightValBoxitem3_2: false,
      rightValBoxitem4_2: false,
      rightValBoxitem5_2: false,
      rightValBoxitem6_2: false,
      rightValBoxitem7_2: false,
      rightValBoxitem8_2: false,
      rightValBoxitemChoose_2: false,

      rightOpa1: false,
      rightOpa2: false,
      word1: '资源匹配中',
      word1_2: '资源匹配中',
      showWorkSecond: false,
      bottomTitleAni1: false,
      bottomTitleAni2: false,
    }
  },
  created() {
    this.isAuto = this.GLOBAL.isAuto
  },
  mounted() {
    this.init()
  },
  methods: {
    // 通用添加小球方法
    addBallNext() {
      var height = 63
      var width = 967
      var svg = d3.select('#svg1').append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 1000; i++) {
        svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (60) + 2).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 950 - (i - 30)).ease(d3.easeLinear)
      }
    },
    // 通用添加小球方法
    addBallNext2() {
      var height = 63
      var width = 967
      var svg = d3.select('#svg2').append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 1000; i++) {
        svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (60) + 2).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 950 - (i - 30)).ease(d3.easeLinear)
      }
    },
    init() {
      let settimer1 = setTimeout(() => {
        this.wordAni1 = true
      }, 500);
      let settimer2 = setTimeout(() => {
        this.wordAni2 = true
        this.centerOpa = true
      }, 1000);
      let settimer3 = setTimeout(() => {
        this.ccAni1 = true
        this.addBallNext()
      }, 1500);
      let settimer4 = setTimeout(() => {
      }, 2000);
      let settimer5 = setTimeout(() => {
        this.ccAni2 = true
      }, 2500);
      let settimer6 = setTimeout(() => {
      }, 3000);
      let settimer7 = setTimeout(() => {
        this.ccAni3 = true
        this.cwordAni1 = true
        this.sixItemSonAni1 = true
      }, 3500);
      let settimer9 = setTimeout(() => {
        this.cwordAni2 = true
      }, 4000);
      let settimer10 = setTimeout(() => {
        this.cwordAni3 = true
      }, 4500);
      let settimer11 = setTimeout(() => {
        this.cwordAni4 = true
      }, 5000);
      let settimer12 = setTimeout(() => {
        this.cwordAni5 = true
        this.rightValBoxitem6 = true
        this.rightValBoxitem8 = true
      }, 5500);
      let settimer13 = setTimeout(() => {
        // this.sixItemSonAni2 = true
      }, 6000);
      let settimer14 = setTimeout(() => {
        this.rightValBoxitem2 = true
        this.rightValBoxitem5 = true
      }, 6500);
      let settimer15 = setTimeout(() => {
        // this.sixItemSonAni4 = true
      }, 7000);
      let settimer16 = setTimeout(() => {
        this.rightValBoxitem1 = true
        this.rightValBoxitem4 = true
      }, 7500);
      let settimer17 = setTimeout(() => {
        // this.sixItemSonAni6 = true
      }, 8000);
      let settimer18 = setTimeout(() => {
        this.rightValBoxitem7 = true
      }, 8500);
      let settimer19 = setTimeout(() => {
        this.rightValBoxitemChoose = true
        this.bottomTitleAni1 = true
      }, 9000);
      let settimer20 = setTimeout(() => {
        this.ccAni4 = true
        this.rightOpa1 = true
        this.iconAni1 = false
        this.iconAni2 = true
        this.wordAni3 = true
        this.word1 = '资源匹配完成'
        this.bottomTitleAni2 = true
      }, 9500);

      let settimer21 = setTimeout(() => {
        this.showWorkSecond = true
        this.init2()
      }, 11000);

      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
        clearTimeout(settimer8)
        settimer8 = null;
        clearTimeout(settimer9)
        settimer8 = null;
        clearTimeout(settimer10)
        settimer10 = null;
        clearTimeout(settimer11)
        settimer11 = null;
        clearTimeout(settimer12)
        settimer12 = null;
        clearTimeout(settimer13)
        settimer13 = null;
        clearTimeout(settimer14)
        settimer14 = null;
        clearTimeout(settimer15)
        settimer15 = null;
        clearTimeout(settimer16)
        settimer16 = null;
        clearTimeout(settimer17)
        settimer17 = null;
        clearTimeout(settimer18)
        settimer18 = null;
        clearTimeout(settimer19)
        settimer19 = null;
        clearTimeout(settimer20)
        settimer20 = null;
        clearTimeout(settimer21)
        settimer21 = null;
      })
    },
    init2() {
      let settimer1 = setTimeout(() => {
        this.wordAni3 = true
      }, 500);
      let settimer2 = setTimeout(() => {
        this.wordAni4 = true
      }, 1000);
      let settimer3 = setTimeout(() => {
        this.ccAni1_2 = true
        this.wordAni5 = true
        this.addBallNext2()
      }, 1500);
      let settimer4 = setTimeout(() => {
      }, 2000);
      let settimer5 = setTimeout(() => {
        this.ccAni2_2 = true
      }, 2500);
      let settimer6 = setTimeout(() => {
      }, 3000);
      let settimer7 = setTimeout(() => {
        this.ccAni3_2 = true
        this.cwordAni1_2 = true
        this.sixItemSonAni1_2 = true
      }, 3500);
      let settimer9 = setTimeout(() => {
        this.cwordAni2_2 = true
      }, 4000);
      let settimer10 = setTimeout(() => {
        this.cwordAni3_2 = true
      }, 4500);
      let settimer11 = setTimeout(() => {
        this.cwordAni4_2 = true
      }, 5000);
      let settimer12 = setTimeout(() => {
        this.cwordAni5_2 = true
        this.rightValBoxitem6_2 = true
        this.rightValBoxitem8_2 = true
      }, 5500);
      let settimer13 = setTimeout(() => {
        // this.sixItemSonAni2_2 = true
      }, 6000);
      let settimer14 = setTimeout(() => {
        this.rightValBoxitem2_2 = true
        this.rightValBoxitem5_2 = true
      }, 6500);
      let settimer15 = setTimeout(() => {
        // this.sixItemSonAni5_2 = true
      }, 7000);
      let settimer16 = setTimeout(() => {
        this.rightValBoxitem7_2 = true
        this.rightValBoxitem4_2 = true
      }, 7500);
      let settimer17 = setTimeout(() => {
        // this.sixItemSonAni6_2 = true
      }, 8000);
      let settimer18 = setTimeout(() => {
        this.rightValBoxitem3_2 = true
      }, 8500);
      let settimer19 = setTimeout(() => {
        this.rightValBoxitemChoose_2 = true
      }, 9000);
      let settimer20 = setTimeout(() => {
        this.ccAni4_2 = true
        this.rightOpa2 = true
        this.iconAni3 = false
        this.iconAni4 = true
        this.wordAni6 = true
        this.word1_2 = '资源匹配完成'
        this.$emit('stepEnd', 2)
      }, 9500);

      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
        clearTimeout(settimer8)
        settimer8 = null;
        clearTimeout(settimer9)
        settimer9 = null;
        clearTimeout(settimer10)
        settimer10 = null;
        clearTimeout(settimer11)
        settimer11 = null;
        clearTimeout(settimer12)
        settimer12 = null;
        clearTimeout(settimer13)
        settimer13 = null;
        clearTimeout(settimer14)
        settimer14 = null;
        clearTimeout(settimer15)
        settimer15 = null;
        clearTimeout(settimer16)
        settimer16 = null;
        clearTimeout(settimer17)
        settimer17 = null;
        clearTimeout(settimer18)
        settimer18 = null;
        clearTimeout(settimer19)
        settimer19 = null;
        clearTimeout(settimer20)
        settimer20 = null;
      })
    },
    endFun(index) {
      if (this.isAuto) {
        return false
      }
      this.$emit('endFun', index)
    },
    resetFun() {
    },
  },
  beforeDestroy() {
    this.resetFun()
  },
}
</script>

<style lang="scss">
.pageWork1 {

  .rightBox {
    width: 2600px;
    box-sizing: border-box;
    padding-right: 60px;
    padding-top: 120px;

    .centerCore {
      opacity: 0;

      .topList {
        position: relative;

        .item {
          opacity: 0;

          .leftIcon {
            .icon {
              width: 34px;
              height: 34px;
              margin-right: 23px;

              img {
                width: 100%;
                height: 100%;
              }
            }

            .text {
              font-size: 20px;
              line-height: 34px;
              width: 150px;
            }

            &:first-child {
              margin-top: 15px;
            }

            &.long {
              margin-top: 45px;
            }
          }

          .cc {
            position: relative;
            background-color: #1F1E21;
            border-radius: 4px;
            margin-bottom: 70px;
          }

          .cc1 {
            width: 967px;
            height: 63px;

            .smallItemList {
              box-sizing: border-box;
              padding: 13px 29px 0 29px;
              position: absolute;
              top: 0;
              left: 0;
              z-index: 1;
              width: 100%;
              height: 100%;

              .smallItem {
                width: 146px;
                height: 36px;
                background: rgba($color: #26262A, $alpha: 0.5);
                border: 1px solid #26A6E0;
                border-radius: 4px;
                font-size: 14px;
                text-align: center;
                line-height: 34px;
                opacity: 0;

                &.on {
                  animation: imgOpacity 1s linear 1 forwards;
                }
              }
            }
          }

          .cc2 {
            width: 967px;
            height: 63px;

            .sixItem {
              padding: 13px 40px 0 40px;

              .sixItemSon {
                .text {
                  font-size: 16px;
                  line-height: 16px;
                }

                .point {
                  width: 10px;
                  height: 10px;
                  background: #666666;
                  border-radius: 50%;
                  margin: 0 auto;
                  margin-top: 10px;
                }

                &.on {
                  .point {
                    background: #21D571;
                  }
                }

                &.hide {}
              }
            }
          }

          .cc3 {
            width: 967px;
            height: 126px;

            .bg {
              background-image: url('~@/assets/images/page4/step1/cc3-bg.png');
              background-size: cover;
              width: 93px;
              height: 103px;
              position: absolute;
              z-index: 0;
              top: 50%;
              left: 50%;
              margin-top: -51.5px;
              margin-left: -46.5px;
            }

            .rightValBoxitem {
              width: 25%;
              height: 50%;
              box-sizing: border-box;
              text-align: center;
              border-left: 1px dashed rgba($color: #ffffff, $alpha: 0.3);
              border-bottom: 1px dashed rgba($color: #ffffff, $alpha: 0.3);
              padding-top: 20px;

              p {
                margin: 0;
                font-size: 16px;
                line-height: 16px;

                span {
                  font-size: 12px;
                }

                &:first-child {
                  margin-bottom: 8px;
                }
              }

              &:nth-child(1),
              &:nth-child(5) {
                border-left: none;
              }

              &:nth-child(5),
              &:nth-child(6),
              &:nth-child(7),
              &:nth-child(8) {
                border-bottom: none;
              }

              &.on {
                color: #00E3FB;
              }

              &.hide {
                p {
                  animation: imgOpacity2 0.5s linear 1 forwards;
                }
              }
            }
          }

          .cc4 {
            width: 967px;
            height: 63px;

            .left {
              width: 126px;
              height: 62px;
              background: #347AFE;
              border-radius: 4px;
              font-size: 16px;
              text-align: center;

              p {
                margin: 5px 0;
                &.ss{
                  font-size: 14px;
                }
              }

              &.on {
                background: #26A6E0;
              }
            }

            .right {
              padding-left: 40px;
              font-size: 14px;
              line-height: 63px;

              span {
                margin-right: 60px;
              }
            }
          }

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }

        .leftLine {
          width: 1px;
          border-left: 1px dashed #16D0FF;
          opacity: 0.5;
          left: 17px;
          position: absolute;
          z-index: 1;
          opacity: 0;

          &.len1 {
            height: 88px;
            top: 55px;
          }

          &.len2 {
            height: 118px;
            top: 187px;
          }

          &.len3 {
            height: 118px;
            top: 353px;
          }

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }
      }

      .bottomTitle {
        font-size: 26px;
        text-align: center;
        padding-top: 10px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }

      &.opa {
        opacity: 1;
      }
    }

    .rightCore {
      width: 1150px;

      .core {
        .left {
          width: 550px;
          // height: 430px;
          opacity: 0;

          .item {
            height: 200px;
            width: 260px;
            background-color: rgba($color: #26262A, $alpha: 0.15);
            text-align: center;
            box-sizing: border-box;
            padding: 18px;

            .title {
              font-size: 16px;
              line-height: 16px;
              text-align: left;
            }

            .value {
              padding-top: 40px;
              font-size: 42px;
              color: #666666;
              line-height: 42px;

              span {
                color: #ffffff;
              }
            }

            .small {
              font-size: 12px;
              margin-top: 20px;
            }

            &.long {
              width: 100%;
            }

            &.on {
              margin-top: 30px;
            }
          }

          .leftTitle {
            font-size: 20px;
            text-align: center;
            margin-top: 60px;
          }

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }
      }

      .bottomTitle {
        font-size: 26px;
        text-align: center;
        padding-top: 90px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }
  }
}

@keyframes imgOpacity {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes imgOpacity2 {

  0% {
    opacity: 1;
  }

  100% {
    opacity: 0.2;
  }
}
</style>
