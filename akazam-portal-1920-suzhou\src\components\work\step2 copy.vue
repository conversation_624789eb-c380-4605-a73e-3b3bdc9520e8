<template>
  <div class="pageWork2 flex-box-between">
    <div class="leftStep flex-box">
      <div class="stepList">
        <div class="item on al" @click="endFun(1)">作业入池</div>
        <div class="arraw"></div>
        <div class="item on">资源池调度</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(3)">模型训练</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(4)">应用部署</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(5)">推理验证</div>
      </div>
      <div>
        <div class="wordList">
          <div :class="showWork2 ? 'on' : ''">
            <p :class="wordAni1 && !showWork2 ? 'ani' : ''">作业名称：智算作业-图像分类学习</p>
            <div class="flex-box">
              <p :class="wordAni2 && !showWork2 ? 'ani' : ''">CPU≥4核</p>
              <p class="ml1" :class="wordAni3 && !showWork2 ? 'ani' : ''">内存≥8G</p>
            </div>
            <p :class="wordAni4 && !showWork2 ? 'ani' : ''">GPU型号：V100</p>
            <div class="flex-box">
              <p :class="wordAni5 && !showWork2 ? 'ani' : ''">GPU单卡显存≥8G</p>
              <p class="ml2" :class="wordAni6 && !showWork2 ? 'ani' : ''">GPU卡数量≥1个</p>
            </div>
            <div class="flex-box">
              <p :class="wordAni7 && !showWork2 ? 'ani' : ''">网络连接：专线</p>
              <p class="ml2" :class="wordAni8 && !showWork2 ? 'ani' : ''">绿色节能：是</p>
              <p class="ml2" :class="wordAni9 && !showWork2 ? 'ani' : ''">智能策略：是</p>
            </div>
          </div>
        </div>
        <div class="wordList">
          <div>
            <p :class="wordAni1_2 ? 'ani' : ''">作业名称：通算作业-图像应用Build</p>
            <div class="flex-box">
              <p :class="wordAni2_2 ? 'ani' : ''">CPU≥2核</p>
              <p class="ml1" :class="wordAni3_2 ? 'ani' : ''">内存≥2G</p>
            </div>
            <div class="flex-box">
              <p :class="wordAni4_2 ? 'ani' : ''">数据存储≥10G</p>
              <p class="ml2" :class="wordAni5_2 ? 'ani' : ''">网络带宽≥2M</p>
            </div>
            <div class="flex-box">
              <p :class="wordAni7_2 ? 'ani' : ''">网络连接：互联网</p>
              <p class="ml2" :class="wordAni8_2 ? 'ani' : ''">就近选择：是</p>
              <p class="ml2" :class="wordAni9_2 ? 'ani' : ''">智能策略：是</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="rightBox flex-box-between">
      <div class="leftCore">
        <div class="flex-box">
          <div class="pointCore" v-show="!showWork2">
            <div class="svg" id="svg1"></div>
            <!-- <div class="bg" :class="wordAni1 ? 'ani' : ''"><img src="~@/assets/images/page4/step2/point.png" alt=""></div> -->
            <div class="posi posi1" :class="wordAni2 ? 'ani' : ''">CPU≥4核</div>
            <div class="posi posi2" :class="wordAni3 ? 'ani' : ''">内存≥8G</div>
            <div class="posi posi3" :class="wordAni4 ? 'ani' : ''">GPU型号：V100</div>
            <div class="posi posi4" :class="wordAni5 ? 'ani' : ''">GPU单卡显存≥8G</div>
            <div class="posi posi5" :class="wordAni6 ? 'ani' : ''">GPU卡数量≥1个</div>
            <div class="word" :class="wordAni2 ? 'ani' : ''">调度智能匹配</div>
          </div>
          <!-- 作业2 -->
          <div class="pointCore" v-show="showWork2">
            <div class="svg" id="svg2"></div>
            <!-- <div class="bg" :class="wordAni1_2 ? 'ani' : ''"><img src="~@/assets/images/page4/step2/point.png" alt=""></div> -->
            <div class="posi posi1" :class="wordAni2_2 ? 'ani' : ''">CPU≥2核</div>
            <div class="posi posi2" :class="wordAni3_2 ? 'ani' : ''">内存≥2G</div>
            <div class="posi posi3" :class="wordAni4_2 ? 'ani' : ''">数据存储≥10G</div>
            <div class="posi posi4" :class="wordAni5_2 ? 'ani' : ''">网络带宽≥2M</div>
            <div class="word" :class="wordAni2_2 ? 'ani' : ''">调度智能匹配</div>
          </div>

          <div class="line" :class="lineAni ? 'ani' : ''" v-show="!showWork2">
          </div>
          <div class="line" :class="lineAni_2 ? 'ani' : ''" v-show="showWork2">
          </div>

          <div class="boxCore">

            <div class="top flex-box" :class="topAni ? 'ani' : ''" v-show="!showWork2">
              <div class="icon" :class="topAni1 ? 'ani' : ''">资源限制</div>
              <div class="arrow" :class="topAni2 ? 'ani' : ''"></div>
              <div class="icon" :class="topAni2 ? 'ani' : ''">网络连接</div>
              <div class="arrow" :class="topAni3 ? 'ani' : ''"></div>
              <div class="icon" :class="topAni3 ? 'ani' : ''">绿色节能</div>
              <div class="arrow" :class="topAni4 ? 'ani' : ''"></div>
              <div class="icon" :class="topAni4 ? 'ani' : ''">智能策略</div>
            </div>

            <div class="top flex-box" :class="topAni_2 ? 'ani' : ''" v-show="showWork2">
              <div class="icon" :class="topAni1_2 ? 'ani' : ''">资源限制</div>
              <div class="arrow" :class="topAni2_2 ? 'ani' : ''"></div>
              <div class="icon" :class="topAni2_2 ? 'ani' : ''">网络连接</div>
              <div class="arrow" :class="topAni3_2 ? 'ani' : ''"></div>
              <div class="icon" :class="topAni3_2 ? 'ani' : ''">就近选择</div>
              <div class="arrow" :class="topAni4_2 ? 'ani' : ''"></div>
              <div class="icon" :class="topAni4_2 ? 'ani' : ''">智能策略</div>
            </div>

            <div class="loading flex-box-center" :class="loadingAni ? 'ani' : ''" v-show="!showWork2">
              <div class="icon" v-show="!loadingAniText"><i class="el-icon-loading"></i></div>
              <div class="icon" v-show="loadingAniText"><i class="el-icon-circle-check" style="color: #43B740"></i></div>
              <div class="word">{{ loadingAniText ? '匹配成功' : '正在匹配可用资源池' }}</div>
            </div>
            <div class="loading flex-box-center" :class="loadingAni_2 ? 'ani' : ''" v-show="showWork2">
              <div class="icon" v-show="!loadingAniText2"><i class="el-icon-loading"></i></div>
              <div class="icon" v-show="loadingAniText2"><i class="el-icon-circle-check" style="color: #43B740"></i></div>
              <div class="word">{{ loadingAniText2 ? '匹配成功' : '正在匹配可用资源池' }}</div>
            </div>

            <div class="bottomCore flex-box-between" :class="bottomCoreAni ? 'ani' : ''" v-show="!showWork2">
              <div class="son" :class="[sonAni1 ? 'ani' : '']">
                <p class="fz20">1<span>个</span></p>
                <p>天翼云</p>
              </div>
              <div class="son" :class="sonAni2 ? 'ani' : ''">
                <p class="fz20">8<span>个</span></p>
                <p>阿里云</p>
              </div>
              <div class="son" :class="[sonAni3 ? 'ani' : '', showChoose ? 'on' : '']">
                <p class="fz20">1<span>个</span></p>
                <p>华为云</p>
              </div>
              <div class="son" :class="sonAni4 ? 'ani' : ''">
                <p class="fz20">2<span>个</span></p>
                <p>公共资源池</p>
              </div>
              <div class="son" :class="sonAni5 ? 'ani' : ''">
                <p class="fz20">10<span>个</span></p>
                <p>林格尔数据中心</p>
              </div>
              <div class="son" :class="sonAni6 ? 'ani' : ''">
                <p class="fz20">3<span>个</span></p>
                <p>贵阳数据中心</p>
              </div>
              <div class="son" :class="[sonAni7 ? 'ani' : '']">
                <p class="fz20">3<span>个</span></p>
                <p>上海青浦数据中心</p>
              </div>
              <div class="son" :class="sonAni8 ? 'ani' : ''">
                <p class="fz20">3<span>个</span></p>
                <p>中卫数据中心</p>
              </div>
            </div>
            <!-- 作业2 -->
            <div class="bottomCore flex-box-between" :class="bottomCoreAni_2 ? 'ani' : ''" v-show="showWork2">
              <div class="son" :class="[sonAni1_2 ? 'ani' : '', showChoose_2 ? 'on' : '']">
                <p class="fz20">1<span>个</span></p>
                <p>天翼云</p>
              </div>
              <div class="son" :class="sonAni2_2 ? 'ani' : ''">
                <p class="fz20">8<span>个</span></p>
                <p>阿里云</p>
              </div>
              <div class="son" :class="sonAni3_2 ? 'ani' : ''">
                <p class="fz20">1<span>个</span></p>
                <p>华为云</p>
              </div>
              <div class="son" :class="sonAni4_2 ? 'ani' : ''">
                <p class="fz20">2<span>个</span></p>
                <p>公共资源池</p>
              </div>
              <div class="son" :class="sonAni5_2 ? 'ani' : ''">
                <p class="fz20">10<span>个</span></p>
                <p>林格尔数据中心</p>
              </div>
              <div class="son" :class="sonAni6_2 ? 'ani' : ''">
                <p class="fz20">3<span>个</span></p>
                <p>贵阳数据中心</p>
              </div>
              <div class="son" :class="[sonAni7_2 ? 'ani' : '']">
                <p class="fz20">1<span>个</span></p>
                <p>上海青浦数据中心</p>
              </div>
              <div class="son" :class="sonAni8_2 ? 'ani' : ''">
                <p class="fz20">3<span>个</span></p>
                <p>中卫数据中心</p>
              </div>
            </div>

            <div class="chooseCore" :class="chooseAni ? 'ani' : ''">
              <div class="name" :class="[showWork2_2 ? 'gary2' : '', showWork2_2_on ? 'opa' : '']">匹配的资源池规格</div>
              <div class="box on flex-box" :class="showWork2_2_on ? 'opa' : ''" v-show="showWork2_2">
                <div class="ll">天翼云-上海</div>
                <div class="rr"><span>实例规格：m1.large</span> <span>(2核4G）</span> 
                </div>
                <div class="posiLeft flex-box-center">
                  <div class="icon"><img src="~@/assets/images/page4/step1/icon-2.png" alt=""></div>
                  <div class="label">通算作业</div>
                </div>
              </div>
              <div class="box second flex-box" :class="[showWork2_2 ? 'gary' : '', showWork2_2_on ? 'opa' : '']">
                <div class="ll">华为云-内蒙古</div>
                <div class="rr"><span>实例规格：m1.large</span> <span>GPU: 1*NVIDIA-V100(32GB)</span> <span>CPU: 8 核 64GB</span>
                </div>
                <div class="posiLeft flex-box-center">
                  <div class="icon"><img src="~@/assets/images/page4/step1/icon-1.png" alt=""></div>
                  <div class="label">智算作业</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bottomWord" :class="[bottomWordAni ? 'ani' : '', showWork2_2 ? 'on' : '']">作业资源匹配</div>
      </div>
      <div class="rightMap">
        <div class=" flex-box">
          <div class="china" :class="chinaAni ? 'ani' : ''">
            <div>
              <div class="posi posi1 flex-box" :class="mapAni1 ? 'ani' : ''">
                <div class="point"></div>
                <div class="name">京津冀枢纽</div>
              </div>
              <div class="posi posi2 flex-box" :class="mapAni2 ? 'ani' : ''">
                <div class="name">内蒙古枢纽</div>
                <div class="point"></div>
              </div>
              <div class="posi posi3 flex-box" :class="mapAni3 ? 'ani' : ''">
                <div class="name">宁夏枢纽</div>
                <div class="point"></div>
              </div>
              <div class="posi posi4 flex-box" :class="mapAni4 ? 'ani' : ''">
                <div class="name">甘肃枢纽</div>
                <div class="point"></div>
              </div>
              <div class="posi posi5 flex-box" :class="mapAni5 ? 'ani' : ''">
                <div class="name">成渝枢纽</div>
                <div class="point"></div>
              </div>
              <div class="posi posi6 flex-box" :class="mapAni6 ? 'ani' : ''">
                <div class="name">贵州枢纽</div>
                <div class="point"></div>
              </div>
              <div class="posi posi7 flex-box" :class="mapAni7 ? 'ani' : ''">
                <div class="name">粤港澳枢纽</div>
                <div class="point"></div>
              </div>
              <div class="linePosi posi1" :class="mapAni1 ? 'ani' : ''"></div>
              <div class="linePosi posi2" :class="mapAni2 ? 'ani' : ''"></div>
              <div class="linePosi posi3" :class="mapAni3 ? 'ani' : ''"></div>
              <div class="linePosi posi4" :class="mapAni3 ? 'ani' : ''"></div>
              <div class="linePosi posi5" :class="mapAni4 ? 'ani' : ''"></div>
              <div class="linePosi posi6" :class="mapAni5 ? 'ani' : ''"></div>
              <div class="linePosi posi7" :class="mapAni6 ? 'ani' : ''"></div>
              <div class="linePosi posi8" :class="mapAni7 ? 'ani' : ''"></div>
              <div class="redLine" :class="redLineAni ? 'ani' : ''"></div>
            </div>
            <div class="pointCenter flex-box">
              <div class="icon"></div>
              <div class="name">长三角算力资源一体化调度示范平台</div>
            </div>

          </div>
          <div class="shanghai" :class="shanghaiAni ? 'ani' : ''">
            <div class="pointCenter flex-box">
              <div class="icon"></div>
            </div>
            <div class="posi posi1 flex-box" :class="redLineAni2 ? 'on' : ''">
              <div class="name">青浦数据中心</div>
              <div class="point"></div>
            </div>
            <div class="posi posi2 flex-box" :class="map2Ani1 ? 'ani' : ''">
              <div class="point"></div>
              <div class="name">浦东数据中心</div>
            </div>
            <div class="linePosi posi1" :class="map2Ani1 ? 'ani' : ''"></div>
            <div class="linePosi posi2" :class="map2Ani1 ? 'ani' : ''"></div>
            <div class="redLine" :class="redLineAni2 ? 'ani' : ''"></div>
            <div class="blueLine" :class="redLineAni2 ? 'ani' : ''"></div>
          </div>
        </div>
        <div class="bottomWord" :class="bottomWordAni2 ? 'ani' : ''">作业资源调度</div>
      </div>
    </div>
  </div>
</template>
<script>
import { getStore, setStore } from "@/common/util";
import * as d3 from 'd3' // d3

export default {
  name: 'workstep2',
  data() {
    return {
      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      wordAni4: false,
      wordAni5: false,
      wordAni6: false,
      wordAni7: false,
      wordAni8: false,
      wordAni9: false,
      bgAni: false,
      lineAni: false,
      topAni: false,
      loadingAni: false,
      bottomCoreAni: false,
      bottomWordAni: false,
      chinaAni: false,
      shanghaiAni: false,
      bottomWordAni2: false,
      topAni1: false,
      topAni2: false,
      topAni3: false,
      topAni4: false,
      sonAni1: false,
      sonAni2: false,
      sonAni3: false,
      sonAni4: false,
      sonAni5: false,
      sonAni6: false,
      sonAni7: false,
      sonAni8: false,
      showChoose: false,
      chooseAni: false,
      mapAni1: false,
      mapAni2: false,
      mapAni3: false,
      mapAni4: false,
      mapAni5: false,
      mapAni6: false,
      mapAni7: false,
      mapShow: true,
      redLineAni: false,
      redLineAni2: false,
      map2Ani1: false,
      map2Ani2: false,

      wordAni1_2: false,
      wordAni2_2: false,
      wordAni3_2: false,
      wordAni4_2: false,
      wordAni5_2: false,
      wordAni6_2: false,
      wordAni7_2: false,
      wordAni8_2: false,
      wordAni9_2: false,
      bgAni_2: false,
      lineAni_2: false,
      topAni_2: false,
      loadingAni_2: false,
      bottomCoreAni_2: false,
      bottomWordAni_2: false,
      chinaAni_2: false,
      shanghaiAni_2: false,
      bottomWordAni2_2: false,
      topAni1_2: false,
      topAni2_2: false,
      topAni3_2: false,
      topAni4_2: false,
      sonAni1_2: false,
      sonAni2_2: false,
      sonAni3_2: false,
      sonAni4_2: false,
      sonAni5_2: false,
      sonAni6_2: false,
      sonAni7_2: false,
      sonAni8_2: false,
      showChoose_2: false,
      chooseAni_2: false,
      mapAni1_2: false,
      mapAni2_2: false,
      mapAni3_2: false,
      mapAni4_2: false,
      mapAni5_2: false,
      mapAni6_2: false,
      mapAni7_2: false,
      mapShow_2: true,
      redLineAni_2: false,
      map2Ani1_2: false,
      map2Ani2_2: false,

      showWork2: false,
      showWork2_2: false,
      showWork2_2_on: false,
      loadingAniText: false,
      loadingAniText2: false,
      isAuto: null
    }
  },
  created() {
    this.isAuto = this.GLOBAL.isAuto
  },
  mounted() {
    this.init()
  },
  methods: {
    // 通用添加小球方法
    addBallNext() {
      var height = 210
      var width = 560
      var svg = d3.select('#svg1').append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 1000; i++) {
        if (i < 500) {
          svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (190) + 9).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 500).ease(d3.easeLinear).transition().duration(500).attr('cx', 560).attr('cy', 102).ease(d3.easeLinear).remove()
        } else {
          svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (190) + 9).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 560 - (i - 500)).ease(d3.easeLinear)
        }
      }
    },
    // 通用添加小球方法
    addBallNext2() {
      var height = 210
      var width = 560
      var svg = d3.select('#svg2').append('svg').attr('height', height).attr('width', width)
      for (let i = 0; i < 1000; i++) {
        if (i < 500) {
          svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (190) + 9).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 500).ease(d3.easeLinear).transition().duration(500).attr('cx', 560).attr('cy', 102).ease(d3.easeLinear).remove()
        } else {
          svg.append('circle').attr('cx', 0).attr('cy', Math.random() * (190) + 9).attr('r', 1.5).attr('fill', '#26A6E0').transition().duration(Math.random() * 10000).attr('cx', 560 - (i - 500)).ease(d3.easeLinear)
        }
      }
    },
    init() {
      let arr = []
      for (let i = 0; i < 64; i++) {
        arr.push(i)
      }
      this.point = arr
      let settimer1 = setTimeout(() => {
        this.wordAni1 = true
      }, 500);
      let settimer2 = setTimeout(() => {
        this.addBallNext()
      }, 1000);
      let settimer7 = setTimeout(() => {
        this.lineAni = true
      }, 3500);
      let settimer8 = setTimeout(() => {
        this.topAni = true
        this.wordAni2 = true
      }, 4000);
      let settimer3 = setTimeout(() => {
        this.wordAni3 = true
      }, 4200);
      let settimer4 = setTimeout(() => {
        this.wordAni4 = true
      }, 4400);
      let settimer5 = setTimeout(() => {
        this.wordAni5 = true
      }, 4600);
      let settimer6 = setTimeout(() => {
        this.wordAni6 = true
      }, 4800);
      let settimer9 = setTimeout(() => {
        this.loadingAni = true
      }, 5000);
      let settimer10 = setTimeout(() => {
        this.bottomCoreAni = true
        this.topAni1 = true
      }, 6000);
      let settimer11 = setTimeout(() => {
        this.bottomWordAni = true
        this.sonAni2 = true
        this.sonAni1 = true
        this.topAni2 = true
        this.mapAni1 = true
        this.mapAni4 = true
        this.wordAni7 = true
      }, 7000);
      let settimer12 = setTimeout(() => {
        this.chinaAni = true
        this.sonAni7 = true
        this.sonAni8 = true
        this.topAni3 = true
        this.mapAni3 = true
        this.mapAni7 = true
        this.wordAni8 = true
      }, 8000);
      let settimer13 = setTimeout(() => {
        this.sonAni4 = true
        this.topAni4 = true
        this.mapAni5 = true
        this.wordAni9 = true
      }, 9000);
      let settimer14 = setTimeout(() => {
        this.bottomWordAni2 = true
        this.sonAni5 = true
        this.sonAni6 = true
        this.mapAni6 = true
      }, 10000);
      let settimer15 = setTimeout(() => {
        this.showChoose = true
        this.chooseAni = true
        this.redLineAni = true
        this.loadingAniText = true
      }, 11000);
      let settimer16 = setTimeout(() => {
        this.showWork2_2 = true
        this.showWork2 = true
      }, 14000);
      let settimer17 = setTimeout(() => {
        this.init2()
      }, 15000);
      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
        clearTimeout(settimer8)
        settimer8 = null;
        clearTimeout(settimer9)
        settimer9 = null;
        clearTimeout(settimer10)
        settimer10 = null;
        clearTimeout(settimer11)
        settimer11 = null;
        clearTimeout(settimer12)
        settimer12 = null;
        clearTimeout(settimer13)
        settimer13 = null;
        clearTimeout(settimer14)
        settimer14 = null;
        clearTimeout(settimer15)
        settimer15 = null;
        clearTimeout(settimer16)
        settimer16 = null;
        clearTimeout(settimer17)
        settimer17 = null;
      })
    },
    init2() {
      let settimer1 = setTimeout(() => {
        this.wordAni1_2 = true
      }, 500);
      let settimer2 = setTimeout(() => {
        this.addBallNext2()
      }, 1000);
      let settimer7 = setTimeout(() => {
        this.lineAni_2 = true
      }, 3500);
      let settimer8 = setTimeout(() => {
        this.topAni_2 = true
        this.wordAni2_2 = true
      }, 4000);
      let settimer3 = setTimeout(() => {
        this.wordAni3_2 = true
      }, 4200);
      let settimer4 = setTimeout(() => {
        this.wordAni4_2 = true
      }, 4400);
      let settimer5 = setTimeout(() => {
        this.wordAni5_2 = true
      }, 4600);
      let settimer6 = setTimeout(() => {
        this.wordAni6_2 = true
      }, 4800);
      let settimer9 = setTimeout(() => {
        this.loadingAni_2 = true
      }, 5000);
      let settimer10 = setTimeout(() => {
        this.bottomCoreAni_2 = true
        this.topAni1_2 = true
      }, 6000);
      let settimer11 = setTimeout(() => {
        this.bottomWordAni_2 = true
        this.sonAni2_2 = true
        this.sonAni3_2 = true
        this.topAni2_2 = true
        this.mapAni1_2 = true
        this.mapAni4_2 = true
        this.wordAni7_2 = true
      }, 7000);
      let settimer12 = setTimeout(() => {
        this.chinaAni_2 = true
        this.sonAni7_2 = true
        this.sonAni8_2 = true
        this.topAni3_2 = true
        this.mapAni3_2 = true
        this.mapAni7_2 = true
        this.wordAni8_2 = true
      }, 8000);
      let settimer13 = setTimeout(() => {
        this.shanghaiAni = true
        // this.shanghaiAni_2 = true
        this.sonAni4_2 = true
        this.topAni4_2 = true
        this.mapAni5_2 = true
        this.wordAni9_2 = true
      }, 9000);
      let settimer14 = setTimeout(() => {
        this.bottomWordAni2 = true
        this.sonAni5_2 = true
        this.sonAni6_2 = true
        this.mapAni6_2 = true
      }, 10000);
      let settimer15 = setTimeout(() => {
        this.showChoose_2 = true
        this.chooseAni_2 = true
        this.map2Ani1 = true
        this.redLineAni2 = true
        this.showWork2_2_on = true
        this.loadingAniText2 = true
        this.$emit('stepEnd', 2)
      }, 11000);
      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
        clearTimeout(settimer8)
        settimer8 = null;
        clearTimeout(settimer9)
        settimer9 = null;
        clearTimeout(settimer10)
        settimer10 = null;
        clearTimeout(settimer11)
        settimer11 = null;
        clearTimeout(settimer12)
        settimer12 = null;
        clearTimeout(settimer13)
        settimer13 = null;
        clearTimeout(settimer14)
        settimer14 = null;
        clearTimeout(settimer15)
        settimer15 = null;
      })
    },
    endFun(index) {
      if (this.isAuto) {
        return false
      }
      this.$emit('endFun', index)
    },
  },
  beforeDestroy() {
  },
}
</script>

<style lang="scss">
.pageWork2 {
  overflow: hidden;

  .rightBox {
    width: 2700px;
    box-sizing: border-box;
    padding-right: 50px;

    .leftCore {
      margin-top: 230px;

      .pointCore {
        width: 563px;
        height: 214px;
        position: relative;
        margin-top: 110px;

        .word {
          text-align: center;
          font-size: 18px;
          color: #00E1FF;
          padding-top: 5px;
          opacity: 0;

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }

        .bg {
          width: 100%;
          height: 100%;
          opacity: 0;

          img {
            width: 100%;
            height: 100%;
          }

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }

        .svg {
          width: 100%;
          height: 100%;
        }

        .posi {
          position: absolute;
          background-image: url('~@/assets/images/page4/step2/posi.png');
          background-size: cover;
          width: 147px;
          height: 37px;
          text-align: center;
          line-height: 37px;
          font-size: 15px;
          opacity: 0;

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }

          &.posi1 {
            top: 2px;
            left: 49px;
          }

          &.posi2 {
            bottom: 2px;
            left: 49px;
          }

          &.posi3 {
            top: 45px;
            left: 180px;
          }

          &.posi4 {
            bottom: 45px;
            left: 180px;
          }

          &.posi5 {
            top: 88px;
            left: 309px;
          }
        }
      }

      .line {
        background-image: url('~@/assets/images/page4/step2/line.png');
        width: 68px;
        height: 184px;
        background-size: cover;
        margin-top: 30px;
        writing-mode: vertical-lr;
        color: #FF7B00;
        font-size: 16px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }

        .word {
          transform: translate(-10px, 30px);
        }
      }

      .boxCore {
        .top {
          opacity: 0;

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }

          .icon {
            background-image: url('~@/assets/images/page4/step2/top-item.png');
            background-size: cover;
            width: 142px;
            height: 63px;
            box-sizing: border-box;
            padding-left: 57px;
            line-height: 63px;
            font-size: 16px;
            opacity: 0.5;

            &.ani {
              animation: imgOpacity05 1s linear 1 forwards;
            }
          }

          .arrow {
            background-image: url('~@/assets/images/page4/step2/arrow.png');
            background-size: cover;
            width: 32px;
            height: 8px;
            margin-top: 26px;
            opacity: 0.5;

            &.ani {
              animation: imgOpacity05 1s linear 1 forwards;
            }
          }

        }

        .loading {
          padding: 20px 0;
          line-height: 30px;
          opacity: 0;

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }

          .icon {
            margin-right: 10px;
            font-size: 30px;
            color: #ffffff;
            line-height: 30px;
            transform: translateY(-3px);

            i {
              font-size: 24px;
              line-height: 30px;
            }
          }
        }

        .bottomCore {
          background-image: url('~@/assets/images/page4/step2/eight.png');
          background-size: cover;
          width: 664px;
          height: 163px;
          opacity: 0;

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }

          .son {
            width: 25%;
            height: 50%;
            text-align: center;
            font-size: 16px;
            line-height: 16px;
            box-sizing: border-box;
            padding-top: 5px;

            .fz20 {
              font-size: 20px;
            }

            p {
              margin: 10px 0;
            }

            span {
              font-size: 12px;
            }

            &.ani {
              animation: imgOpacityUn 1s linear 1 forwards;
            }

            &.on {
              color: #00E1FF;
              opacity: 1;
            }
          }
        }

        .chooseCore {
          opacity: 0;


          .name {
            font-size: 14px;
            padding: 15px 0;

            &.gary {
              opacity: 0.5;
            }

            &.gary2 {
              opacity: 0;
            }

            &.opa {
              animation: imgOpacity 1s linear 1 forwards;
            }
          }

          .box {
            background-image: url('~@/assets/images/page4/step2/choose.png');
            background-size: cover;
            width: 665px;
            height: 62px;
            position: relative;

            .ll {
              width: 130px;
              text-align: center;
              font-size: 16px;
              line-height: 62px;
            }

            .rr {
              font-size: 14px;
              padding-left: 15px;
              line-height: 62px;

              span {
                padding-right: 30px;
              }
            }

            .posiLeft {
              position: absolute;
              top: 0;
              left: -160px;
              width: 136px;
              height: 58px;
              line-height: 58px;
              background: #26262A;
              border: 2px solid #00C2FC;
              border-radius: 4px;

              .icon {
                width: 30px;
                height: 30px;
                margin-top: 8px;
                margin-right: 5px;

                img {
                  width: 100%;
                  height: 100%;
                }
              }

              .label {
                font-size: 18px;
                color: #ffffff;
              }
            }

            &.on {
              background-image: url('~@/assets/images/page4/step2/choose2.png');
              margin-bottom: 20px;
              opacity: 0;

              .rr {
                line-height: 62px;
              }


            }

            &.second {
              .posiLeft {
                border-color: #347AFE;
              }
            }

            &.gary {
              opacity: 0.5;
            }

            &.opa {
              animation: imgOpacity 1s linear 1 forwards;
            }
          }



          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }
      }

      .bottomWord {
        font-size: 26px;
        text-align: center;
        margin-top: 150px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }

        &.on {
          margin-top: 88px;
        }
      }
    }

    .rightMap {
      margin-top: 150px;

      .china {
        width: 765px;
        height: 597px;
        opacity: 0;
        background-image: url('~@/assets/images/page4/step2/china.png');
        background-size: cover;
        position: relative;

        .pointCenter {
          position: absolute;
          z-index: 2;
          top: 331px;
          left: 573px;
          width: 300px;

          .icon {
            background-image: url('~@/assets/images/page4/step2/point-c.png');
            width: 68px;
            height: 68px;
            background-size: cover;
          }

          .name {
            font-size: 14px;
            color: #29AD9C;
            line-height: 68px;
            transform: translateX(-20px);
          }
        }

        .posi {
          position: absolute;
          z-index: 1;

          .point {
            background-image: url('~@/assets/images/page4/step2/ponit-y.png');
            background-size: cover;
            width: 22px;
            height: 22px;
          }

          .name {
            font-size: 14px;
            line-height: 22px;
            color: #CB9B1A;
            padding: 0 10px;
          }

          &.posi1 {
            top: 203px;
            left: 516px;
          }

          &.posi2 {
            top: 201px;
            left: 358px;
          }

          &.posi3 {
            top: 255px;
            left: 276px;
          }

          &.posi4 {
            top: 298px;
            left: 199px;
          }

          &.posi5 {
            top: 394px;
            left: 343px;
          }

          &.posi6 {
            top: 488px;
            left: 270px;
          }

          &.posi7 {
            top: 519px;
            left: 417px;
          }

          &.ani {
            animation: imgOpacityUn 1s linear 1 forwards;
          }
        }

        .redLine {
          position: absolute;
          z-index: 1;
          background-size: cover;
          background-image: url('~@/assets/images/page4/step2/line2-1.png');
          width: 149px;
          height: 142px;
          top: 224px;
          left: 443px;
          opacity: 0;

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }

        .linePosi {
          position: absolute;
          z-index: 1;
          background-size: cover;

          &.posi1 {
            width: 70px;
            height: 133px;
            top: 224px;
            left: 533px;
            background-image: url('~@/assets/images/page4/step2/line1.png');
          }

          &.posi2 {
            width: 143px;
            height: 142px;
            top: 216px;
            left: 454px;
            background-image: url('~@/assets/images/page4/step2/line2.png');
          }

          &.posi3 {
            width: 234px;
            height: 106px;
            top: 257px;
            left: 362px;
            background-image: url('~@/assets/images/page4/step2/line3.png');
          }

          &.posi4 {
            width: 241px;
            height: 94px;
            top: 273px;
            left: 354px;
            background-image: url('~@/assets/images/page4/step2/line4.png');
          }

          &.posi5 {
            width: 314px;
            height: 60px;
            top: 309px;
            left: 283px;
            background-image: url('~@/assets/images/page4/step2/line5.png');
          }

          &.posi6 {
            width: 172px;
            height: 33px;
            top: 373px;
            left: 428px;
            background-image: url('~@/assets/images/page4/step2/line6.png');
          }

          &.posi7 {
            width: 246px;
            height: 117px;
            top: 378px;
            left: 357px;
            background-image: url('~@/assets/images/page4/step2/line7.png');
          }

          &.posi8 {
            width: 98px;
            height: 141px;
            top: 376px;
            left: 509px;
            background-image: url('~@/assets/images/page4/step2/line8.png');
          }

          &.ani {
            animation: imgOpacityUn 1s linear 1 forwards;
          }
        }

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }

      .shanghai {
        width: 306px;
        height: 390px;
        opacity: 0;
        background-image: url('~@/assets/images/page4/step2/shanghai.png');
        background-size: cover;
        margin-top: 170px;
        margin-left: 50px;
        position: relative;

        .pointCenter {
          position: absolute;
          z-index: 2;
          top: 265px;
          left: 75px;

          .icon {
            background-image: url('~@/assets/images/page4/step2/point-c.png');
            width: 68px;
            height: 68px;
            background-size: cover;
          }
        }

        .redLine {
          position: absolute;
          z-index: 1;
          background-size: cover;
          background-image: url('~@/assets/images/page4/step2/line2-4.png');
          width: 49px;
          height: 100px;
          top: 194px;
          left: 101px;
          opacity: 0;

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }

        .blueLine {
          position: absolute;
          z-index: 1;
          background-size: cover;
          background-image: url('~@/assets/images/page4/step2/line2-2.png');
          width: 83px;
          height: 138px;
          top: 202px;
          left: 47px;
          opacity: 0;

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }

        .posi {
          position: absolute;
          z-index: 1;

          .point {
            background-image: url('~@/assets/images/page4/step2/ponit-y.png');
            background-size: cover;
            width: 22px;
            height: 22px;
          }

          .name {
            font-size: 14px;
            line-height: 22px;
            color: #CB9B1A;
            padding: 0 10px;
          }

          &.posi1 {
            top: 191px;
            left: -28px;

            &.on {
              .point {
                background-image: url('~@/assets/images/page4/step2/ponit-b.png');
              }

              .name {
                color: #0091FF;
                opacity: 0;
              }
            }
          }

          &.posi2 {
            width: 200px;
            top: 167px;
            left: 201px;
          }

          &.ani {
            animation: imgOpacityUn 1s linear 1 forwards;
          }
        }

        .linePosi {
          position: absolute;
          z-index: 1;
          background-size: cover;

          &.posi1 {
            width: 21px;
            height: 73px;
            top: 216px;
            left: 83px;
            background-image: url('~@/assets/images/page4/step2/line11.png');
          }

          &.posi2 {
            width: 92px;
            height: 103px;
            top: 189px;
            left: 120px;
            background-image: url('~@/assets/images/page4/step2/line12.png');
          }

          &.ani {
            animation: imgOpacityUn 1s linear 1 forwards;
          }
        }

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }

        img {
          width: 100%;
          height: 100%;
        }
      }

      .bottomWord {
        font-size: 26px;
        text-align: center;
        margin-top: 60px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }
  }
}

@keyframes imgOpacity {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}


@keyframes imgOpacity05 {

  0% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}

@keyframes imgOpacityUn {

  0% {
    opacity: 1;
  }

  100% {
    opacity: 0.2;
  }
}
</style>

