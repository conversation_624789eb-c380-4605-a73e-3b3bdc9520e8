<template>
  <div class="pageCommon page4-1">
    <!-- 头部-s -->
    <header-common :icon="5" :name="'返回'" @indexClick="indexClickFun"></header-common>
    <!-- 头部-e -->
    <div class="imgPosi" v-show="imgPosiShow" @click="imgPosiShow = false"></div>
    <div class="mainBox flex-box-between" id="mainSvg">
      <div class="leftBox">
        <div class="flex-box-between">
          <div class="lenged">
          </div>
          <div class="viewCore" :class="showMenu ? 'on' : ''">
            <div class="topNumCore flex-box-between">
              <div class="item">{{ num1 }}<span>个</span></div>
              <div class="item">{{ num2 }}<span>个</span></div>
              <div class="item">{{ num3 }}<span>个</span></div>
            </div>
            <div class="svgBox" id="svgBox">

            </div>
            <div class="bottomWordCore flex-box-between">
              <div class="item">队列中</div>
              <div class="item">执行中</div>
              <div class="item">已完成</div>
            </div>
          </div>

          <div class="menu flex-box-between" v-show="showMenu">
            <div class="item" :class="showItem ? 'ani' : ''" @click="goWork(1)">
              <img class="img1" src="~@/assets/images/page4/icon-1_on.png" alt="">
              <img class="img2" src="~@/assets/images/page4/icon-1.png" alt="">
              <p>人工智能</p>
            </div>
            <div class="item" :class="showItem ? 'ani' : ''" @click="goWork(2)">
              <img class="img1" src="~@/assets/images/page4/icon-2_on.png" alt="">
              <img class="img2" src="~@/assets/images/page4/icon-2.png" alt="">
              <p>人脸识别</p>
            </div>
            <div class="item" :class="showItem ? 'ani' : ''"><img src="~@/assets/images/page4/icon-3.png" alt="">
              <p>自动驾驶</p>
            </div>
            <div class="item" :class="[showItem ? 'ani' : '']" @click="goWork(4)">
              <img class="img1" src="~@/assets/images/page4/icon-4_on.png" alt="">
              <img class="img2" src="~@/assets/images/page4/icon-4.png" alt="">
              <p>云渲染</p>
            </div>
            <div class="item" :class="showItem ? 'ani' : ''"><img src="~@/assets/images/page4/icon-5.png" alt="">
              <p>工业仿真</p>
            </div>
            <div class="item" :class="showItem ? 'ani' : ''"><img src="~@/assets/images/page4/icon-6.png" alt="">
              <p>预测分析</p>
            </div>
            <div class="item" :class="showItem ? 'ani' : ''"><img src="~@/assets/images/page4/icon-7.png" alt="">
              <p>智慧城市</p>
            </div>
            <div class="item" :class="[showItem ? 'ani' : '']" @click="goWork(8)">
              <img class="img1" src="~@/assets/images/page4/icon-8_on.png" alt="">
              <img class="img2" src="~@/assets/images/page4/icon-8.png" alt="">
              <p>图像识别</p>
            </div>
            <div class="item" :class="showItem ? 'ani' : ''" @click="goWork(9)">
              <img class="img1" src="~@/assets/images/page4/icon-9_on.png" alt="">
              <img class="img2" src="~@/assets/images/page4/icon-9.png" alt="">
              <p>文生图</p>
            </div>
          </div>
        </div>
        <div class="changeBox flex-box" @click="showMenu = !showMenu">
          <div class="text"><span v-if="!showMenu">作业概览</span><span v-else>作业类型</span></div>
          <div class="icon"><img src="~@/assets/images/page4-1/change.png" alt=""></div>
        </div>
      </div>
      <div class="rightCore flex-box-between">
        <div class="centerBox">
          <div class="svgTwo" id="svgTwo"></div>
          <div class="provice flex-box-between">
            <div class="son" v-for="(item, index) in province" :key="index">{{ item }}</div>
          </div>
          <div class="item sty1 flex-box-between" :class="filterColor && filterColor != 'blue' ? 'opacity03' : ''">
            <div class="icon"><img src="~@/assets/images/page4-1/icon-1.png" alt=""></div>
            <div class="son" v-for="(item, index) in listNumArrar" :key="index">{{ item }}个</div>
          </div>
          <div class="item sty2 flex-box-between" :class="filterColor && filterColor != 'green' ? 'opacity03' : ''">
            <div class="icon"><img src="~@/assets/images/page4-1/icon-2.png" alt=""></div>
            <div class="son" v-for="(item, index) in listNumArrar2" :key="index">{{ item }}个</div>
          </div>
          <div class="item sty3 flex-box-between" :class="filterColor && filterColor != 'yellow' ? 'opacity03' : ''">
            <div class="icon"><img src="~@/assets/images/page4-1/icon-3.png" alt=""></div>
            <div class="son" v-for="(item, index) in listNumArrar3" :key="index">{{ item }}个</div>
          </div>
          <div class="lenged flex-box-center">
            <div class="item sty1 flex-box" :class="filterColor == 'blue' ? 'on' : ''" @click="colorChange('blue')">
              <div class="round"></div>
              <div class="name">超算作业</div>
            </div>
            <div class="item sty2 flex-box" :class="filterColor == 'green' ? 'on' : ''" @click="colorChange('green')">
              <div class="round"></div>
              <div class="name">智算作业</div>
            </div>
            <div class="item sty3 flex-box" :class="filterColor == 'yellow' ? 'on' : ''" @click="colorChange('yellow')">
              <div class="round"></div>
              <div class="name">通算作业</div>
            </div>
          </div>
          <div class="bottomTitle">作业调度</div>
        </div>
        <div class="rightBox">
          <div class="svgThree" id="svgThree" :class="showPoor ? 'on' : ''"></div>
          <div class="svgFour flex-box-between" id="svgFour" v-show="showPoor"></div>
          <div class="posiBox" :class="showPoor ? 'on' : ''">
            <div class="posi posi1" @click="goPoor(1)">
              <div class="word">54<span>%</span></div>
              <div class="icon"><img src="~@/assets/images/page4-1/ri-1.png" alt=""></div>
            </div>
            <div class="posi posi2" @click="goPoor(2)">
              <div class="word">22<span>%</span></div>
              <div class="icon"><img src="~@/assets/images/page4-1/ri-2.png" alt=""></div>
            </div>
            <div class="posi posi3" @click="goPoor(3)">
              <div class="word">10<span>%</span></div>
              <div class="icon"><img src="~@/assets/images/page4-1/ri-3.png" alt=""></div>
            </div>
            <div class="posi posi4" @click="goPoor(4)">
              <div class="word">6<span>%</span></div>
              <div class="text">上海公共资源池</div>
            </div>
            <div class="posi posi5" @click="goPoor(5)">
              <div class="word">4<span>%</span></div>
              <div class="text">腾讯云</div>
            </div>
            <div class="posi posi6" @click="goPoor(6)">
              <div class="word">2<span>%</span></div>
              <div class="text">数据中心</div>
            </div>
            <!-- <div class="posi posi7">
            <div class="word">1<span>%</span></div>
            <div class="text">超算中心</div>
          </div>
          <div class="posi posi8">
            <div class="word">1<span>%</span></div>
            <div class="text">智算中心</div>
          </div> -->

          </div>
          <div class="bottomChnage flex-box-center" :class="showPoor ? 'on' : ''">
            <div class="item1" v-show="showPoor" @click="showPoorFun">资源池</div>
            <div class="item2" v-show="showPoor">天翼云</div>
          </div>
          <div class="bottomTitle">作业资源池</div>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import headerCommon from '../components/header/Index'
import * as d3 from 'd3' // d3
import { number } from 'echarts';

export default {
  components: {
    headerCommon
  },
  data() {
    return {
      svg: null,
      color: ['#5470C6', '#91CC75', '#FAC858'],
      B0: 0,
      B1: 0,
      B2: 0,
      B3: 0,
      B4: 0,
      B5: 0,
      B0_1: 0,
      B1_1: 0,
      B2_1: 0,
      B3_1: 0,
      B4_1: 0,
      B5_1: 0,
      B0_2: 0,
      B1_2: 0,
      B2_2: 0,
      B3_2: 0,
      B4_2: 0,
      B5_2: 0,
      base1MoveTimer: null,
      num1: 0,
      num2: 0,
      num3: 0,
      numChangeTimer: null,
      province: ['上海', '江苏', '北京', '四川', '香港', '贵州', '青海', '甘肃', '内蒙古'],
      listNumArrar: [0, 0, 0, 0, 0, 0, 0, 0, 0],
      listNumArrar2: [0, 0, 0, 0, 0, 0, 0, 0, 0],
      listNumArrar3: [0, 0, 0, 0, 0, 0, 0, 0, 0],
      svgTwo: null,
      centerMoveTimer: null,
      svgThree: null,
      showMenu: false,
      showItem: true,
      showPoor: false,
      svgFour: false,
      imgPosiShow: false,
      filterColor: null
    };
  },
  filters: {

  },
  mounted() {
    this.initBall()
    this.initBall2()
    this.initBall3()
  },
  created() {

  },
  methods: {
    indexClickFun() {
      this.$router.push({
        path: '/page3'
      })
    },
    reloadPage() {
      this.$router.go(0);
    },
    errorReset() {
      this.B0 = 0
      this.B1 = 0
      this.B2 = 0
      this.B3 = 0
      this.B4 = 0
      this.B5 = 0
      this.B0_1 = 0
      this.B1_1 = 0
      this.B2_1 = 0
      this.B3_1 = 0
      this.B4_1 = 0
      this.B5_1 = 0
      this.B0_2 = 0
      this.B1_2 = 0
      this.B2_2 = 0
      this.B3_2 = 0
      this.B4_2 = 0
      this.B5_2 = 0
      if (this.base1MoveTimer) {
        clearInterval(this.base1MoveTimer)
      }
      if (this.numChangeTimer) {
        clearInterval(this.numChangeTimer)
      }
      if (this.centerMoveTimer) {
        clearInterval(this.centerMoveTimer)
      }
      d3.select('#mainSvg').select('svg').remove()
      this.initBall()
      this.initBall2()
      this.initBall3()
    },
    colorChange(color) {
      let filterColor = null
      if (this.filterColor && this.filterColor == color) {
        filterColor = null
      } else {
        filterColor = color
      }
      this.filterColor = filterColor
      if (filterColor) {
        d3.select('#mainSvg').selectAll(`circle`).attr('opacity', '0.3')
        d3.select('#mainSvg').selectAll(`circle[colorType=${color}]`).attr('opacity', '1')
        d3.select('#svgFour').selectAll(`circle[textRound=textRound]`).attr('opacity', '1')
      } else {
        d3.select('#mainSvg').selectAll(`circle`).attr('opacity', '1')
      }
      // this.imgPosiShow = true  
    },
    goPoor(index) {
      let title = ''
      let arrange = 0
      let blue = 0
      let green = 0
      let yellow = 0
      let textList = []
      if (index == 1) {
        title = '天翼云'
        arrange = 5
        blue = 3
        green = 1
        yellow = 1
        textList = ['石家庄20 - 自研', '西安5 - 自研', '贵州1 - 合营', '上海4 - 合营', '太原2']
      }
      if (index == 2) {
        title = '阿里云'
        arrange = 5
        blue = 3
        green = 1
        yellow = 1
        textList = ['石家庄20 - 自研', '西安5 - 自研', '贵州1 - 合营', '上海4 - 合营', '太原2']
      }
      if (index == 3) {
        title = '华为云'
        arrange = 5
        blue = 1
        green = 3
        yellow = 1
        textList = ['石家庄20 - 自研', '西安5 - 自研', '贵州1 - 合营', '上海4 - 合营', '太原2']
      }
      if (index == 4) {
        title = '上海公共资源池'
        arrange = 3
        blue = 1
        green = 1
        yellow = 1
        textList = ['石家庄20 - 自研', '西安5 - 自研', '贵州1 - 合营']
      }
      if (index == 5) {
        title = '腾讯云'
        arrange = 1
        blue = 1
        green = 0
        yellow = 0
        textList = ['石家庄20 - 自研']
      }
      if (index == 6) {
        title = '数据中心'
        arrange = 1
        blue = 1
        green = 0
        yellow = 0
        textList = ['石家庄20 - 自研']
      }
      this.goPoorNext(title, arrange, blue, green, yellow, textList)
    },
    goPoorNext(title, arrange, blue, green, yellow, textList) {
      var height = this.GLOBAL.relPx(470)
      var width = this.GLOBAL.relPx(850)
      let arrangeOne = Math.floor(36 / arrange)
      let widthOne = (width / arrange).toFixed(2) - this.GLOBAL.relPx(10)
      for (let c = 0; c < blue; c++) {
        let svg = d3.select('#svgFour').append('svg').attr('height', height).attr('width', widthOne)
        for (let i = 0; i < arrangeOne; i++) {
          for (let k = 0; k < 20; k++) {
            svg.append('circle').attr('opacity', `${!this.filterColor || (this.filterColor && this.filterColor == 'blue') ? 1 : 0.3}`).attr('colorType', `blue`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(6) + this.GLOBAL.relPx(23) * i).attr('cy', this.GLOBAL.relPx(6) + this.GLOBAL.relPx(23) * k).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#5470C6')
          }
        }
        svg.append('circle').attr('textRound', 'textRound').attr('cx', (widthOne / 2 - this.GLOBAL.relPx(5))).attr('cy', this.GLOBAL.relPx(235)).attr('r', (widthOne / 2 - this.GLOBAL.relPx(10)) > this.GLOBAL.relPx(80) ? this.GLOBAL.relPx(80) : (widthOne / 2 - this.GLOBAL.relPx(10))).attr('fill', '#1A191C').attr('stroke', '#22355E')
        svg.append('text').text(textList[c])
          .attr("x", '50%')
          .attr("y", '50%')
          .attr("fill", "#ffffff")
          .attr("text-anchor", "middle")
      }
      for (let c = 0; c < green; c++) {
        let svg = d3.select('#svgFour').append('svg').attr('height', height).attr('width', widthOne)
        for (let i = 0; i < arrangeOne; i++) {
          for (let k = 0; k < 20; k++) {
            svg.append('circle').attr('opacity', `${!this.filterColor || (this.filterColor && this.filterColor == 'green') ? 1 : 0.3}`).attr('colorType', `green`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(6) + this.GLOBAL.relPx(23) * i).attr('cy', this.GLOBAL.relPx(6) + this.GLOBAL.relPx(23) * k).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#91CC75')
          }
        }
        svg.append('circle').attr('textRound', 'textRound').attr('cx', (widthOne / 2 - this.GLOBAL.relPx(5))).attr('cy', this.GLOBAL.relPx(235)).attr('r', (widthOne / 2 - this.GLOBAL.relPx(10)) > this.GLOBAL.relPx(80) ? this.GLOBAL.relPx(80) : (widthOne / 2 - this.GLOBAL.relPx(10))).attr('fill', '#1A191C').attr('stroke', '#22355E')
        svg.append('text').text(textList[c + blue - 1])
          .attr("x", '50%')
          .attr("y", '50%')
          .attr("fill", "#ffffff")
          .attr("text-anchor", "middle")
      }
      for (let c = 0; c < yellow; c++) {
        let svg = d3.select('#svgFour').append('svg').attr('height', height).attr('width', widthOne)
        for (let i = 0; i < arrangeOne; i++) {
          for (let k = 0; k < 20; k++) {
            svg.append('circle').attr('opacity', `${!this.filterColor || (this.filterColor && this.filterColor == 'yellow') ? 1 : 0.3}`).attr('colorType', `yellow`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(6) + this.GLOBAL.relPx(23) * i).attr('cy', this.GLOBAL.relPx(6) + this.GLOBAL.relPx(23) * k).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#FAC858')
          }
        }
        svg.append('circle').attr('textRound', 'textRound').attr('cx', (widthOne / 2 - this.GLOBAL.relPx(5))).attr('cy', this.GLOBAL.relPx(235)).attr('r', (widthOne / 2 - this.GLOBAL.relPx(10)) > this.GLOBAL.relPx(80) ? this.GLOBAL.relPx(80) : (widthOne / 2 - this.GLOBAL.relPx(10))).attr('fill', '#1A191C').attr('stroke', '#22355E')
        svg.append('text').text(textList[c + blue + green - 1])
          .attr("x", '50%')
          .attr("y", '50%')
          .attr("fill", "#ffffff")
          .attr("text-anchor", "middle")
      }
      this.showPoor = true
    },
    showPoorFun() {
      if (!this.showPoor) {
        return false
      }
      this.showPoor = false
      d3.select('#svgFour').selectAll('svg').remove()
    },
    goWork(index) {
      if (index === 4) {
        this.$router.push({
          path: '/page12'
        })
      } else if (index === 8) {
        this.$router.push({
          path: '/page2'
        })
      }
      // if (index === 1) {
      //   this.$router.push({
      //     path: '/page9'
      //   })
      // } else if (index === 2) {
      //   this.$router.push({
      //     path: '/page10'
      //   })
      // } else if (index === 4) {
      //   this.$router.push({
      //     path: '/page6'
      //   })
      // } else if (index === 8) {
      //   this.$router.push({
      //     path: '/page2'
      //   })
      // } else if (index === 9) {
      //   this.$router.push({
      //     path: '/page11'
      //   })
      // }
    },
    goWork2() {
      this.$router.push({
        path: '/page6'
      })
    },
    initBall() {
      var height = this.GLOBAL.relPx(430)
      var width = this.GLOBAL.relPx(810)
      var svg = d3.select('#svgBox').append('svg').attr('height', height).attr('width', width)
      this.svg = svg
      this.baseBall()
    },
    baseBall() {
      let svg = this.svg
      let color = this.color
      for (let k = 0; k < 10; k++) {
        for (let i = 0; i < 6; i++) {
          let colorIndex = Math.floor((Math.random() * color.length))
          let colorText = ''
          if (colorIndex == 0) {
            colorText = 'blue'
          }
          if (colorIndex == 1) {
            colorText = 'green'
          }
          if (colorIndex == 2) {
            colorText = 'yellow'
          }
          svg.append('circle').attr('opacity', `${!this.filterColor || (this.filterColor && this.filterColor == colorText) ? 1 : 0.3}`).attr('colorType', `${colorText}`).attr('type', `base`).attr('batch', `${k}`).attr('index', `${i}`).attr('cx', this.GLOBAL.relPx(12) + this.GLOBAL.relPx(24) * i).attr('cy', this.GLOBAL.relPx(410) - this.GLOBAL.relPx(24) * k).attr('r', this.GLOBAL.relPx(6)).attr('fill', color[colorIndex])
        }
      }
      for (let k = 0; k < 10; k++) {
        for (let i = 0; i < 6; i++) {
          let colorIndex2 = Math.floor((Math.random() * color.length))
          let colorText2 = ''
          if (colorIndex2 == 0) {
            colorText2 = 'blue'
          }
          if (colorIndex2 == 1) {
            colorText2 = 'green'
          }
          if (colorIndex2 == 2) {
            colorText2 = 'yellow'
          }
          svg.append('circle').attr('opacity', `${!this.filterColor || (this.filterColor && this.filterColor == colorText2) ? 1 : 0.3}`).attr('colorType', `${colorText2}`).attr('type', `base2`).attr('batch', `${k}`).attr('index', `${i}`).attr('cx', this.GLOBAL.relPx(12) + this.GLOBAL.relPx(335) + this.GLOBAL.relPx(24) * i).attr('cy', this.GLOBAL.relPx(410) - this.GLOBAL.relPx(24) * k).attr('r', this.GLOBAL.relPx(6)).attr('fill', color[colorIndex2])
        }
      }
      for (let k = 0; k < 8; k++) {
        for (let i = 0; i < 6; i++) {
          let colorIndex3 = Math.floor((Math.random() * color.length))
          let colorText3 = ''
          if (colorIndex3 == 0) {
            colorText3 = 'blue'
          }
          if (colorIndex3 == 1) {
            colorText3 = 'green'
          }
          if (colorIndex3 == 2) {
            colorText3 = 'yellow'
          }
          svg.append('circle').attr('opacity', `${!this.filterColor || (this.filterColor && this.filterColor == colorText3) ? 1 : 0.3}`).attr('colorType', `${colorText3}`).attr('type', `base3`).attr('batch', `${k}`).attr('index', `${i}`).attr('cx', this.GLOBAL.relPx(12) + this.GLOBAL.relPx(670) + this.GLOBAL.relPx(24) * i).attr('cy', this.GLOBAL.relPx(410 - 24 * k)).attr('r', this.GLOBAL.relPx(6)).attr('fill', color[colorIndex3])
        }
      }
      this.moveRound1()
      this.base1MoveTimer = setInterval(() => {
        this.moveRound1()
      }, 3200);
      this.GLOBAL.timerArraySet.push(this.base1MoveTimer)

      this.numChangeTimer = setInterval(() => {
        this.num1 = Math.ceil((Math.random() * (200 - 100 + 1)) + 100 - 1);
        this.num2 = Math.ceil((Math.random() * (300 - 200 + 1)) + 200 - 1);
        this.num3 = Math.ceil((Math.random() * (250 - 150 + 1)) + 150 - 1);
      }, 2500);
      this.GLOBAL.timerArraySet.push(this.numChangeTimer)
    },
    moveRound1() {
      let svg = this.svg
      let color = this.color
      let randomNumber = Math.floor(Math.random() * 6);
      let list = d3.select('#svgBox').selectAll('circle')._groups[0]
      let baseList = [[], [], [], [], [], []]
      list.forEach((ele, index) => {
        if (ele.attributes.type.value == 'base') {
          baseList[ele.attributes.index.value].push(ele)
        }
      })
      let baseListNow = baseList[randomNumber]
      if (!baseListNow || baseListNow.length == 0) {
        for (var i = 0; i < baseList.length; i++) {
          if (!baseList[i] || baseList[i].length === 0) continue;
          if (baseList[i].length > 0) {
            baseListNow = baseList[i]
            break;
          }
        }
      }
      // base1 小球1500移动右边 延时2000 此行小球都移动下一位300  延时2500 随机加一个球500  3000一轮
      d3.select(baseListNow[0]).transition().duration(1500).attr('cx', this.GLOBAL.relPx(330)).ease(d3.easeLinear).remove()
      let setClear1 = setTimeout(() => {
        baseListNow.forEach(ele => {
          d3.select(ele).transition().duration(300).attr('cy', Number(ele.attributes.cy.value) + this.GLOBAL.relPx(24)).ease(d3.easeLinear)
        })
      }, 2000);
      this.GLOBAL.timerArrayOut.push(setClear1)
      let setClear2 = setTimeout(() => {
        let randomNumber2 = Math.floor(Math.random() * 6);
        let addList = baseList[randomNumber2]
        let addData = addList[addList.length - 1]
        if (!addData) {
          addData = {
            attributes: {
              batch: {
                value: 0
              },
              index: {
                value: randomNumber2
              },
              cx: {
                value: this.GLOBAL.relPx(12 + 24 * randomNumber2)
              },
              cy: {
                value: this.GLOBAL.relPx(434)
              }
            }
          }
        }
        let colorIndex = Math.floor((Math.random() * color.length))
        let colorText = ''
        if (colorIndex == 0) {
          colorText = 'blue'
        }
        if (colorIndex == 1) {
          colorText = 'green'
        }
        if (colorIndex == 2) {
          colorText = 'yellow'
        }
        // filterColor
        svg.append('circle').attr('opacity', `${!this.filterColor || (this.filterColor && this.filterColor == colorText) ? 1 : 0.3}`).attr('colorType', `${colorText}`).attr('type', `base`).attr('batch', `${Number(addData.attributes.batch.value) + 1}`).attr('index', `${randomNumber2}`).attr('cx', `${Number(addData.attributes.cx.value)}`).attr('cy', 0).attr('r', this.GLOBAL.relPx(6)).attr('fill', color[colorIndex]).transition().duration(500).attr('cy', `${Number(addData.attributes.cy.value) - this.GLOBAL.relPx(24)}`).ease(d3.easeLinear)
      }, 2500);
      this.GLOBAL.timerArrayOut.push(setClear2)
      let setClear3 = setTimeout(() => {
        this.moveRound2()
      }, 500);
      this.GLOBAL.timerArrayOut.push(setClear3)
    },

    moveRound2() {
      let svg = this.svg
      let color = this.color
      let randomNumber = Math.floor(Math.random() * 6);

      let list = d3.select('#svgBox').selectAll('circle')._groups[0]
      let baseList = [[], [], [], [], [], []]
      list.forEach((ele, index) => {
        if (ele.attributes.type.value == 'base2') {
          baseList[ele.attributes.index.value].push(ele)
        }
      })
      let baseListNow = baseList[randomNumber]
      if (!baseListNow || baseListNow.length == 0) {
        for (var i = 0; i < baseList.length; i++) {
          if (!baseList[i] || baseList[i].length === 0) continue;
          if (baseList[i].length > 0) {
            baseListNow = baseList[i]
            break;
          }
        }
      }
      d3.select(baseListNow[0]).transition().duration(1500).attr('cx', this.GLOBAL.relPx(660)).ease(d3.easeLinear).remove()
      let setClear4 = setTimeout(() => {
        baseListNow.forEach(ele => {
          d3.select(ele).transition().duration(300).attr('cy', Number(ele.attributes.cy.value) + this.GLOBAL.relPx(24)).ease(d3.easeLinear)
        })
      }, 2000);
      this.GLOBAL.timerArrayOut.push(setClear4)

      let setClear5 = setTimeout(() => {
        let randomNumber2 = Math.floor(Math.random() * 6);
        let addList = baseList[randomNumber2]
        let addData = addList[addList.length - 1]
        if (!addData || !addData.attributes) {
          addData = {
            attributes: {
              batch: {
                value: 0
              },
              index: {
                value: randomNumber2
              },
              cx: {
                value: this.GLOBAL.relPx(347 + 24 * randomNumber2)
              },
              cy: {
                value: this.GLOBAL.relPx(434)
              }
            }
          }
        }
        let colorIndex2 = Math.floor((Math.random() * color.length))
        let colorText2 = ''
        if (colorIndex2 == 0) {
          colorText2 = 'blue'
        }
        if (colorIndex2 == 1) {
          colorText2 = 'green'
        }
        if (colorIndex2 == 2) {
          colorText2 = 'yellow'
        }
        svg.append('circle').attr('opacity', `${!this.filterColor || (this.filterColor && this.filterColor == colorText2) ? 1 : 0.3}`).attr('colorType', `${colorText2}`).attr('type', `base2`).attr('batch', `${Number(addData.attributes.batch.value) + 1}`).attr('index', `${randomNumber2}`).attr('cx', `${Number(addData.attributes.cx.value)}`).attr('cy', this.GLOBAL.relPx(0)).attr('r', this.GLOBAL.relPx(6)).attr('fill', color[colorIndex2]).transition().duration(500).attr('cy', `${Number(addData.attributes.cy.value) - this.GLOBAL.relPx(24)}`).ease(d3.easeLinear)
      }, 2500);
      this.GLOBAL.timerArrayOut.push(setClear5)

      let setClear6 = setTimeout(() => {
        this.moveRound3()
      }, 2000);
      this.GLOBAL.timerArrayOut.push(setClear6)

    },

    moveRound3() {
      let svg = this.svg
      let color = this.color
      let randomNumber = Math.floor(Math.random() * 6);
      let list = d3.select('#svgBox').selectAll('circle')._groups[0]
      let baseList = [[], [], [], [], [], []]
      list.forEach((ele, index) => {
        if (ele.attributes.type.value == 'base3') {
          baseList[ele.attributes.index.value].push(ele)
        }
      })
      let baseListNow = baseList[randomNumber]
      if (!baseListNow || baseListNow.length == 0) {
        for (var i = 0; i < baseList.length; i++) {
          if (!baseList[i] || baseList[i].length === 0) continue;
          if (baseList[i].length > 0) {
            baseListNow = baseList[i]
            break;
          }
        }
      }
      d3.select(baseListNow[0]).transition().duration(300).attr('cy', 450).ease(d3.easeLinear).remove()
      let setClear7 = setTimeout(() => {
        baseListNow.forEach(ele => {
          d3.select(ele).transition().duration(300).attr('cy', Number(ele.attributes.cy.value) + this.GLOBAL.relPx(24)).ease(d3.easeLinear)
        })
      }, 500);
      this.GLOBAL.timerArrayOut.push(setClear7)
      let setClear8 = setTimeout(() => {
        let randomNumber2 = Math.floor(Math.random() * 6);
        let addList = baseList[randomNumber2]
        let addData = addList[addList.length - 1]
        // let finalListNum = 0
        if (!addData || !addData.attributes) {
          addData = {
            attributes: {
              batch: {
                value: 0
              },
              index: {
                value: randomNumber2
              },
              cx: {
                value: this.GLOBAL.relPx(682 + 24 * randomNumber2)
              },
              cy: {
                value: this.GLOBAL.relPx(434)
              }
            }
          }
        }
        let colorIndex3 = Math.floor((Math.random() * color.length))
        let colorText3 = ''
        if (colorIndex3 == 0) {
          colorText3 = 'blue'
        }
        if (colorIndex3 == 1) {
          colorText3 = 'green'
        }
        if (colorIndex3 == 2) {
          colorText3 = 'yellow'
        }
        svg.append('circle').attr('opacity', `${!this.filterColor || (this.filterColor && this.filterColor == colorText3) ? 1 : 0.3}`).attr('colorType', `${colorText3}`).attr('type', `base3`).attr('batch', `${Number(addData.attributes.batch.value) + 1}`).attr('index', `${randomNumber2}`).attr('cx', `${Number(addData.attributes.cx.value)}`).attr('cy', this.GLOBAL.relPx(0)).attr('r', this.GLOBAL.relPx(6)).attr('fill', color[colorIndex3]).transition().duration(500).attr('cy', `${Number(addData.attributes.cy.value) - this.GLOBAL.relPx(24)}`).ease(d3.easeLinear)
        // if (finalListNum > 70) {
        //   this.reset()
        //   setTimeout(() => {
        //     this.initBall()
        //   }, 1000);
        // }
      }, 1000);
      this.GLOBAL.timerArrayOut.push(setClear8)
    },
    initBall2() {
      var height = this.GLOBAL.relPx(550)
      var width = this.GLOBAL.relPx(1300)
      var svg = d3.select('#svgTwo').append('svg').attr('height', height).attr('width', width)
      this.svgTwo = svg
      let timerTime = 0
      this.centerMoveTimer = setInterval(() => {
        svg.append('circle').attr('opacity', `${!this.filterColor || (this.filterColor && this.filterColor == 'blue') ? 1 : 0.3}`).attr('colorType', `blue`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(60)).attr('cy', this.GLOBAL.relPx(165)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#5470C6').transition().duration(36000).attr('cx', this.GLOBAL.relPx(1300)).ease(d3.easeLinear).remove()
        svg.append('circle').attr('opacity', `${!this.filterColor || (this.filterColor && this.filterColor == 'green') ? 1 : 0.3}`).attr('colorType', `green`).attr('type', `base2`).attr('cx', this.GLOBAL.relPx(60)).attr('cy', this.GLOBAL.relPx(335)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#91CC75').transition().duration(36000).attr('cx', this.GLOBAL.relPx(1300)).ease(d3.easeLinear).remove()
        svg.append('circle').attr('opacity', `${!this.filterColor || (this.filterColor && this.filterColor == 'yellow') ? 1 : 0.3}`).attr('colorType', `yellow`).attr('type', `base3`).attr('cx', this.GLOBAL.relPx(60)).attr('cy', this.GLOBAL.relPx(510)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#FAC858').transition().duration(36000).attr('cx', this.GLOBAL.relPx(1300)).ease(d3.easeLinear).remove()
        let arr1 = []
        let arr2 = []
        let arr3 = []
        for (let i = 0; i < 9; i++) {
          arr1.push(Math.floor(Math.random() * 100 + 1))
          arr2.push(Math.floor(Math.random() * 100 + 1))
          arr3.push(Math.floor(Math.random() * 100 + 1))
          svg.append('circle').attr('opacity', `${!this.filterColor || (this.filterColor && this.filterColor == 'blue') ? 1 : 0.3}`).attr('colorType', `blue`).attr('type', `blue${i}`).attr('cx', this.GLOBAL.relPx(140 + i * 139)).attr('cy', this.GLOBAL.relPx(130)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#5470C6').transition().duration(2350).attr('cy', this.GLOBAL.relPx(30)).ease(d3.easeLinear).remove()
          svg.append('circle').attr('opacity', `${!this.filterColor || (this.filterColor && this.filterColor == 'green') ? 1 : 0.3}`).attr('colorType', `green`).attr('type', `green${i}`).attr('cx', this.GLOBAL.relPx(160 + i * 139)).attr('cy', this.GLOBAL.relPx(335)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#91CC75').transition().duration(7400).attr('cy', this.GLOBAL.relPx(30)).ease(d3.easeLinear).remove()
          svg.append('circle').attr('opacity', `${!this.filterColor || (this.filterColor && this.filterColor == 'yellow') ? 1 : 0.3}`).attr('colorType', `yellow`).attr('type', `green${i}`).attr('cx', this.GLOBAL.relPx(180 + i * 139)).attr('cy', this.GLOBAL.relPx(510)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#FAC858').transition().duration(11100).attr('cy', this.GLOBAL.relPx(30)).ease(d3.easeLinear).remove()
        }
        this.listNumArrar = arr1
        this.listNumArrar2 = arr2
        this.listNumArrar3 = arr3

      }, 1000);
      this.GLOBAL.timerArraySet.push(this.centerMoveTimer)

    },
    initBall3() {
      var height = this.GLOBAL.relPx(470)
      var width = this.GLOBAL.relPx(850)
      var svg = d3.select('#svgThree').append('svg').attr('height', height).attr('width', width)
      this.svgThree = svg
      for (let i = 0; i < 14; i++) {
        for (let k = 0; k < 20; k++) {
          svg.append('circle').attr('colorType', `blue`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(6 + 23 * i)).attr('cy', this.GLOBAL.relPx(6 + 23 * k)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#5470C6')
        }
      }
      for (let i = 0; i < 7; i++) {
        for (let k = 0; k < 8; k++) {
          svg.append('circle').attr('colorType', `yellow`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(330 + 23 * i)).attr('cy', this.GLOBAL.relPx(6 + 23 * k)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#FAC858')
        }
      }
      for (let i = 0; i < 7; i++) {
        for (let k = 0; k < 12; k++) {
          svg.append('circle').attr('colorType', `green`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(330 + 23 * i)).attr('cy', this.GLOBAL.relPx(190 + 23 * k)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#91CC75')
        }
      }

      for (let i = 0; i < 14; i++) {
        for (let k = 0; k < 5; k++) {
          svg.append('circle').attr('colorType', `blue`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(534 + 23 * i)).attr('cy', this.GLOBAL.relPx(6 + 23 * k)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#5470C6')
        }
      }
      for (let i = 0; i < 12; i++) {
        for (let k = 0; k < 6; k++) {
          svg.append('circle').attr('colorType', `green`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(534 + 23 * i)).attr('cy', this.GLOBAL.relPx(120 + 23 * k)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#91CC75')
        }
      }
      for (let i = 0; i < 2; i++) {
        for (let k = 0; k < 6; k++) {
          svg.append('circle').attr('colorType', `yellow`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(812 + 23 * i)).attr('cy', this.GLOBAL.relPx(120 + 23 * k)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#FAC858')
        }
      }

      for (let i = 0; i < 3; i++) {
        for (let k = 0; k < 3; k++) {
          svg.append('circle').attr('colorType', `blue`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(534 + 23 * i)).attr('cy', this.GLOBAL.relPx(288 + 23 * k)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#5470C6')
        }
      }
      for (let i = 0; i < 2; i++) {
        for (let k = 0; k < 2; k++) {
          svg.append('circle').attr('colorType', `blue`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(606 + 23 * i)).attr('cy', this.GLOBAL.relPx(288 + 23 * k)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#5470C6')
        }
      }
      svg.append('circle').attr('colorType', `green`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(606)).attr('cy', this.GLOBAL.relPx(334)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#91CC75')
      svg.append('circle').attr('colorType', `blue`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(630)).attr('cy', this.GLOBAL.relPx(334)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#5470C6')

      for (let i = 0; i < 3; i++) {
        for (let k = 0; k < 4; k++) {
          svg.append('circle').attr('colorType', `blue`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(534 + 23 * i)).attr('cy', this.GLOBAL.relPx(372 + 23 * k)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#5470C6')
        }
      }
      for (let i = 0; i < 2; i++) {
        for (let k = 0; k < 3; k++) {
          svg.append('circle').attr('colorType', `green`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(608 + 23 * i)).attr('cy', this.GLOBAL.relPx(372 + 23 * k)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#91CC75')
        }
      }
      for (let i = 0; i < 2; i++) {
        for (let k = 0; k < 1; k++) {
          svg.append('circle').attr('colorType', `yellow`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(608 + 23 * i)).attr('cy', this.GLOBAL.relPx(442 + 23 * k)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#FAC858')
        }
      }
      for (let i = 0; i < 8; i++) {
        for (let k = 0; k < 5; k++) {
          svg.append('circle').attr('colorType', `green`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(675 + 23 * i)).attr('cy', this.GLOBAL.relPx(288 + 23 * k)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#91CC75')
        }
      }
      for (let i = 0; i < 5; i++) {
        for (let k = 0; k < 3; k++) {
          svg.append('circle').attr('colorType', `blue`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(675 + 23 * i)).attr('cy', this.GLOBAL.relPx(400 + 23 * k)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#5470C6')
        }
      }
      for (let i = 0; i < 3; i++) {
        for (let k = 0; k < 3; k++) {
          svg.append('circle').attr('colorType', `yellow`).attr('type', `base`).attr('cx', this.GLOBAL.relPx(790 + 23 * i)).attr('cy', this.GLOBAL.relPx(400 + 23 * k)).attr('r', this.GLOBAL.relPx(6)).attr('fill', '#FAC858')
        }
      }
    },
    reset() {
      this.B0 = 0
      this.B1 = 0
      this.B2 = 0
      this.B3 = 0
      this.B4 = 0
      this.B5 = 0
      this.B0_1 = 0
      this.B1_1 = 0
      this.B2_1 = 0
      this.B3_1 = 0
      this.B4_1 = 0
      this.B5_1 = 0
      this.B0_2 = 0
      this.B1_2 = 0
      this.B2_2 = 0
      this.B3_2 = 0
      this.B4_2 = 0
      this.B5_2 = 0
      if (this.base1MoveTimer) {
        clearInterval(this.base1MoveTimer)
      }
      if (this.numChangeTimer) {
        clearInterval(this.numChangeTimer)
      }
      d3.select('#svgBox').select('svg').remove()
      // if (document.getElementById('svgBox').innerHTML) {
      //   document.getElementById('svgBox').innerHTML = ''
      // }
    },
  },
  beforeDestroy() {
    this.reset()
    if (this.centerMoveTimer) {
      clearInterval(this.centerMoveTimer)
    }
  },
};
</script>

<style lang="scss" scoped>
.page4-1 {
  .mainBox {
    margin-top: 115px;
    position: relative;
    .leftBox {
      // width: 1010px;
      position: relative;
      transform: scale(0.82) translateX(-100px);

      .lenged {
        padding-top: 220px;

        .item {
          margin-bottom: 48px;

          .round {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-top: 2px;
          }

          .name {
            font-size: 16px;
            line-height: 16px;
            margin-left: 10px;
          }

          &.sty1 {
            .round {
              background-color: #5470C6;
            }
          }

          &.sty2 {
            .round {
              background-color: #91CC75;
            }
          }

          &.sty3 {
            .round {
              background-color: #FAC858;
            }
          }
        }
      }

      .viewCore {
        background-image: url('~@/assets/images/page4-1/view.png');
        background-size: cover;
        width: 851px;
        height: 581px;

        .topNumCore {
          padding: 0 50px;
          padding-top: 45px;
          font-size: 40px;
          box-sizing: border-box;

          span {
            font-size: 14px;
          }
        }

        .bottomWordCore {
          font-size: 18px;
          color: #00E4FF;
          padding: 10px 65px 0 65px;
          text-align: center;
        }

        .svgBox {
          width: 820px;
          height: 430px;
          margin: 0 auto;
        }

        &.on {
          opacity: 0;
        }
      }

      .changeBox {
        margin-top: 46px;
        margin-left: 360px;
        cursor: pointer;

        .icon {
          width: 28px;
          height: 28px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .text {
          font-size: 26px;
          padding-right: 10px;
          line-height: 28px;
        }
      }

      .menu {
        width: 585px;
        height: 557px;
        position: absolute;
        z-index: 2;
        top: 20px;
        left: 130px;
        
        .item {
          width: 193px;
          height: 181px;
          background-color: rgba($color: #ffffff, $alpha: 0.1);
          box-sizing: border-box;
          padding-top: 33px;

          img {
            width: 70px;
            height: 70px;
            margin: 0 auto;
            display: block;
          }

          .img1 {
            display: none;
          }

          p {
            text-align: center;
            font-size: 24px;
            color: #ffffff;
          }

          &.on {
            background-color: #28A2CE;
          }

          &.ani {
            animation: transopacity 1s ease-in 1 forwards;
          }

          &:nth-child(8),
          &:nth-child(4),
          &:nth-child(1),
          &:nth-child(2),
          &:nth-child(9) {
            cursor: pointer;

            &:hover {
              background-color: #28A2CE;

              .img2 {
                display: none;
              }

              .img1 {
                display: block;
              }
            }
          }
        }
      }
    }

    .rightCore {
      // width: 1300px;
      transform: scale(0.82);
      position: absolute;
      top: 0;
      right: -100px;
      .centerBox {
        width: 1300px;
        position: relative;

        .svgTwo {
          position: absolute;
          top: 0;
          left: 0;
          width: 1300px;
          height: 550px;
          z-index: 0;
        }

        .provice {
          padding-left: 140px;
          position: relative;
          z-index: 1;
        }

        .item {
          margin-top: 120px;
          position: relative;
          z-index: 1;

          .icon {
            width: 45px;
            height: 45px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .son {
            width: 50px;
            height: 50px;
            background: #1A191C;
            border: 1px solid #5470C6;
            border-radius: 50%;
            text-align: center;
            line-height: 50px;
            font-size: 16px;
          }

          &.sty1 {
            .son {
              border-color: #5470C6;
            }
          }

          &.sty2 {
            .son {
              border-color: #91CC75;
            }
          }

          &.sty3 {
            .son {
              border-color: #FAC858;
            }
          }

          &.opacity03 {
            opacity: 0.3;
          }
        }

        .lenged {
          margin-top: 50px;

          .item {
            margin: 0 30px;
            cursor: pointer;

            .round {
              width: 12px;
              height: 12px;
              border-radius: 50%;
              margin-top: 2px;
            }

            .name {
              font-size: 16px;
              line-height: 16px;
              margin-left: 10px;
            }

            &.sty1 {
              .round {
                background-color: #5470C6;
              }
            }

            &.sty2 {
              .round {
                background-color: #91CC75;
              }
            }

            &.sty3 {
              .round {
                background-color: #FAC858;
              }
            }

            &.on {
              &.sty1 {
                .name {
                  color: #5470C6;
                }
              }

              &.sty2 {
                .name {
                  color: #91CC75;
                }
              }

              &.sty3 {
                .name {
                  color: #FAC858;
                }
              }
            }
          }
        }

        .bottomTitle {
          font-size: 26px;
          text-align: center;
          margin-top: 30px;
        }
      }

      .rightBox {
        width: 850px;
        position: relative;
        margin-top: 20px;
        margin-right: 100px;
        display: none;

        .posiBox {
          &.on {
            opacity: 0;
          }
        }

        .svgThree {
          width: 100%;

          &.on {
            opacity: 0;
          }
        }

        .svgFour {
          width: 100%;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;

          &.on {
            opacity: 0;
          }
        }

        .posi {
          position: absolute;
          text-align: center;
          background: #1A191C;
          border: 1px solid #22355E;
          border-radius: 50%;
          box-sizing: border-box;
          cursor: pointer;

          .icon {
            margin: 0 auto;

            img {
              width: 100%;
              height: 100%;
            }
          }

          &.posi1 {
            width: 209px;
            height: 209px;
            top: 130px;
            left: 142px;

            .word {
              font-size: 68px;
              padding-top: 25px;
              padding-bottom: 15px;
            }

            span {
              font-size: 15px;
            }

            .icon {
              width: 152px;
              height: 43px;
            }
          }

          &.posi2 {
            width: 185px;
            height: 185px;
            top: 34px;
            left: 598px;

            .word {
              font-size: 66px;
              padding-top: 25px;
              padding-bottom: 15px;
            }

            span {
              font-size: 20px;
            }

            .icon {
              width: 119px;
              height: 27px;
            }
          }

          &.posi3 {
            width: 133px;
            height: 133px;
            top: 312px;
            left: 695px;

            .word {
              font-size: 44px;
              padding-top: 15px;
              padding-bottom: 5px;
            }

            span {
              font-size: 13px;
            }

            .icon {
              width: 108px;
              height: 23px;
            }
          }

          &.posi4 {
            width: 73px;
            height: 73px;
            top: 369px;
            left: 545px;

            .word {
              font-size: 25px;
              padding-top: 15px;
              padding-bottom: 5px;
            }

            span {
              font-size: 7px;
            }

            .text {
              font-size: 9px;
            }
          }

          &.posi5 {
            width: 51px;
            height: 51px;
            top: 285px;
            left: 532px;

            .word {
              font-size: 24px;
              padding-top: 5px;
              padding-bottom: 5px;
            }

            span {
              font-size: 7px;
            }

            .text {
              font-size: 9px;
            }
          }

          &.posi6 {
            width: 36px;
            height: 36px;
            top: 280px;
            left: 598px;

            .word {
              font-size: 18px;
              padding-top: 5px;
              padding-bottom: 5px;
            }

            span {
              font-size: 6px;
            }

            .text {
              font-size: 7px;
            }
          }
        }

        .bottomTitle {
          font-size: 26px;
          text-align: center;
          margin-top: 50px;
        }

        .bottomChnage {
          margin-top: 20px;
          opacity: 0;
          height: 69px;

          .item1 {
            width: 137px;
            height: 69px;
            background-image: url('~@/assets/images/page4-1/btc-1.png');
            background-size: cover;
            font-size: 16px;
            text-align: center;
            box-sizing: border-box;
            padding-top: 30px;
            cursor: pointer;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .item2 {
            width: 133px;
            height: 69px;
            background-image: url('~@/assets/images/page4-1/btc-2.png');
            background-size: cover;
            font-size: 16px;
            text-align: center;
            box-sizing: border-box;
            padding-top: 30px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          &.on {
            opacity: 1;
          }
        }
      }
    }
  }

  .centerWord {
    border: 1px solid #ff0000;
  }

  .imgPosi {
    background-image: url('~@/assets/images/page4-1/img.png');
    background-size: cover;
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
  }
}</style>
