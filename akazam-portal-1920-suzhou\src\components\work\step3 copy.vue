<template>
  <div class="pageWork3 flex-box-between">
    <div class="leftStep flex-box">
      <div class="stepList">
        <div class="item on al" @click="endFun(1)">作业入池</div>
        <div class="arraw"></div>
        <div class="item on al" @click="endFun(2)">资源池调度</div>
        <div class="arraw"></div>
        <div class="item on">模型训练</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(4)">应用部署</div>
        <div class="arraw"></div>
        <div class="item" @click="endFun(5)">推理验证</div>
      </div>
      <div class="wordList">
        <p :class="wordAni1 ? 'ani' : ''">作业名称：智算作业-图像分类学习</p>
        <p :class="wordAni2 ? 'ani' : ''">学习框架：PyTorch</p>
        <p :class="wordAni3 ? 'ani' : ''">上传数据集：data_url</p>
        <p :class="wordAni4 ? 'ani' : ''">配置输出数据集目录：mnist-output</p>
        <p :class="wordAni5 ? 'ani' : ''">运行资源池：天翼云-内蒙古</p>
      </div>
    </div>
    <div class="rightBox flex-box-between">
      <div class="left">
        <div class="imageBox">
          <div class="posiArrow flex-box" :class="imgAni4 ? 'ani' : ''">
            <img src="~@/assets/images/page4/step3/arrow-2.png" alt="">
            <div class="word on">迁移训练</div>
          </div>
          <div class="flex-box top">
            <div class="img img1" :class="imgAni1 ? 'ani' : ''">
              <div class="word">图像数据集</div>
              <img src="~@/assets/images/page4/step3/img1.png" alt="">
            </div>
            <div class="arrow arrow1" :class="imgAni2 ? 'ani' : ''">
              <div class="word">预训练</div>
              <img src="~@/assets/images/page4/step3/arrow1.png" alt="">
              <!-- <div class="word">(picture data)</div> -->
            </div>
            <div class="img img2" :class="imgAni3 ? 'ani' : ''">
              <div class="word">卷积神经网络</div>
              <img src="~@/assets/images/page4/step3/img2.png" alt="">
            </div>
          </div>
          <div class="flex-box down">
            <div class="img img3" :class="imgAni5 ? 'ani' : ''">
              <!-- <div class="word">Connectivity-strength based Weight ICVF</div> -->
              <img src="~@/assets/images/page4/step3/img4.png" alt="">
              <!-- <div class="word word2 flex-box">
                <div>JME</div>
                <div>NC</div>
              </div> -->
              <div class="word">训练数据</div>
            </div>
            <div class="arrow arrow1 on" :class="imgAni6 ? 'ani' : ''">
              <div class="word">训练模型</div>
              <img src="~@/assets/images/page4/step3/arrow1.png" alt="">
              <!-- <div class="word">(Neural data)</div> -->
            </div>
            <div class="opbox">
              <div class="img img4" :class="imgAni7 ? 'ani' : ''">
                <img src="~@/assets/images/page4/step3/img3.png" alt="">
              </div>
              <div class="arrowBox flex-box">
                <div class="arrow arrowl" :class="imgAni7 ? 'ani' : ''">
                  <img src="~@/assets/images/page4/step3/long.png" alt="">
                  <div class="word">特征抽取</div>
                </div>
                <div class="arrow arrows" :class="imgAni7 ? 'ani' : ''">
                  <img src="~@/assets/images/page4/step3/short.png" alt="">
                  <div class="word">分类器设计</div>
                </div>
              </div>
            </div>
            <div class="img img5" :class="imgAni8 ? 'ani' : ''">
              <img src="~@/assets/images/page4/step3/img5.png" alt="">
              <div class="word">分类模型</div>
            </div>
          </div>
        </div>
        <div class="lineBox" :class="lineBoxAni ? 'ani' : ''">
          <div class="line">
            <div class="inner" :style="{ 'width': `${percent}%` }"></div>
          </div>
          <div class="word">{{ statusText }}</div>
        </div>
      </div>
      <div class="rightImgBox flex-box-end">
        <div class="right" :class="rightAni ? 'ani' : ''" v-show="!showImgList">
          <div class="flex-box-center">
            <div class="item div1">
              <div class="word">{{ num1 }}<span>%</span></div>
              <div class="label">内存利用率</div>
            </div>
            <div class="item div2">
              <div class="word">{{ num2 }}<span>%</span></div>
              <div class="label">CPU利用率</div>
            </div>
          </div>
          <div class="flex-box-between">
            <div class="item div3">
              <div class="word">{{ num3 }}<span>%</span></div>
              <div class="label">GPU-0利用率</div>
            </div>
            <div class="item big div4">
              <div class="icon"><img src="~@/assets/images/page4/step3/icon.png" alt=""></div>
              <div class="label">资源消耗概况</div>
            </div>
            <div class="item div5">
              <div class="word">{{ num5 }}<span>%</span></div>
              <div class="label">GPU-0显存利用率</div>
            </div>
          </div>
          <div class="flex-box-center">
            <div class="item div6">
              <div class="word">{{ num6 }}<span>%</span></div>
              <div class="label">GPU-1利用率</div>
            </div>
            <div class="item div7">
              <div class="word">{{ num7 }}<span>%</span></div>
              <div class="label">GPU-1显存利用率</div>
            </div>
          </div>
        </div>
        <div class="changeBox flex-box" @click="showImgList = !showImgList" :class="showButton ? 'ani' : ''">
          <div class="text"><span v-if="!showImgList">作业性能概览</span><span v-else>作业输出结果</span></div>
          <div class="icon"><img src="~@/assets/images/page4-1/change.png" alt=""></div>
        </div>

        <div class="imgList" v-show="showImgList">
          <div class="son flex-box" :class="img1Ani ? 'ani' : ''">
            <div class="name">飞机</div>
            <div class="img"><img src="~@/assets/images/page4/step3/img10-1.png" alt=""></div>
          </div>
          <div class="son flex-box" :class="img2Ani ? 'ani' : ''">
            <div class="name">汽车</div>
            <div class="img"><img src="~@/assets/images/page4/step3/img10-2.png" alt=""></div>
          </div>
          <div class="son flex-box" :class="img3Ani ? 'ani' : ''">
            <div class="name">鸟</div>
            <div class="img"><img src="~@/assets/images/page4/step3/img10-3.png" alt=""></div>
          </div>
          <div class="son flex-box" :class="img4Ani ? 'ani' : ''">
            <div class="name">猫</div>
            <div class="img"><img src="~@/assets/images/page4/step3/img10-4.png" alt=""></div>
          </div>
          <div class="son flex-box" :class="img5Ani ? 'ani' : ''">
            <div class="name">鹿</div>
            <div class="img"><img src="~@/assets/images/page4/step3/img10-5.png" alt=""></div>
          </div>
          <div class="son flex-box" :class="img6Ani ? 'ani' : ''">
            <div class="name">狗</div>
            <div class="img"><img src="~@/assets/images/page4/step3/img10-6.png" alt=""></div>
          </div>
          <div class="son flex-box" :class="img7Ani ? 'ani' : ''">
            <div class="name">青蛙</div>
            <div class="img"><img src="~@/assets/images/page4/step3/img10-7.png" alt=""></div>
          </div>
          <div class="son flex-box" :class="img8Ani ? 'ani' : ''">
            <div class="name">马</div>
            <div class="img"><img src="~@/assets/images/page4/step3/img10-8.png" alt=""></div>
          </div>
          <div class="son flex-box" :class="img9Ani ? 'ani' : ''">
            <div class="name">船</div>
            <div class="img"><img src="~@/assets/images/page4/step3/img10-9.png" alt=""></div>
          </div>
          <div class="son flex-box" :class="img10Ani ? 'ani' : ''">
            <div class="name">卡车</div>
            <div class="img"><img src="~@/assets/images/page4/step3/img10-10.png" alt=""></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getStore, setStore } from "@/common/util";

export default {
  name: 'workstep2',
  data() {
    return {
      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      wordAni4: false,
      wordAni5: false,
      imgAni1: false,
      imgAni2: false,
      imgAni3: false,
      imgAni4: false,
      imgAni5: false,
      imgAni6: false,
      imgAni7: false,
      imgAni8: false,
      rightAni: false,
      num1: 80,
      num2: 79,
      num3: 70,
      num4: 86,
      num5: 86,
      num6: 80,
      num7: 63,
      timer: null,
      timer2: null,
      lineBoxAni: null,
      percent: 0,
      timerpercent: null,
      img1Ani: false,
      img2Ani: false,
      img3Ani: false,
      img4Ani: false,
      img5Ani: false,
      img6Ani: false,
      img7Ani: false,
      img8Ani: false,
      img9Ani: false,
      img10Ani: false,
      showImgList: false,
      showButton: false,
      statusText: '作业执行中',
      isAuto: null,
    }
  },
  created() {
    this.isAuto = this.GLOBAL.isAuto
  },
  mounted() {
    this.init()
  },
  methods: {
    lineAni() {
      this.timerpercent = setInterval(() => {
        this.percent += 1
        if (this.percent >= 100) {
          clearInterval(this.timerpercent)
        }
      }, 40);
    },
    numRandom() {
      this.timer = setInterval(() => {
        Math.random() >= 0.5 && this.num1 < 100 ? this.num1 += 1 : this.num1 -= 1
        Math.random() >= 0.5 && this.num3 < 100 ? this.num3 += 1 : this.num3 -= 1
        Math.random() >= 0.5 && this.num4 < 100 ? this.num4 += 1 : this.num4 -= 1
        Math.random() >= 0.5 && this.num6 < 100 ? this.num6 += 1 : this.num6 -= 1
      }, 800);
      this.timer2 = setInterval(() => {
        Math.random() >= 0.5 && this.num2 < 100 ? this.num2 += 1 : this.num2 -= 1
        Math.random() >= 0.5 && this.num5 < 100 ? this.num5 += 1 : this.num5 -= 1
        Math.random() >= 0.5 && this.num7 < 100 ? this.num7 += 1 : this.num7 -= 1
      }, 1300);
    },
    init() {
      let settimer1 = setTimeout(() => {
        this.wordAni1 = true
      }, 500);
      let settimer2 = setTimeout(() => {
        this.wordAni2 = true
      }, 1000);
      let settimer3 = setTimeout(() => {
        this.wordAni3 = true
        this.imgAni1 = true
        this.lineBoxAni = true
        this.lineAni()
      }, 1500);
      let settimer4 = setTimeout(() => {
        this.wordAni4 = true
        this.imgAni2 = true
      }, 2000);
      let settimer5 = setTimeout(() => {
        this.wordAni5 = true
        this.imgAni3 = true
        this.rightAni = true
      }, 2500);
      let settimer6 = setTimeout(() => {
        this.imgAni4 = true
        this.numRandom()
      }, 3000);
      let settimer7 = setTimeout(() => {
        this.imgAni5 = true
      }, 3500);
      let settimer8 = setTimeout(() => {
        this.imgAni6 = true
      }, 4000);
      let settimer9 = setTimeout(() => {
        this.imgAni7 = true
      }, 4500);
      let settimer10 = setTimeout(() => {
        this.imgAni8 = true
      }, 5000);
      let settimer11 = setTimeout(() => {
        this.showImgList = true
        this.img1Ani = true
      }, 5500);
      let settimer12 = setTimeout(() => {
        this.img2Ani = true
        this.statusText = '作业执行完成'
      }, 6000);
      let settimer13 = setTimeout(() => {
        this.img3Ani = true
      }, 6500);
      let settimer14 = setTimeout(() => {
        this.img4Ani = true
      }, 7000);
      let settimer15 = setTimeout(() => {
        this.img5Ani = true
      }, 7500);
      let settimer16 = setTimeout(() => {
        this.img6Ani = true
      }, 8000);
      let settimer17 = setTimeout(() => {
        this.img7Ani = true
      }, 8500);
      let settimer18 = setTimeout(() => {
        this.img8Ani = true
      }, 9000);
      let settimer19 = setTimeout(() => {
        this.img9Ani = true
      }, 9500);
      let settimer20 = setTimeout(() => {
        this.img10Ani = true
        this.showButton = true
        this.$emit('stepEnd', 3)
      }, 10000);

      this.$once('hook:beforeDestroy', () => {
        clearTimeout(settimer1)
        settimer1 = null;
        clearTimeout(settimer2)
        settimer2 = null;
        clearTimeout(settimer3)
        settimer3 = null;
        clearTimeout(settimer4)
        settimer4 = null;
        clearTimeout(settimer5)
        settimer5 = null;
        clearTimeout(settimer6)
        settimer6 = null;
        clearTimeout(settimer7)
        settimer7 = null;
        clearTimeout(settimer8)
        settimer8 = null;
        clearTimeout(settimer9)
        settimer9 = null;
        clearTimeout(settimer10)
        settimer10 = null;
        clearTimeout(settimer11)
        settimer11 = null;
        clearTimeout(settimer12)
        settimer12 = null;
        clearTimeout(settimer13)
        settimer13 = null;
        clearTimeout(settimer14)
        settimer14 = null;
        clearTimeout(settimer15)
        settimer15 = null;
        clearTimeout(settimer16)
        settimer16 = null;
        clearTimeout(settimer17)
        settimer17 = null;
        clearTimeout(settimer18)
        settimer18 = null;
        clearTimeout(settimer19)
        settimer19 = null;
        clearTimeout(settimer20)
        settimer20 = null;
      })
    },
    endFun(index) {
      if (this.isAuto) {
        return false
      }
      this.$emit('endFun', index)
    },
    resetFun() {
      if (this.timerpercent) {
        clearInterval(this.timerpercent)
      }
      if (this.timer) {
        clearInterval(this.timer)
      }
      if (this.timer2) {
        clearInterval(this.timer2)
      }
    }
  },
  beforeDestroy() {
    this.resetFun()
  },
}
</script>

<style lang="scss">
.pageWork3 {

  .rightBox {
    width: 2400px;
    box-sizing: border-box;
    padding-right: 150px;

    .left {
      margin-top: 170px;

      .imageBox {
        text-align: center;
        position: relative;

        .posiArrow {
          position: absolute;
          top: 170px;
          left: 670px;
          opacity: 0;

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }

          img {
            width: 29px;
            height: 99px;
          }
        }

        .img {
          opacity: 0;

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }

        .arrow {
          opacity: 0;

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }

        .top {
          margin-bottom: 30px;
        }

        .down{
          padding-top: 40px;
        }

        .word {
          padding: 10px 0;
          &.on{
            transform: translate(10px, 30px);
          }
        }

        .word2 {
          div {
            &:first-child {
              padding-left: 50px;
            }

            &:nth-child(2) {
              padding-left: 150px;
            }
          }
        }

        .img1 {
          margin-left: 100px;

          img {
            width: 226px;
            height: 142px;
          }
        }

        .arrow1 {
          margin: 70px 35px 0 35px;
          &.on{
            margin-top: 30px;
          }
          img {
            width: 148px;
            height: 29px;
          }
        }

        .img2 {
          img {
            width: 370px;
            height: 151px;
          }
        }

        .img3 {
          .word{
            margin-top: 20px;
          }
          img {
            width: 337px;
            height: 156px;
          }
        }

        .img4 {
          img {
            width: 444px;
            height: 151px;
          }
        }

        .img5 {
          margin-left: 30px;
          margin-top: 20px;

          img {
            width: 129px;
            height: 127px;
          }
        }

        .arrowl {
          margin-right: 10px;

          img {
            width: 281px;
            height: 23px;
          }
        }

        .arrows {
          margin-left: 10px;

          img {
            width: 110px;
            height: 23px;
          }
        }
      }

      .lineBox {
        margin-top: 50px;
        opacity: 0;

        .line {
          width: 815px;
          height: 10px;
          border-radius: 5px;
          background-color: #1A191C;
          margin: 0 auto;

          .inner {
            background-image: url('~@/assets/images/page4/step4/percent.png');
            width: 0%;
            height: 10px;
            border-radius: 5px;
          }
        }

        .word {
          text-align: center;
          padding-top: 20px;
          font-size: 26px;
        }

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }

    .right {
      background-image: url('~@/assets/images/page4/step3/right.png');
      background-size: cover;
      width: 675px;
      height: 647px;
      margin-top: 90px;
      opacity: 0;

      &.ani {
        animation: imgOpacity 1s linear 1 forwards;
      }

      .item {
        width: 200px;
        height: 200px;
        box-sizing: border-box;
        padding-top: 60px;
        text-align: center;

        .word {
          font-size: 48px;

          span {
            font-size: 18px;
          }
        }

        .icon {
          width: 74px;
          height: 74px;
          margin: 0 auto;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .label {
          font-size: 18px;
          margin-top: 10px;
        }

        &.div1 {
          margin-right: 30px;
        }

        &.div2 {
          margin-left: 10px;
        }

        &.div3 {
          margin-top: 10px;
        }

        &.div4 {
          width: 250px;
          margin-left: 10px;
          margin-top: 10px;
        }

        &.div5 {
          margin-left: 10px;
          margin-top: 10px;
        }

        &.div6 {
          margin-right: 30px;
          margin-top: 10px;
        }

        &.div7 {
          margin-left: 10px;
          margin-top: 10px;
        }
      }
    }

    .rightImgBox {

      width: 730px;
      position: relative;
    }

    .changeBox {
      cursor: pointer;
      position: absolute;
      bottom: -50px;
      left: 50%;
      margin-left: -70px;
      opacity: 0;

      .icon {
        width: 28px;
        height: 28px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .text {
        font-size: 26px;
        padding-right: 10px;
        line-height: 28px;
      }

      &.ani {
        animation: imgOpacity 0.5s linear 1 forwards;
      }
    }

    .imgList {
      position: absolute;
      top: 150px;
      left: -30px;
      z-index: 1;

      .son {
        margin-bottom: 5px;
        opacity: 0;

        .name {
          width: 165px;
          line-height: 51px;
        }

        .img {
          width: 558px;
          height: 51px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        &.ani {
          animation: imgOpacity 0.5s linear 1 forwards;
        }
      }
    }
  }
}

@keyframes imgOpacity {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}</style>
