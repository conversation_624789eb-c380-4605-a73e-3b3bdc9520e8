<template>
  <div class="roundBox">
    <div class="round">
      <div class="roundBg"></div>
      <div class="text">{{ value }}<span>G</span></div>
      <div class="wave-svg-shape" :style="`height: ${value > 100 ? 100 : value}%;`">
        <svg class="wave-svg" xmlns="http://www.w3.org/2000/svg" data-name="3-waves" viewBox="0 0 600 215.43">
          <title>wave shape</title>
          <path class="871c1787-a7ef-4a54-ad03-3cd50e05767a"
            d="M639,986.07c-17-1-27.33-.33-40.5,2.67s-24.58,11.84-40.46,15c-13.56,2.69-31.27,2.9-46.2,1.35-17.7-1.83-35-9.06-35-9.06S456,987.07,439,986.07s-27.33-.33-40.5,2.67-24.58,11.84-40.46,15c-13.56,2.69-31.27,2.9-46.2,1.35-17.7-1.83-35-9.06-35-9.06S256,987.07,239,986.07s-27.33-.33-40.5,2.67-24.58,11.84-40.46,15c-13.56,2.69-31.27,2.9-46.2,1.35-17.7-1.83-35-9.06-35-9.06v205.06h600V996S656,987.07,639,986.07Z"
            transform="translate(-76 -985)" fill="#1B5E93"></path>
        </svg>
      </div>
    </div>
  </div>
</template>
<script>

export default {
  name: 'headerCommon',
  props: {
    value: {
      type: Number,
      default: 15
    },
    fill: {
      type: String,
      default: '#1B5E93'
    },
    
  },
  data() {
    return {
      navList: [],
      navListIndex: 0
    }
  },
  created() {

  },
  mounted() {
  },
  methods: {
    
  },
  beforeDestroy() {
  },
}
</script>

<style lang="scss">
.roundBox {
  width: 68px;
  height: 68px;
  background-color: rgba($color: #1480eb, $alpha: 0.1);
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 0 auto;
  text-align: center;
  .round {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin: 5px auto 0 auto;
    font-size: 24px;
    // background-image: url('~@/assets/images/page4/step1/round.png');
    // background-size: cover;
    line-height: 60px;
    position: relative;
    overflow: hidden;

    .text {
      position: relative;
      z-index: 3;
    }

    span {
      font-size: 12px;
    }

    .wave-svg-shape {
      position: absolute;
      bottom: 0px;
      left: 0px;
      width: 100%;
      height: 60%;
      overflow: hidden;
      animation: fillUpSvg 2s ease-in-out;
      transition: all .3s;
      z-index: 2;

      .wave-svg {
        width: 100%;
        min-width: 300%;
        height: auto;
        overflow: hidden;
        float: left;
        fill: #1B5E93;
        margin: 0;
        animation: waveSvgAnim 2s linear infinite;
        opacity: 0.5;
      }
    }

    .roundBg {
      position: absolute;
      z-index: 3;
      width: 100%;
      height: 100%;
      background-image: url('~@/assets/images/page4/round.png');
      background-size: cover;
      top: 0;
      left: 0;
    }
  }
}

@keyframes fillUpSvg {

  0% {
    transform: translateY(100%) scaleY(0);
  }

  100% {
    transform: translateY(0) scaleY(1);
  }
}


@keyframes waveSvgAnim {

  0% {
    transform: translate(-66%);
  }

  100% {
    transform: translate(-33%)
  }
}
</style>

