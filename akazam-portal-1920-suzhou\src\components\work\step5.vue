<template>
  <div class="pageWork5 flex-box-between">
    <div class="leftStep flex-box">
      <div class="stepList">
        <div class="item on al" @click="endFun(1)">基础配置</div>
        <div class="arraw on al"></div>
        <div class="item on al" @click="endFun(2)">资源池分配</div>
        <div class="arraw on al"></div>
        <div class="item on" @click="endFun(3)">运行设置</div>
        <div class="arraw on al"></div>
        <div class="item on al" @click="endFun(4)">模型训练</div>
        <div class="arraw on al"></div>
        <div class="item on al" @click="endFun(5)">推理部署</div>
        <div class="arraw"></div>
        <div class="item on">访问验证</div>
      </div>
      <div class="wordList">
        <p :class="wordAni1 ? 'ani' : ''">应用名称：图像识别-app16na84</p>
        <p :class="wordAni2 ? 'ani' : ''">上传图片：</p>
        <div :class="wordAni3 ? 'ani' : ''" class="upload"><img src="~@/assets/images/page4/step5/upload-test.png" alt="">
        </div>
        <!-- <div id="box">
          <canvas id="canvas"></canvas>
          <button @click="addTile(50)">50</button>
          <button @click="addTile(40)">40</button>
          <button @click="addTile(30)">30</button>
          <button @click="addTile(20)">20</button>
          <button @click="addTile(10)">10</button>
          <button @click="addTile(8)">8</button>
          <button @click="addTile(5)">5</button>
          <button @click="addTile(3)">3</button>
          <button @click="addTile(0)">还原</button>
          <button @click="addTileAuto(40)">自动</button>
        </div> -->
      </div>
    </div>
    <div class="rightBox flex-box-between">
      <div class="left">
        <div class="imgCore flex-box">
          <div class="img img1" :class="imgAni1 ? 'ani' : ''"><img src="~@/assets/images/page4/step5/img1.png" alt="">
            <div class="line line1"></div>
            <div class="word word1">卷积核</div>
          </div>
          <div class="img img2" :class="imgAni2 ? 'ani' : ''"><img src="~@/assets/images/page4/step5/img2.png" alt="">
            <div class="line line2"></div>
          </div>
          <div class="img img3" :class="imgAni3 ? 'ani' : ''"><img src="~@/assets/images/page4/step5/img3.png" alt="">
            <div class="line line3"></div>
            <div class="word word2">池化层</div>
            <div class="word word3">卷积层+激活层</div>
          </div>
          <div class="img img4" :class="imgAni4 ? 'ani' : ''"><img src="~@/assets/images/page4/step5/img4.png" alt="">
            <div class="line line4"></div>
          </div>
          <div class="img img5" :class="imgAni5 ? 'ani' : ''"><img src="~@/assets/images/page4/step5/img5.png" alt="">
            <div class="line line5"></div>
            <div class="line line6"></div>
          </div>
          <div class="img img6" :class="imgAni6 ? 'ani' : ''"><img src="~@/assets/images/page4/step5/img6.png" alt="">
            <div class="word word4">展平层</div>
          </div>
          <div class="img img7" :class="imgAni7 ? 'ani' : ''"><img src="~@/assets/images/page4/step5/img7.png" alt="">
          </div>
          <div class="img img8" :class="imgAni8 ? 'ani' : ''"><img src="~@/assets/images/page4/step5/img8.png" alt="">
            <div class="word word5">
              <p>马</p>
              <p>斑马</p>
              <p>狗</p>
            </div>
            <div class="word word6">
              <p>分类激活函数</p>
            </div>
          </div>
        </div>
        <div class="lineCore">
          <div class="line" :class="lineAni ? 'ani' : ''">
            <div class="inner" :style="{ 'width': `${percent}%` }"></div>
          </div>
        </div>
        <div class="wordBottom" :class="lineWordAni ? 'ani' : ''">作业测试完成</div>
      </div>
      <div class="right">
        <div class="imgCore" v-show="rightImgAni">
          <img src="~@/assets/images/page4/step5/img-r8.png" :class="rightImgAni ? 'ani' : ''" alt="">
          <div class="imgBlur" :class="rightImgAni ? 'ani' : ''" alt=""></div>
          <!-- <img src="~@/assets/images/page4/step5/img-r1.png" :class="imgIndex === 0 ? 'ani' : ''" alt="">
          <img src="~@/assets/images/page4/step5/img-r2.png" :class="imgIndex === 1 ? 'ani' : ''" alt="">
          <img src="~@/assets/images/page4/step5/img-r3.png" :class="imgIndex === 2 ? 'ani' : ''" alt="">
          <img src="~@/assets/images/page4/step5/img-r4.png" :class="imgIndex === 3 ? 'ani' : ''" alt="">
          <img src="~@/assets/images/page4/step5/img-r5.png" :class="imgIndex === 4 ? 'ani' : ''" alt="">
          <img src="~@/assets/images/page4/step5/img-r6.png" :class="imgIndex === 5 ? 'ani' : ''" alt="">
          <img src="~@/assets/images/page4/step5/img-r7.png" :class="imgIndex === 6 ? 'ani' : ''" alt="">
          <img src="~@/assets/images/page4/step5/img-r8.png" :class="imgIndex === 7 ? 'ani' : ''" alt=""> -->
          <div class="imgEnd" :class="imgEndShow ? 'ani' : ''">测试结果：斑马-Zebra</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getStore, setStore } from "@/common/util";
import Mosaic from 'image-mosaic';

export default {
  name: 'workstep2',
  data() {
    return {
      imgSrc: require('../../assets/images/page4/step5/upload-test.png'),
      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      imgAni1: false,
      imgAni2: false,
      imgAni3: false,
      imgAni4: false,
      imgAni5: false,
      imgAni6: false,
      imgAni7: false,
      imgAni8: false,
      imgIndex: null,
      lineAni: null,
      lineWordAni: null,
      percent: 0,
      imgEndShow: false,
      timerpercent: null,
      rightImgAni: false,
      isAuto: false
    }
  },
  created() {
    this.isAuto = this.GLOBAL.isAuto
  },
  mounted() {
    this.init()
    // this.initMosaic()
  },
  methods: {
    addTileAuto(size) {
      let _this = this
      let timer = setInterval(() => {
        _this.addTile(size)
        size -= 1
        if (size < 0) {
          clearInterval(timer)
        }
      }, 500);
    },
    addTile(size) {
      this.drawImageToCanvas().then(ctx => {
        const mosaic = new Mosaic(ctx, {
          tileWidth: size <= 0 ? 10 : size,
          tileHeight: size <= 0 ? 10 : size,
        })
        if (size <= 0) {
          mosaic.eraseAllTiles()
          return false
        }
        mosaic.drawAllTiles()
      })
    },
    clearTile() {
    },
    initMosaic() {
      this.drawImageToCanvas().then(ctx => {
        const mosaic = new Mosaic(ctx, {
          tileWidth: 40,
          tileHeight: 40,
        })
        mosaic.drawAllTiles(40)
        // const MouseEvents = {
        //   init() {
        //     mosaic.context.canvas.addEventListener('mousedown', MouseEvents.mousedown)
        //   },
        //   mousedown() {
        //     mosaic.context.canvas.addEventListener('mousemove', MouseEvents.mousemove)
        //     document.addEventListener('mouseup', MouseEvents.mouseup)
        //   },
        //   mousemove(e) {
        //     if (e.shiftKey) {
        //       mosaic.eraseTileByPoint(e.layerX, e.layerY)
        //       return
        //     }
        //     mosaic.drawTileByPoint(e.layerX, e.layerY)
        //   },
        //   mouseup() {
        //     mosaic.context.canvas.removeEventListener('mousemove', MouseEvents.mousemove)
        //     document.removeEventListener('mouseup', MouseEvents.mouseup)
        //   }
        // }
        MouseEvents.init()
        // document.querySelector('#drawAll').addEventListener('click', () => {
        //   mosaic.drawAllTiles(10)
        // })
        // document.querySelector('#clearAll').addEventListener('click', () => {
        //   mosaic.eraseAllTiles()
        // })
      })
    },
    drawImageToCanvas() {
      const canvas = document.querySelector('#canvas')
      const ctx = canvas.getContext('2d')
      let imageUrl
      if (this.imgSrc) {
        imageUrl = this.imgSrc
      }
      return new Promise((resolve) => {
        const image = new Image()
        image.crossOrigin = 'Annoymous'
        image.onload = function () {
          canvas.width = image.width
          canvas.height = image.height
          ctx.drawImage(this, 0, 0, image.width, image.height)
          resolve(ctx)
        }
        image.src = imageUrl
      })
    },
    endFun(index) {
      if (this.isAuto) {
        return false
      }
      this.$emit('endFun', index)
    },
    lineAniFun() {
      this.timerpercent = setInterval(() => {
        this.percent += 1
        if (this.percent >= 100) {
          clearInterval(this.timerpercent)
        }
      }, 30);
    },
    init() {
      setTimeout(() => {
        this.wordAni1 = true
      }, 500);
      setTimeout(() => {
        this.wordAni2 = true
      }, 1000);
      setTimeout(() => {
        this.wordAni3 = true
      }, 1500);
      setTimeout(() => {
        this.imgAni1 = true
      }, 2000);
      setTimeout(() => {
        this.imgAni2 = true
        this.imgIndex = 0
        this.rightImgAni = true
        this.lineAni = true
        this.lineAniFun()
      }, 2500);
      setTimeout(() => {
        this.imgAni3 = true
        this.imgIndex = 1
      }, 3000);
      setTimeout(() => {
        this.imgAni4 = true
        this.imgIndex = 2
      }, 3500);
      setTimeout(() => {
        this.imgAni5 = true
        this.imgIndex = 3
      }, 4000);
      setTimeout(() => {
        this.imgAni6 = true
        this.imgIndex = 4
      }, 4500);
      setTimeout(() => {
        this.imgAni7 = true
        this.imgIndex = 5
      }, 5000);
      setTimeout(() => {
        this.imgAni8 = true
        this.imgIndex = 6
      }, 5500);
      setTimeout(() => {
        this.imgIndex = 7
        this.lineWordAni = true
      }, 6000);
      setTimeout(() => {
        this.imgEndShow = true
      }, 7500);
    },
    resetFun() {
      if (this.timerpercent) {
        clearInterval(this.timerpercent)
      }
    },
  },
  beforeDestroy() {
    this.resetFun()
  },
}
</script>

<style lang="scss">
.pageWork5 {
  .rightBox {
    width: 2600px;
    box-sizing: border-box;
    padding-right: 50px;

    .left {
      margin-top: 270px;

      .imgCore {
        .img {
          position: relative;
          opacity: 0;

          img {
            width: 100%;
            height: 100%;
          }

          .word {
            position: absolute;
            z-index: 1;
            text-align: center;
            font-size: 20px;

            p {
              margin: 0;
              margin-bottom: 10px;
            }

            &.word1 {
              bottom: -35px;
              left: 44px;
            }

            &.word2 {
              top: -35px;
              left: 40px;
            }

            &.word3 {
              width: 200px;
              bottom: -23px;
              left: -30px;
            }

            &.word4 {
              width: 120px;
              left: -35px;
            }

            &.word5 {
              top: 10px;
              right: -55px;
            }

            &.word6 {
              width: 200px;
              left: -60px;
            }
          }

          .line {
            position: absolute;
            z-index: 1;
            background-size: cover;

            &.line1 {
              top: 98px;
              left: 82px;
              background-image: url('~@/assets/images/page4/step5/line1.png');
              width: 173px;
              height: 29px;
            }

            &.line2 {
              top: 49px;
              left: 72px;
              background-image: url('~@/assets/images/page4/step5/line2.png');
              width: 147px;
              height: 25px;
            }

            &.line3 {
              top: 89px;
              left: 70px;
              background-image: url('~@/assets/images/page4/step5/line3.png');
              width: 139px;
              height: 21px;
            }

            &.line4 {
              top: 47px;
              left: 76px;
              background-image: url('~@/assets/images/page4/step5/line4.png');
              width: 124px;
              height: 36px;
            }

            &.line5 {
              top: -66px;
              left: 10px;
              background-image: url('~@/assets/images/page4/step5/line5.png');
              width: 137px;
              height: 74px;
            }

            &.line6 {
              bottom: -29px;
              right: -47px;
              background-image: url('~@/assets/images/page4/step5/line6.png');
              width: 99px;
              height: 43px;
            }
          }

          &.img1 {
            width: 144px;
            height: 141px;
            margin-top: 97px;
            margin-right: 30px;
          }

          &.img2 {
            width: 148px;
            height: 158px;
            margin-top: 95px;
            margin-right: 10px;
          }

          &.img3 {
            width: 133px;
            height: 141px;
            margin-top: 110px;
            margin-right: 10px;
          }

          &.img4 {
            width: 117px;
            height: 122px;
            margin-top: 117px;
            margin-right: 5px;
          }

          &.img5 {
            width: 100px;
            height: 114px;
            margin-top: 124px;
            margin-right: 37px;
          }

          &.img6 {
            width: 38px;
            height: 229px;
            margin-top: 52px;
          }

          &.img7 {
            width: 288px;
            height: 329px;
          }

          &.img8 {
            width: 98px;
            height: 117px;
            margin-top: 104px;
          }

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }
      }

      .lineCore {
        margin-top: 150px;

        .line {
          width: 815px;
          height: 10px;
          background: #1A191C;
          border-radius: 5px;
          margin: 0 auto;
          opacity: 0;

          .inner {
            background-image: url('~@/assets/images/page4/step4/percent.png');
            width: 0%;
            height: 10px;
            border-radius: 5px;
          }

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }
      }

      .wordBottom {
        font-size: 26px;
        color: #FFFFFF;
        text-align: center;
        margin-top: 30px;
        opacity: 0;

        &.ani {
          animation: imgOpacity 1s linear 1 forwards;
        }
      }
    }

    .right {
      margin-top: 170px;

      .imgCore {
        width: 1172px;
        height: 558px;
        position: relative;


        img {
          width: 100%;
          height: 100%;
          display: block;
          z-index: 0;
          position: absolute;
          top: 0;
          left: 0;
          // opacity: 0;
          filter: grayscale(100%);

          &.ani2 {
            animation: imgOpacity 0.45s linear 1 forwards;
          }

          &.ani {
            animation: grayscaleAni 4s linear 1 forwards;
          }
        }

        .imgEnd {
          width: 100%;
          height: 100%;
          background-color: rgba($color: #000, $alpha: 0.7);
          font-size: 48px;
          color: #ffffff;
          text-align: center;
          line-height: 558px;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 3;
          opacity: 0;

          &.ani {
            animation: imgOpacity 0.45s linear 1 forwards;
          }
        }

        .imgBlur {
          width: 100%;
          height: 100%;
          font-size: 24px;
          color: #ffffff;
          text-align: center;
          line-height: 558px;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 2;
          backdrop-filter: blur(20px);

          &.ani {
            animation: easeBlur 4s linear 1 forwards;
          }
        }

      }
    }
  }
}

@keyframes imgOpacity {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes easeBlur {
  0% {
    backdrop-filter: blur(20px);
  }

  100% {
    backdrop-filter: blur(0px);
  }
}


@keyframes grayscaleAni {
  0% {
    filter: grayscale(100%);
  }

  100% {
    filter: grayscale(0%);
  }
}
</style>
