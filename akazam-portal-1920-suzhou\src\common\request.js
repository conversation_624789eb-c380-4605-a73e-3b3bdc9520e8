import axios from 'axios';
import env from '@/config/env';
import Router from '@/router/index'
import qs from 'qs';

const MOCKURL = ''; // mock数据地址

/**
 * 自定义Axios实例
 */
const AJAX = axios.create({
    baseURL: env.baseUrl,
    timeout: 30000,
    withCredentials: env.credential
});

// request拦截器
AJAX.interceptors.request.use(
    config => {
      // if (config.method  === 'post') {
      //   config.data = qs.stringify(config.data);
      // }
        // if (config.method == 'post') {
        //   config.data = {
        //     ...config.data,
        //     _t: Date.parse(new Date()) / 1000
        //   }
        // } else if (config.method == 'get') {
        //   config.params = {
        //     ...config.params,
        //     _t: Date.parse(new Date()) / 1000
        //   }
        // }
        // var token = ''
        // if (localStorage.getItem('token')) {
        //     token = localStorage.getItem('token')
        // }
        var headers = {
            // "accessToken": token,
            'Content-Type': 'application/json'
        }
        config.headers = headers
        return config;
    },
    error => {
        Promise.reject(error)
    }
)

// response 拦截器
AJAX.interceptors.response.use(
    (response) => {
      const res = response.data
      if (!res || Array.isArray(res)) {
        return res
      }
      if (res.status != 200) {
        return Promise.reject(res)
      } else {
        return res
      }
    },
    (error) => {
      let code = error.response.status
      if ( code == 404) {
      } else if (code == 502) {
      } else {
        return Promise.reject(error.response)
      }
    }
  )


export default AJAX
