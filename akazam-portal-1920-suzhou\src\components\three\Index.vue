<template>
  <div>
    <div id="layerMain">
      <div>{{ countryName }}</div>
      <div class="shape"></div>
    </div>
    <div ref="mapId" class="earth"></div>
  </div>
</template>
<script>
import * as THREE from "three"
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js"
import * as TWEEN from "tween"
import map_img from '../../assets/images/page1/map.jpg'
import map_img_bg from '../../assets/images/page1/mapbg.png'
import map_img_light from '../../assets/images/page1/light.png'
import map_wl from '../../assets/images/page1/wl.png'
import map_china from '../../assets/images/page1/c-china.png'
import map_india from '../../assets/images/page1/c-india.png'
import map_usa from '../../assets/images/page1/c-usa.png'
import map_canada from '../../assets/images/page1/c-canada.png'
import map_england from '../../assets/images/page1/c-england.png'
import map_france from '../../assets/images/page1/c-france.png'
import map_germany from '../../assets/images/page1/c-germany.png'
import map_holland from '../../assets/images/page1/c-holland.png'
import map_japan from '../../assets/images/page1/c-japan.png'
import map_korea from '../../assets/images/page1/c-korea.png'
let camera, scene, controls, mesh;
let group = new THREE.Group();
let group2 = new THREE.Group();
let radius = 70;
let fov = 100;
export default {
  name: 'index',
  data() {
    return {
      mapDom: null,
      renderer: null,
      animationType: true, // 地球入场动画
      rotationY: false, // 地球自动旋转
      meshAnimateType: false, // 标记点动画
      lonlat: { x: 0, y: 0, z: 200 },
      countryName: null, // 数据
      speed: 0.003
    }
  },
  mounted() {
    this.info()
  },
  methods: {
    // 初始化
    info() {
      this.infoThree()
      this.infoBall()
      this.infoBallBg()
      this.createSprite()
      this.infoRender()
      // this.renderer.domElement.addEventListener("click", this.infoMouse)
    },

    // 基本配置
    infoThree() {
      // 场景
      scene = new THREE.Scene()
      // 渲染
      this.renderer = new THREE.WebGLRenderer({
        antialias: true,
      })
      this.mapDom = this.$refs.mapId
      this.renderer.setSize(this.mapDom.clientWidth, this.mapDom.clientHeight)
      this.renderer.setClearColor(0x000, 0)
      this.mapDom.appendChild(this.renderer.domElement)
      // 相机
      camera = new THREE.PerspectiveCamera(
        fov,
        this.mapDom.clientWidth / this.mapDom.clientHeight,
        1,
        1000
      )
      camera.position.set(0, 0, 100)
      camera.lookAt(0, 0, 0)
      // 鼠标
      // this.infoOrbitControls()
    },

    // 重新渲染
    infoRender() {
      this.renderer.clear()
      // 地球入场动画
      if (this.animationType) this.ballAnimation()
      // 地球旋转
      if (this.rotationY) this.ballRotationY()
      // 标记点动画
      // if (this.meshAnimateType) this.meshAnimate()
      this.renderer.render(scene, camera)
      requestAnimationFrame(this.infoRender)
      TWEEN.update()
    },

    // 鼠标
    infoOrbitControls() {
      controls = new OrbitControls(camera, this.renderer.domElement)
      controls.enableDamping = true
      controls.enableZoom = true
      controls.autoRotate = false
      controls.autoRotateSpeed = 2
      controls.enablePan = true
    },

    // 地球
    infoBall() {
      // 纹理贴图
      let textureLoader = new THREE.TextureLoader();
      textureLoader.load(map_img, function (texture) {
        // 创建球
        let geometry = new THREE.SphereGeometry(radius, 100, 100);
        let material = new THREE.MeshBasicMaterial({
          map: texture, //设置颜色贴图属性值
        });
        //网格模型对象Mesh
        mesh = new THREE.Mesh(geometry, material);
        // 唯一标识
        mesh.name = "ballMain";
        // mesh.rotateZ(Math.PI / 4)
        // 添加到场景中
        scene.add(mesh);
      });
    },

    // 地球
    infoBallBg() {
      // 纹理贴图
      let textureLoader = new THREE.TextureLoader();
      textureLoader.load(map_img_bg, function (texture) {
        // 创建球
        let geometry = new THREE.SphereGeometry(radius * 1.05, 101, 101);
        let material = new THREE.MeshBasicMaterial({
          map: texture, //设置颜色贴图属性值
          transparent: true,
          opacity: 0.1
        });
        //网格模型对象Mesh
        mesh = new THREE.Mesh(geometry, material);
        // 唯一标识
        mesh.name = "ballMainBg";
        // 添加到场景中
        scene.add(mesh);
      });
    },

    createSprite() {
      var textureLoader = new THREE.TextureLoader();
      var texture = textureLoader.load(map_img_light);//加载纹理贴图
      // 创建精灵材质对象SpriteMaterial
      var spriteMaterial = new THREE.SpriteMaterial({
        map: texture, //设置贴图
        transparent: true,//开启透明
        // opacity: 0.5,//通过透明度整体调节光圈
      });
      var sprite = new THREE.Sprite(spriteMaterial);
      sprite.scale.set(radius * 2.35, radius * 2.35, 1);//缩放精灵
      scene.add(sprite);
    },

    // 地球入场动画
    ballAnimation() {
      scene.rotation.y = 3
      scene.rotation.x = 0.6
      fov -= 1
      if (fov <= 45) {
        this.animationType = false
        camera.position.set(0, 0, 200)
        camera.lookAt(0, 0, 0)
        // this.infoOrbitControls()
      } else {
        camera = new THREE.PerspectiveCamera(
          fov,
          this.mapDom.clientWidth / this.mapDom.clientHeight,
          1,
          1000
        );
        camera.position.set(0, 0, 200)
        camera.lookAt(0, 0, 0)
      }
    },

    // 地球自动旋转
    ballRotationY() {
      scene.rotation.y -= this.speed
    },

    infoMark(item, index) {
      let _this = this
      let url = null
      let settime = 0
      if (item.name === '印度') {
        url = map_india
        settime = 1000
      }
      if (item.name === '中国') {
        url = map_china
        settime = 2000
      }
      if (item.name === '韩国') {
        url = map_korea
        settime = 3000
      }
      if (item.name === '日本') {
        url = map_japan
        settime = 4000
      }
      if (item.name === '美国') {
        url = map_usa
        settime = 6000
      }
      if (item.name === '加拿大') {
        url = map_canada
        settime = 7000
      }
      if (item.name === '英国') {
        url = map_england
        settime = 9000
      }
      if (item.name === '法国') {
        url = map_france
        settime = 10000
      }
      if (item.name === '德国') {
        url = map_germany
        settime = 11000
      }
      if (item.name === '荷兰') {
        url = map_holland
        settime = 12000
      }

      _this.infoMarkNext(item, url, settime, index)
      // setTimeout(() => {
      //   this.rotationY = false
      // }, 18500);
      // setTimeout(() => {
      //   scene.rotation.y = 2.91
      // }, 19000);
    },
    infoMarkNext(item, url, settime, index) {
      let _this = this
      let textureLoader = new THREE.TextureLoader();
      textureLoader.load(url, function (texture) {
        // 创建球
        let geometry = new THREE.SphereGeometry(radius, 100, 100);
        let material = new THREE.MeshBasicMaterial({
          map: texture, //设置颜色贴图属性值
          transparent: true,
          opacity: 0
        });
        //网格模型对象Mesh
        let mesh = new THREE.Mesh(geometry, material);
        // 唯一标识
        mesh.name = item.name + 'map'
        mesh.privateType = 'markmap'

        let cityGeometry = new THREE.PlaneBufferGeometry(1, 1) //默认在XOY平面上
        let cityWaveMaterial = new THREE.MeshBasicMaterial({
          // color: item.color,
          transparent: true,
          opacity: 0,
          side: THREE.DoubleSide
        })
        let mesh2 = new THREE.Mesh(cityGeometry, cityWaveMaterial)
        const coord = _this.lon2xyz(radius * 1.01, item.lon, item.lat)
        // mesh2.scale.set(2, 2, 2)
        // 唯一标识
        mesh2.name = item.name
        mesh2.privateType = 'mark'
        mesh2.position.set(coord.x, coord.y, coord.z)
        const coordVec3 = new THREE.Vector3(
          coord.x,
          coord.y,
          coord.z
        ).normalize()
        const meshNormal = new THREE.Vector3(0, 0, 1)
        mesh2.quaternion.setFromUnitVectors(meshNormal, coordVec3)
        if (scene.getObjectByName(item.name) === undefined) {
          //网格模型添加到场景中
          group.add(mesh)
          scene.add(group)
          _this.meshAnimateOpacity(settime, index)
          setTimeout(() => {
            // group2.add(mesh2)
            // scene.add(group, group2)
            _this.meshAnimateType = true
            if(item.name == '中国') {
              _this.rotationY = true
            }
            if (item.name == '日本') {
              setTimeout(() => {
                _this.speed = 0.015
              }, 500);
            }
            if (item.name == '美国') {
              setTimeout(() => {
                _this.speed = 0.003
              }, 500);
            }
            if (item.name == '加拿大') {
              setTimeout(() => {
                _this.speed = 0.015
              }, 500);
            }
            if (item.name == '英国') {
              setTimeout(() => {
                _this.speed = 0.002
              }, 500);
            }
            if (item.name == '荷兰') {
              _this.rotationY = false
              let timer = setInterval(() => {
                scene.rotation.y -= 0.01
                if (scene.rotation.y <= -3.4) {
                  clearInterval(timer)
                }
              }, 10);
            }
          }, settime + 2500);
          // scene.add(group2)
        }
      });
    },

    // 经纬度转坐标
    lon2xyz(R, longitude, latitude) {
      const lon = (Number(longitude) + 90) * (Math.PI / 180)
      const lat = Number(latitude) * (Math.PI / 180)
      const x = R * Math.cos(lat) * Math.sin(lon)
      const y = R * Math.sin(lat)
      const z = R * Math.cos(lon) * Math.cos(lat)
      return { x, y, z }
    },

    meshAnimateOpacity(settime, index) {
      if (group.children[index].privateType === "markmap") {
        setTimeout(() => {
          group.children[index].material.opacity = 1
        }, settime + 2500);
      }
    },


    // 以下方法暂时无用

    // 标记点动画
    meshAnimate() {
      for (let i = 0; i < group.children.length; i++) {
        // group.children[i].material.opacity = 1
        if (group.children[i].privateType === "markmap") {
          // 添加初始随机数，防止动画同步
          group.children[i].material.opacity += 0.1
          // group.children[i].scale.set(
          //   group.children[i].material.opacity + 7,
          //   group.children[i].material.opacity + 7,
          //   group.children[i].material.opacity + 7
          // )
          // if (group.children[i].material.opacity < 0) {
          //   group.children[i].material.opacity = 1
          // }
        }
      }
    },

    // 移动相机
    cameraPos(objList) {
      this.frameDivClose()
      let layerObj = scene.getObjectByName(objList.name)
      if (layerObj) {
        scene.rotation.y = 0
        // this.rotationY = false
        new TWEEN.Tween({ x: this.lonlat.x, y: this.lonlat.y, z: this.lonlat.z })
          .to({ x: layerObj.position.x * 2.8, y: layerObj.position.y * 2.8, z: layerObj.position.z * 2.8 }, 1500)
          .onUpdate(function () {
            camera.position.x = this.x
            camera.position.y = this.y
            camera.position.z = this.z
            camera.lookAt(0, 0, 0)
          })
          .onComplete(() => {
            this.retrievalLayer(objList.name)
          })
          .easing(TWEEN.Easing.Sinusoidal.InOut)
          .start()
        this.lonlat = camera.position
        // 弹窗面板赋值
        this.countryName = objList.name
      } else {
        alert('图层数据已被全部删除，请重新刷新界面，或者重新调用数据初始化方法: this.infoMap ()')
      }
    },

    // 检索指定的图层
    retrievalLayer(name) {
      let layerObj = scene.getObjectByName(name)
      this.infoDiv(layerObj.position.x, layerObj.position.y, layerObj.position.z)
    },

    // 鼠标事件（点击标记的点的事件）
    infoMouse(event) {
      event.preventDefault();
      const raycaster = new THREE.Raycaster();
      const mouse = new THREE.Vector2();
      // 通过鼠标点击位置,计算出 raycaster 所需点的位置,以屏幕为中心点,范围 -1 到 1
      let getBoundingClientRect = this.mapDom.getBoundingClientRect();
      mouse.x =
        ((event.clientX - getBoundingClientRect.left) /
          this.mapDom.offsetWidth) *
        2 -
        1;
      mouse.y =
        -(
          (event.clientY - getBoundingClientRect.top) /
          this.mapDom.offsetHeight
        ) *
        2 +
        1;
      //通过鼠标点击的位置(二维坐标)和当前相机的矩阵计算出射线位置
      raycaster.setFromCamera(mouse, camera);
      // 获取与射线相交的对象数组，其中的元素按照距离排序，越近的越靠前
      let intersects = raycaster.intersectObjects(scene.children);
      // 点击对象的处理
      for (let i = 0; i < intersects.length; i++) {
        if (intersects[i].object.name !== 'ballMain') {
          // 弹窗面板赋值
          this.countryName = intersects[i].object.name
          let objList = {
            name: intersects[i].object.name
          }
          this.cameraPos(objList)
          return false
        } else {
          // 开启自动旋转
          this.rotationY = true
          this.frameDivClose()
        }
      }
    },

    // 标签
    infoDiv(pointx, pointy, pointz) {
      // 坐标转换
      let world_vector = new THREE.Vector3(
        pointx,
        pointy,
        pointz
      )
      let vector = world_vector.project(camera)
      let halfWidth = this.mapDom.offsetWidth / 2,
        halfHeight = this.mapDom.offsetHeight / 2
      let x = Math.round(vector.x * halfWidth + halfWidth)
      let y = Math.round(-vector.y * halfHeight + halfHeight)
      //创建div容器
      let moonDiv = document.getElementById("layerMain")
      moonDiv.style.display = "block"
      moonDiv.style.left = x - 150 + "px"
      moonDiv.style.top = y - 180 + "px"
    },

    // 关闭标签
    frameDivClose() {
      let divHtml = document.getElementById("layerMain")
      divHtml.style.display = "none"
    },

    // 添加光柱
    infoColumn(item) {
      const material = new THREE.MeshBasicMaterial({
        color: item.color,
        transparent: true,
        opacity: .9,
        side: THREE.DoubleSide
      })
      const coord = this.lon2xyz(radius * 1.01, item.lon, item.lat)
      const coordVec3 = new THREE.Vector3(coord.x, coord.y, coord.z).normalize()
      const geometry = new THREE.CylinderGeometry(0.2, 2.8, 30)
      const mesh = new THREE.Mesh(geometry, material)
      mesh.name = item.name
      mesh.privateType = 'column'
      mesh.position.set(coord.x, coord.y, coord.z)
      mesh.quaternion.setFromUnitVectors(new THREE.Vector3(0, 1, 0), coordVec3)
      group.add(mesh)
      scene.add(group)
    },

    // 删除所有标记点
    delAll() {
      this.frameDivClose()
      group.traverse((item) => {
        if (item.type === 'Mesh') {
          item.geometry.dispose()
          item.material.dispose()
        }
      })
      scene.remove(group)
      // 删除group中的children
      if (group.children && group.children.length > 0) {
        let i = 0
        for (i; i < group.children.length; i++) {
          group.remove(group.children[i])
        }
      }
    },

    // 删除指定标记点
    delMark(item) {
      this.frameDivClose()
      let layerObj = scene.getObjectByName(item.name)
      group.remove(layerObj)
    },


  }
}
</script>

<style lang="scss">
#layerMain {
  position: absolute;
  width: 300px;
  height: 160px;
  line-height: 160px;
  text-align: center;
  color: white;
  display: none;
  background-color: rgba(34, 34, 35, .6);
  opacity: 0;

  .shape {
    position: absolute;
    margin: auto;
    left: 0;
    right: 0;
    width: 0;
    height: 0;
    bottom: -40px;
    border: 20px solid transparent;
    border-top-color: rgba(34, 34, 35, .6);
  }
}
</style>
