<template>
  <div class="pageWork2_3 flex-box-between">
    <div class="leftStep2">
      <div class="stepList flex-box">
        <div class="item on al" @click="endFun(1)">应用服务加载</div>
        <div class="arraw on al"></div>
        <div class="item on al" @click="endFun(2)">运行资源池</div>
        <div class="arraw on al"></div>
        <div class="item on flex-box-center">预测结果发布</div>
      </div>
      <div>
        <div class="wordList">
          <p :class="wordAni1 ? 'ani' : ''">
            应用作业：气象风险预测服务-慢阻肺，中暑
          </p>
          <p :class="wordAni2 ? 'ani' : ''" class="flex-box">
            <i v-if="iconAni1" class="el-icon-loading"></i>
            <img v-if="iconAni2" src="~@/assets/images/page4/step1/success.png" alt="" />
            {{ word1 }}
          </p>
        </div>
      </div>
    </div>
    <div class="rightBox flex-box-between">
      <div class="rightCore" v-show="!showPage2">
        <div class="rightitem left" :class="coreAni1 ? 'ani' : ''">
          <div class="flex-box-between">
            <div class="title">慢阻肺风险预测</div>
            <div class="fiveColor flex-box">
              <div class="item c1">低</div>
              <div class="item c2">轻微</div>
              <div class="item c3">中等</div>
              <div class="item c4">较高</div>
              <div class="item c5">高</div>
            </div>
          </div>
          <div class="newcore flex-box-between">
            <div class="lecore">
              <video class="video" src="~@/assets/images/1920/work3/video1.mp4" autoplay muted loop></video>
              <div class="posicore">
                <div class="item p1">
                  <round-common :value="25" :word="`地区人口`"></round-common>
                </div>
                <div class="item p2">
                  <round-common :value="25" :word="`气压`"></round-common>
                </div>
                <div class="item p3">
                  <round-common :value="25" :word="`气温`"></round-common>
                </div>
                <div class="item p4">
                  <round-common :value="25" :word="`温度`"></round-common>
                </div>
                <div class="item p5">
                  <round-common :value="25" :word="`大气污染`"></round-common>
                </div>
                <div class="item p6">
                  <round-common :value="25" :word="`风速`"></round-common>
                </div>
                <div class="item p7">
                  <round-common :value="25" :word="`健康因子`"></round-common>
                </div>
              </div>
            </div>
            <div class="lemap" ref="chinamap2"></div>
          </div>
          <div class="lineword">风险预测中</div>
          <div class="lineCore flex-box">
            <div class="line on">
              <div class="inner" :style="{ width: `${percent}%` }"></div>
            </div>
          </div>
        </div>
        <div class="rightitem left" :class="coreAni2 ? 'ani' : ''">
          <div class="flex-box-between">
            <div class="title">中暑风险预测</div>
            <div class="fiveColor flex-box">
              <div class="item c1">低</div>
              <div class="item c2">轻微</div>
              <div class="item c3">中等</div>
              <div class="item c4">较高</div>
              <div class="item c5">高</div>
            </div>
          </div>
          <div class="newcore flex-box-between">
            <div class="lecore2">
              <video class="video" src="~@/assets/images/1920/work3/video2.mp4" autoplay muted loop></video>
              <div class="word p1">辐照</div>
              <div class="word p2">气温</div>
              <div class="word p3">气压</div>
              <div class="word p4">日照</div>
            </div>
            <div class="lemap" ref="chinamap3"></div>
          </div>
          <div class="lineword">风险预测中</div>
          <div class="lineCore flex-box">
            <div class="line on">
              <div class="inner" :style="{ width: `${percent}%` }"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="rightCore" v-show="showPage2">
        <div class="rightitem left" :class="coreAni1 ? 'ani' : ''">
          <div class="flex-box-between">
            <div class="title">慢阻肺风险预测</div>
            <div class="time">数据更新：{{ getData }}</div>
          </div>
          <div class="levelCore flex-box-between">
            <div class="item flex-box-between">
              <div class="levelicon">
                <div class="icon" :class="`icon${todayData.copdLevel}`"></div>
                <div class="word">今日</div>
              </div>
              <div class="info">{{ todayData.copdAdvice }}</div>
            </div>
            <div class="item flex-box-between">
              <div class="levelicon">
                <div class="icon" :class="`icon${tomorrowData.copdLevel}`"></div>
                <div class="word">明日</div>
              </div>
              <div class="info">{{ tomorrowData.copdAdvice }}</div>
            </div>
          </div>
          <div class="title">风险预测趋势</div>
          <div class="lineEchartsCore" ref="mylineChart1"></div>
        </div>
        <div class="rightitem left" :class="coreAni2 ? 'ani' : ''">
          <div class="flex-box-between">
            <div class="title">中暑风险预测</div>
            <div class="time">数据更新：{{ getData }}</div>
          </div>
          <div class="levelCore flex-box-between">
            <div class="item flex-box-between">
              <div class="levelicon">
                <div class="icon" :class="`icon${todayData.zsLevel}`"></div>
                <div class="word">今日</div>
              </div>
              <div class="info">{{ todayData.zsAdvice }}</div>
            </div>
            <div class="item flex-box-between">
              <div class="levelicon">
                <div class="icon" :class="`icon${tomorrowData.zsLevel}`"></div>
                <div class="word">明日</div>
              </div>
              <div class="info">{{ tomorrowData.zsAdvice }}</div>
            </div>
          </div>
          <div class="title">风险预测趋势</div>
          <div class="lineEchartsCore" ref="mylineChart2"></div>
        </div>
      </div>
      <div class="rightCore">
        <div class="rightitem left" :class="coreAni2 ? 'ani' : ''">
          <div class="title">算力网络</div>
          <div class="map" ref="chinamap"></div>
          <!-- <div class="textposi">云专网:{{ speed }}MB/S</div> -->
        </div>
        <div class="rightitem left" :class="coreAni3 ? 'ani' : ''">
          <div class="title">资源池水位</div>
          <div class="fourCore flex-box-between" :class="fourCoreAni1 ? 'ani' : ''">
            <div class="son">
              <div class="t1">{{ num1 }}<span>%</span></div>
              <div class="t2">GPU利用率</div>
            </div>
            <div class="son">
              <div class="t1">{{ num2 }}<span>%</span></div>
              <div class="t2">CPU利用率</div>
            </div>
            <div class="son">
              <div class="t1">{{ num3 }}<span>%</span></div>
              <div class="t2">内存利用率</div>
            </div>
            <div class="son">
              <div class="t1">{{ num4 }}<span>%</span></div>
              <div class="t2">磁盘利用率</div>
            </div>
          </div>
          <div class="downtitle">上海气象局专属资源池</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getStore, setStore } from "@/common/util";
import VueMatrixRaindrop2 from "../vueMatrixDigitRain/index2.vue";
import * as d3 from "d3"; // d3
import roundCommon from "../round/Index4";
import circleProgressbar from "vue-circleprogressbar";
import Mosaic from "image-mosaic";
import * as echarts from "echarts";
import chinaMap from "@/assets/json/shanghai.json";

import { getJson } from "@/api/common";

export default {
  name: "workstep1",
  components: {
    VueMatrixRaindrop2,
    roundCommon,
    circleProgressbar,
  },
  props: {
    propsdata: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      point: [],
      percent: 0,
      percent2: 0,
      wordAni1: false,
      wordAni2: false,
      wordAni3: false,
      wordAni4: false,
      wordAni5: false,
      wordAni6: false,
      iconAni1: true,
      iconAni2: false,
      iconAni3: true,
      iconAni4: false,
      showCode: false,
      timer: null,
      timer2: null,
      itemAni1: false,
      itemAni2: false,
      percent2: 0,
      percentdesc: "上传训练数据集...",
      percentdesc2: "作业入池中...",
      percentdesc3: "作业入池中...",
      wordBottomAni: false,
      imgItemAni: false,
      imgItemAni2: false,
      time1: 0,
      time2: 0,
      time1Timer: null,
      time2Timer: null,
      timeDate: "",
      imgLeftAni: false,
      imgRightAni: false,
      ponit1Index: null,
      ponit2Index: null,
      pointAnitimer1: null,
      pointAnitimer2: null,
      wordBottomAni2: null,
      isAuto: null,
      centerOpa: false,
      rightOpa: false,

      fourCoreAni1: false,
      fourCoreAni2: false,
      num1: 0,
      num2: 0,
      num3: 0,
      num4: 0,
      num5: 0,
      num6: 0,
      num7: 0,
      num8: 0,
      word1: "预测结果发布",
      word1_2: "渲染执行中",
      time10: 10,
      tsCoreAni1: false,
      tsCoreAni2: false,
      lineAni1: false,
      lineAni2: false,
      timerpercent: null,
      timerpercent2: null,
      timerpercent3: null,
      showVideo: false,
      imgSrc2: require("../../assets/images/1920/work2/img.png"),
      timeCoreAni: false,
      downtextAni1: false,
      downtextAni2: false,
      showimgAni: false,
      lineBoxAni: false,
      heightAni: false,
      coreAni1: false,
      coreAni2: false,
      coreAni3: false,
      speed: 20,
      timerspeed: null,
      mapChart2: null,
      mapChart3: null,
      arr_copd: [],
      arr_copd2: [],
      arr_zs: [],
      arr_zs2: [],
      todayData: {},
      tomorrowData: {},
      getDate: "",
      lineChart1: null,
      lineChart2: null,
      getData: "",
      showPage2: false,
    };
  },
  created() {
    const date = new Date();
    const year = date.getFullYear(); // 获取当前年份，例如：2021
    const month = date.getMonth() + 1; // 获取当前月份，注意需要加1，例如：9
    const day = date.getDate(); // 获取当前日期，例如：22
    this.getData = `${year}/${month}/${day}`;
  },
  mounted() {
    echarts.registerMap("china", { geoJSON: chinaMap });
    this.init();
    this.speedRandom();
    this.initMap2();
    this.initMap3();
    this.numRandom();
  },
  methods: {
    initLine1() {
      let _this = this;
      if (!this.lineChart1) {
        this.lineChart1 = echarts.init(this.$refs.mylineChart1, null, {
          width: this.GLOBAL.relPx(550),
          height: this.GLOBAL.relPx(150),
        });
        this.GLOBAL.echartsDomArray.push(this.lineChart1);
      }
      let option = {
        xAxis: {
          type: "category",
          data: this.arr_copd,
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff",
            },
            // 这里重新定义就可以
            formatter: function (value) {
              const date = new Date(value);
              const year = date.getFullYear(); // 获取当前年份，例如：2021
              const month = date.getMonth() + 1; // 获取当前月份，注意需要加1，例如：9
              const day = date.getDate(); // 获取当前日期，例如：22
              return `${month}/${day}`;
            },
          },
        },
        grid: {
          left: "40",
          right: "20",
          top: "20",
          bottom: "20",
        },
        yAxis: {
          type: "value",
          min: 1,
          max: 5,
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: "rgba(255,255,255,0.1)",
            },
          },
          axisLabel: {
            show: true,
            splitNumber: 4,
            textStyle: {
              color: "#fff",
            },
            // 这里重新定义就可以
            formatter: function (value) {
              var texts = [];
              if (value === 1) {
                texts.push("低");
              } else if (value === 2) {
                texts.push("轻微");
              } else if (value === 3) {
                texts.push("中等");
              } else if (value === 4) {
                texts.push("较高");
              } else if (value === 5) {
                texts.push("高");
              }
              return texts;
            },
          },
        },
        series: [
          {
            data: this.arr_copd2,
            type: "line",
            smooth: true,
          },
        ],
      };
      this.lineChart1.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.lineChart1.setOption(option);
    },
    initLine2() {
      let _this = this;
      if (!this.lineChart2) {
        this.lineChart2 = echarts.init(this.$refs.mylineChart2, null, {
          width: this.GLOBAL.relPx(550),
          height: this.GLOBAL.relPx(150),
        });
        this.GLOBAL.echartsDomArray.push(this.lineChart2);
      }
      let option = {
        xAxis: {
          type: "category",
          data: this.arr_zs,
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff",
            },
            // 这里重新定义就可以
            formatter: function (value) {
              const date = new Date(value);
              const year = date.getFullYear(); // 获取当前年份，例如：2021
              const month = date.getMonth() + 1; // 获取当前月份，注意需要加1，例如：9
              const day = date.getDate(); // 获取当前日期，例如：22
              return `${month}/${day}`;
            },
          },
        },
        grid: {
          left: "40",
          right: "20",
          top: "20",
          bottom: "20",
        },
        yAxis: {
          type: "value",
          min: 1,
          max: 5,
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: "rgba(255,255,255,0.1)",
            },
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff",
            },
            // 这里重新定义就可以
            formatter: function (value) {
              var texts = [];
              if (value === 1) {
                texts.push("低");
              } else if (value === 2) {
                texts.push("轻微");
              } else if (value === 3) {
                texts.push("中等");
              } else if (value === 4) {
                texts.push("较高");
              } else if (value === 5) {
                texts.push("高");
              }
              return texts;
            },
          },
        },
        series: [
          {
            data: this.arr_zs2,
            type: "line",
            smooth: true,
          },
        ],
      };
      this.lineChart2.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.lineChart2.setOption(option);
    },
    generateDateArray(currentDate) {
      let dateArray = [];
      for (let i = -3; i <= 1; i++) {
        let date = new Date(currentDate);
        date.setDate(currentDate.getDate() + i);
        dateArray.push(this.formatDate(date));
      }
      return dateArray;
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    initDataJson() {
      let arr_copd = [];
      let arr_copd2 = [];
      let arr_zs = [];
      let arr_zs2 = [];
      let todayData = {
        copdLevel: "",
        copdAdvice: "",
        zsLevel: "",
        zsAdvice: "",
      };
      let tomorrowData = {
        copdLevel: "",
        copdAdvice: "",
        zsLevel: "",
        zsAdvice: "",
      };
      const date = new Date();
      let dateArray = this.generateDateArray(date).reverse();
      let list = []
      dateArray.forEach((ele) => {
        list.push({
          date: `${ele}  00:00:00`,
          "data": [{
            "name": "ZS",
            "date": `${ele}  00:00:00`,
            "value": "中暑\n${ele}\n气象风险等级：不易中暑\n预防建议：不易中暑，应注意饮食清淡，睡眠充足。"
          }, {
            "name": "COPD",
            "date": `${ele}  00:00:00`,
            "value": "COPD\n${ele}\n气象风险等级：较高\n防范人群：COPD患者\n预防建议：温差大，早晚请适当添衣，注意保暖。"
          }]
        })
      })
      let res = [{
        "date": "2024-09-25 00:00:00",
        "data": [{
          "name": "ZS",
          "date": "2024-09-25 00:00:00",
          "value": "中暑\n2024-01-09\n气象风险等级：不易中暑\n预防建议：不易中暑，应注意饮食清淡，睡眠充足。"
        }, {
          "name": "COPD",
          "date": "2024-09-25 00:00:00",
          "value": "COPD\n2024-01-09\n气象风险等级：较高\n防范人群：COPD患者\n预防建议：温差大，早晚请适当添衣，注意保暖。"
        }]
      }, {
        "date": "2024-09-24 00:00:00",
        "data": [{
          "name": "ZS",
          "date": "2024-09-24 00:00:00",
          "value": "中暑\n2024-01-08\n气象风险等级：不易中暑\n预防建议：不易中暑，应注意饮食清淡，睡眠充足。"
        }, {
          "name": "COPD",
          "date": "2024-09-24 00:00:00",
          "value": "COPD\n2024-01-08\n气象风险等级：较高\n防范人群：COPD患者\n预防建议：温差较大，早晚请适当添衣。"
        }]
      }, {
        "date": "2024-09-23 00:00:00",
        "data": [{
          "name": "ZS",
          "date": "2024-09-23 00:00:00",
          "value": "中暑\n2024-01-07\n气象风险等级：不易中暑\n预防建议：不易中暑，应注意饮食清淡，睡眠充足。"
        }, {
          "name": "COPD",
          "date": "2024-09-23 00:00:00",
          "value": "COPD\n2024-01-07\n气象风险等级：较高\n防范人群：COPD患者\n预防建议：温差较大，早晚请适当添衣。"
        }]
      }, {
        "date": "2024-09-22 00:00:00",
        "data": [{
          "name": "ZS",
          "date": "2024-09-22 00:00:00",
          "value": "中暑\n2024-01-06\n气象风险等级：不易中暑\n预防建议：不易中暑，应注意饮食清淡，睡眠充足。"
        }, {
          "name": "COPD",
          "date": "2024-09-22 00:00:00",
          "value": "COPD\n2024-01-06\n气象风险等级：较高\n防范人群：COPD患者\n预防建议：细颗粒物PM2.5浓度较高，减少长时间户外活动，室内适当开启空气净化器；温差大，早晚请适当添衣，注意保暖。"
        }]
      }, {
        "date": "2024-09-21 00:00:00",
        "data": [{
          "name": "ZS",
          "date": "2024-09-21 00:00:00",
          "value": "中暑\n2024-01-05\n气象风险等级：不易中暑\n预防建议：不易中暑，应注意饮食清淡，睡眠充足。"
        }, {
          "name": "COPD",
          "date": "2024-09-21 00:00:00",
          "value": "COPD\n2024-01-05\n气象风险等级：较高\n防范人群：COPD患者\n预防建议：温差大，早晚请适当添衣，注意保暖。"
        }]
      }]
      res = list
      if (res && res.length > 0) {
        res.forEach((ele, index) => {
          let arr = [];
          if (ele.data && ele.data.length === 4) {
            arr = [ele.data[2], ele.data[3]];
          } else {
            arr = [ele.data[0], ele.data[1]];
          }
          arr.forEach((ele2) => {
            if (ele2.value.indexOf("COPD") > -1) {
              let strarr = ele2.value.split("\n");
              let level = "";
              strarr.forEach((ele3) => {
                if (ele3.indexOf("等级") > -1) {
                  if (ele3.indexOf("等级：不易") > -1) {
                    level = 1;
                  } else if (ele3.indexOf("等级：高") > -1) {
                    level = 5;
                  } else if (ele3.indexOf("等级：较高") > -1) {
                    level = 4;
                  } else if (ele3.indexOf("等级：中") > -1) {
                    level = 3;
                  } else {
                    level = 2;
                  }
                }
              });
              arr_copd.push(ele.date);
              arr_copd2.push(level);
            }
            if (ele2.value.indexOf("中暑") > -1) {
              let strarr = ele2.value.split("\n");
              let level = "";
              strarr.forEach((ele3) => {
                if (ele3.indexOf("等级") > -1) {
                  if (ele3.indexOf("等级：不易") > -1) {
                    level = 1;
                  } else if (ele3.indexOf("等级：高") > -1) {
                    level = 5;
                  } else if (ele3.indexOf("等级：较高") > -1) {
                    level = 4;
                  } else if (ele3.indexOf("等级：中") > -1) {
                    level = 3;
                  } else {
                    level = 2;
                  }
                }
              });
              arr_zs.push(ele.date);
              arr_zs2.push(level);
            }
          });
        });
        let predata = res[0].data;
        let predataToday = res[1].data;
        let arr = [predata[0], predata[1]];
        let arr2 = [predataToday[0], predataToday[1]];
        arr.forEach((ele) => {
          if (ele.value.indexOf("COPD") > -1) {
            let strarr = ele.value.split("\n");
            strarr.forEach((ele2) => {
              if (ele2.indexOf("等级") > -1) {
                if (ele2.indexOf("等级：不易") > -1) {
                  tomorrowData.copdLevel = 1;
                } else if (ele2.indexOf("等级：高") > -1) {
                  tomorrowData.copdLevel = 5;
                } else if (ele2.indexOf("等级：较高") > -1) {
                  tomorrowData.copdLevel = 4;
                } else if (ele2.indexOf("等级：中") > -1) {
                  tomorrowData.copdLevel = 3;
                } else {
                  tomorrowData.copdLevel = 2;
                }
              }
              if (ele2.indexOf("预防建议") > -1) {
                tomorrowData.copdAdvice = ele2;
              }
            });
          }
          if (ele.value.indexOf("中暑") > -1) {
            let strarr = ele.value.split("\n");
            strarr.forEach((ele2) => {
              if (ele2.indexOf("等级") > -1) {
                if (ele2.indexOf("等级：不易") > -1) {
                  tomorrowData.zsLevel = 1;
                } else if (ele2.indexOf("等级：高") > -1) {
                  tomorrowData.zsLevel = 5;
                } else if (ele2.indexOf("等级：较高") > -1) {
                  tomorrowData.zsLevel = 4;
                } else if (ele2.indexOf("等级：中") > -1) {
                  tomorrowData.zsLevel = 3;
                } else {
                  tomorrowData.zsLevel = 2;
                }
              }
              if (ele2.indexOf("预防建议") > -1) {
                tomorrowData.zsAdvice = ele2;
              }
            });
          }
        });
        arr2.forEach((ele) => {
          if (ele.value.indexOf("COPD") > -1) {
            let strarr = ele.value.split("\n");
            strarr.forEach((ele2) => {
              if (ele2.indexOf("等级") > -1) {
                if (ele2.indexOf("等级：不易") > -1) {
                  todayData.copdLevel = 1;
                } else if (ele2.indexOf("等级：高") > -1) {
                  todayData.copdLevel = 5;
                } else if (ele2.indexOf("等级：较高") > -1) {
                  todayData.copdLevel = 4;
                } else if (ele2.indexOf("等级：中") > -1) {
                  todayData.copdLevel = 3;
                } else {
                  todayData.copdLevel = 2;
                }
              }
              if (ele2.indexOf("预防建议") > -1) {
                todayData.copdAdvice = ele2;
              }
            });
          }
          if (ele.value.indexOf("中暑") > -1) {
            let strarr = ele.value.split("\n");
            strarr.forEach((ele2) => {
              if (ele2.indexOf("等级") > -1) {
                if (ele2.indexOf("等级：不易") > -1) {
                  todayData.zsLevel = 1;
                } else if (ele2.indexOf("等级：高") > -1) {
                  todayData.zsLevel = 5;
                } else if (ele2.indexOf("等级：较高") > -1) {
                  todayData.zsLevel = 4;
                } else if (ele2.indexOf("等级：中等") > -1) {
                  todayData.zsLevel = 3;
                } else {
                  todayData.zsLevel = 2;
                }
              }
              if (ele2.indexOf("预防建议") > -1) {
                todayData.zsAdvice = ele2;
              }
            });
          }
        });
        this.arr_copd = arr_copd.reverse();
        this.arr_zs = arr_zs.reverse();
        this.arr_copd2 = arr_copd2.reverse();
        this.arr_zs2 = arr_zs2.reverse();
        this.todayData = todayData;
        this.tomorrowData = tomorrowData;
        this.initLine1();
        this.initLine2();
      }
      return
      getJson().then((res) => {
        if (res && res.length > 0) {
          res.forEach((ele, index) => {
            let arr = [];
            if (ele.data && ele.data.length === 4) {
              arr = [ele.data[2], ele.data[3]];
            } else {
              arr = [ele.data[0], ele.data[1]];
            }
            arr.forEach((ele2) => {
              if (ele2.value.indexOf("COPD") > -1) {
                let strarr = ele2.value.split("\n");
                let level = "";
                strarr.forEach((ele3) => {
                  if (ele3.indexOf("等级") > -1) {
                    if (ele3.indexOf("等级：不易") > -1) {
                      level = 1;
                    } else if (ele3.indexOf("等级：高") > -1) {
                      level = 5;
                    } else if (ele3.indexOf("等级：较高") > -1) {
                      level = 4;
                    } else if (ele3.indexOf("等级：中") > -1) {
                      level = 3;
                    } else {
                      level = 2;
                    }
                  }
                });
                arr_copd.push(ele.date);
                arr_copd2.push(level);
              }
              if (ele2.value.indexOf("中暑") > -1) {
                let strarr = ele2.value.split("\n");
                let level = "";
                strarr.forEach((ele3) => {
                  if (ele3.indexOf("等级") > -1) {
                    if (ele3.indexOf("等级：不易") > -1) {
                      level = 1;
                    } else if (ele3.indexOf("等级：高") > -1) {
                      level = 5;
                    } else if (ele3.indexOf("等级：较高") > -1) {
                      level = 4;
                    } else if (ele3.indexOf("等级：中") > -1) {
                      level = 3;
                    } else {
                      level = 2;
                    }
                  }
                });
                arr_zs.push(ele.date);
                arr_zs2.push(level);
              }
            });
          });
          let predata = res[0].data;
          let predataToday = res[1].data;
          let arr = [predata[0], predata[1]];
          let arr2 = [predataToday[0], predataToday[1]];
          arr.forEach((ele) => {
            if (ele.value.indexOf("COPD") > -1) {
              let strarr = ele.value.split("\n");
              strarr.forEach((ele2) => {
                if (ele2.indexOf("等级") > -1) {
                  if (ele2.indexOf("等级：不易") > -1) {
                    tomorrowData.copdLevel = 1;
                  } else if (ele2.indexOf("等级：高") > -1) {
                    tomorrowData.copdLevel = 5;
                  } else if (ele2.indexOf("等级：较高") > -1) {
                    tomorrowData.copdLevel = 4;
                  } else if (ele2.indexOf("等级：中") > -1) {
                    tomorrowData.copdLevel = 3;
                  } else {
                    tomorrowData.copdLevel = 2;
                  }
                }
                if (ele2.indexOf("预防建议") > -1) {
                  tomorrowData.copdAdvice = ele2;
                }
              });
            }
            if (ele.value.indexOf("中暑") > -1) {
              let strarr = ele.value.split("\n");
              strarr.forEach((ele2) => {
                if (ele2.indexOf("等级") > -1) {
                  if (ele2.indexOf("等级：不易") > -1) {
                    tomorrowData.zsLevel = 1;
                  } else if (ele2.indexOf("等级：高") > -1) {
                    tomorrowData.zsLevel = 5;
                  } else if (ele2.indexOf("等级：较高") > -1) {
                    tomorrowData.zsLevel = 4;
                  } else if (ele2.indexOf("等级：中") > -1) {
                    tomorrowData.zsLevel = 3;
                  } else {
                    tomorrowData.zsLevel = 2;
                  }
                }
                if (ele2.indexOf("预防建议") > -1) {
                  tomorrowData.zsAdvice = ele2;
                }
              });
            }
          });
          arr2.forEach((ele) => {
            if (ele.value.indexOf("COPD") > -1) {
              let strarr = ele.value.split("\n");
              strarr.forEach((ele2) => {
                if (ele2.indexOf("等级") > -1) {
                  if (ele2.indexOf("等级：不易") > -1) {
                    todayData.copdLevel = 1;
                  } else if (ele2.indexOf("等级：高") > -1) {
                    todayData.copdLevel = 5;
                  } else if (ele2.indexOf("等级：较高") > -1) {
                    todayData.copdLevel = 4;
                  } else if (ele2.indexOf("等级：中") > -1) {
                    todayData.copdLevel = 3;
                  } else {
                    todayData.copdLevel = 2;
                  }
                }
                if (ele2.indexOf("预防建议") > -1) {
                  todayData.copdAdvice = ele2;
                }
              });
            }
            if (ele.value.indexOf("中暑") > -1) {
              let strarr = ele.value.split("\n");
              strarr.forEach((ele2) => {
                if (ele2.indexOf("等级") > -1) {
                  if (ele2.indexOf("等级：不易") > -1) {
                    todayData.zsLevel = 1;
                  } else if (ele2.indexOf("等级：高") > -1) {
                    todayData.zsLevel = 5;
                  } else if (ele2.indexOf("等级：较高") > -1) {
                    todayData.zsLevel = 4;
                  } else if (ele2.indexOf("等级：中等") > -1) {
                    todayData.zsLevel = 3;
                  } else {
                    todayData.zsLevel = 2;
                  }
                }
                if (ele2.indexOf("预防建议") > -1) {
                  todayData.zsAdvice = ele2;
                }
              });
            }
          });
          this.arr_copd = arr_copd.reverse();
          this.arr_zs = arr_zs.reverse();
          this.arr_copd2 = arr_copd2.reverse();
          this.arr_zs2 = arr_zs2.reverse();
          this.todayData = todayData;
          this.tomorrowData = tomorrowData;
          this.initLine1();
          this.initLine2();
        }
      });
    },
    speedRandom() {
      this.timerspeed = setInterval(() => {
        this.speed = parseInt(Math.random() * (22 - 18) + 18);
        this.initMap2();
      }, 1000);
      this.GLOBAL.timerArraySet.push(this.timerspeed);
    },
    initanchor(isData) {
      let otherNow = this.propsdata.otherNow;
      let option = {
        tooltip: {
          show: false,
          formatter: "{a} <br/>{b} : {c}%",
        },
        grid: {
          top: 0,
        },
        series: [
          {
            name: "Pressure",
            type: "gauge",
            splitNumber: 1,
            min: otherNow.min,
            max: otherNow.max,
            radius: 125,
            startAngle: 210,
            endAngle: -30,
            center: ["50%", "65%"],
            pointer: {
              itemStyle: {
                color: "#158EFF",
              },
            },
            title: {
              show: false,
            },
            progress: {
              show: true,
              itemStyle: {
                color: "#158EFF",
              },
            },
            detail: {
              show: false,
              valueAnimation: false,
              formatter: "{value}",
            },
            anchor: {
              show: true,
              itemStyle: {
                size: 20,
              },
            },
            itemStyle: {
              borderWidth: 20,
            },
            data: [
              {
                value: otherNow.use,
                name: "已使用",
              },
            ],
          },
        ],
      };
      // 内存泄漏 无dom 不执行
      if (!this.chartsanchor) {
        this.chartsanchor = echarts.init(this.$refs.anchor, null, {
          width: 250,
          height: 200,
        });
      }
      this.chartsanchor.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.chartsanchor.setOption(option);
    },
    initMap() {
      let _this = this;
      if (!this.mapChart) {
        this.mapChart = echarts.init(this.$refs.chinamap, null, {
          width: this.GLOBAL.relPx(300),
          height: this.GLOBAL.relPx(340),
        });
        this.GLOBAL.echartsDomArray.push(this.mapChart);
      }
      let center = [121.47898, 31.23942];
      let zoom = 13;
      let fromList = [
        {
          name: "上海气象局专属资源池",
          value: [121.4434, 31.19456],
          xname: "",
        },
      ];
      let toList = [
        // {
        //   "name": "青浦",
        //   "value": [121.13055, 31.15545],
        //   "xname": ""
        // },
        {
          name: "临港资源池",
          value: [121.92735, 30.8594],
          xname: "",
        },
        // {
        //   "name": "周家渡资源池",
        //   "value": [121.68043, 31.27513],
        //   "xname": ""
        // }
      ];
      let list = [
        {
          fromName: "上海气象局专属资源池",
          toName: "临港资源池",
          coords: [
            [121.4434, 31.19456],
            [121.92735, 30.8594],
          ],
        },
      ];
      let option = {
        backgroundColor: "",
        title: {
          left: "center",
          textStyle: {
            color: "#fff",
          },
        },
        geo: {
          map: "china",
          aspectScale: 0.72, //长宽比
          center: center,
          zoom: zoom,
          roam: false,
          label: {
            show: false,
          },
          emphasis: {
            disabled: true,
            itemStyle: {
              normal: {
                areaColor: {
                  type: "radial",
                  x: 0.3,
                  y: 0.3,
                  r: 0.8,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#464646", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#464646", // 100% 处的颜色
                    },
                  ],
                  globalCoord: true, // 缺省为 false
                },
                shadowColor: "#464646",
                shadowOffsetX: 5,
                shadowOffsetY: 5,
              },
            },
          },
          itemStyle: {
            normal: {
              areaColor: {
                type: "radial",
                x: 0.3,
                y: 0.3,
                r: 0.8,
                colorStops: [
                  {
                    offset: 0,
                    color: "#464646", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#464646", // 100% 处的颜色
                  },
                ],
                globalCoord: true, // 缺省为 false
              },
              shadowColor: "#464646",
              shadowOffsetX: 5,
              shadowOffsetY: 5,
            },
          },
          regions: [
            {
              name: "南海诸岛",
              itemStyle: {
                areaColor: "rgba(0, 10, 52, 1)",
                borderColor: "rgba(0, 10, 52, 1)",
                normal: {
                  opacity: 0,
                  label: {
                    show: false,
                    color: "#009cc9",
                  },
                },
              },
            },
          ],
        },
        series: [
          {
            name: "上海",
            type: "lines",
            zlevel: 10,
            symbol: ["none"],
            symbolSize: 10,
            effect: {
              show: true,
              period: 4, //箭头指向速度，值越小速度越快
              trailLength: 0.1, //特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: "arrow", //箭头图标
              symbolSize: 8, //图标大小
              opacity: 0.5,
            },
            lineStyle: {
              normal: {
                color: "rgba(33, 213, 113, 1)",
                width: 1,
                opacity: 0.2,
                type: "solid",
                curveness: -0.2,
              },
            },
            data: list,
            zlevel: 2,
          },
          {
            type: "effectScatter",
            coordinateSystem: "geo",
            //要有对应的经纬度才显示，先经度再维度
            data: fromList,
            showEffectOn: "render",
            symbolSize(value, params) {
              return 10;
              // return params.data.warning
            },
            rippleEffect: {
              scale: 2, // 波纹的最大缩放比例
              brushType: "stroke",
            },
            itemStyle: {
              normal: {
                color: "rgba(33, 213, 113, 1)",
              },
            },
            label: {
              show: true,
              formatter: "{b}",
              fontWeight: "bold",
              fontSize: 12,
              color: "#fff",
              textBorderColor: "#000",
              position: "top",
              textBorderWidth: 3,
              offset: [0, -10],
            },
            zlevel: 10,
          },
          {
            type: "effectScatter",
            coordinateSystem: "geo",
            //要有对应的经纬度才显示，先经度再维度
            data: toList,
            showEffectOn: "render",
            symbolSize(value, params) {
              return 9;
              // return params.data.warning
            },
            rippleEffect: {
              scale: 3, // 波纹的最大缩放比例
              brushType: "stroke",
            },
            itemStyle: {
              normal: {
                color: "rgba(21, 142, 255, 1)",
              },
            },
            label: {
              show: true,
              formatter: "{b}",
              fontWeight: "bold",
              fontSize: 12,
              color: "#fff",
              textBorderColor: "#000",
              position: "bottom",
              textBorderWidth: 3,
              offset: [0, 10],
            },
            zlevel: 3,
          },
          {
            type: "map",
            roam: false,
            center: center,
            zoom: zoom,
            label: {
              show: false,
              textStyle: {
                color: "#ccc",
              },
            },
            // selectedMode: false,
            selectedMode: "multiple",
            emphasis: {
              disabled: true,
            },
            select: {
              disabled: true,
              itemStyle: {
                borderColor: "#787879",
                borderWidth: 1,
                areaColor: "#334053",
              },
              label: {
                color: "#828282",
              },
            },

            itemStyle: {
              borderColor: "#787879",
              borderWidth: 1,
              areaColor: "#333",
            },

            //     roam: false,
            map: "china", //使用
            // data: this.difficultData //热力图数据   不同区域 不同的底色
          },
        ],
      };
      this.mapChart.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.mapChart.setOption(option);
    },
    initMap2() {
      let _this = this;
      if (!this.mapChart2) {
        this.mapChart2 = echarts.init(this.$refs.chinamap2, null, {
          width: this.GLOBAL.relPx(226),
          height: this.GLOBAL.relPx(260),
        });
        this.GLOBAL.echartsDomArray.push(this.mapChart2);
      }
      let center = [121.47898, 31.25942];
      let zoom = 13;
      let colordata = [
        { nama: "黄浦区", value: 1 },
        { nama: "浦东新区", value: 2 },
        { nama: "徐汇区", value: 3 },
      ];
      let regions = [
        {
          name: "黄浦区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
        {
          name: "徐汇区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
        {
          name: "长宁区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
        {
          name: "静安区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
        {
          name: "普陀区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
        {
          name: "虹口区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
        {
          name: "杨浦区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
        {
          name: "闵行区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
        {
          name: "宝山区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
        {
          name: "嘉定区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
        {
          name: "浦东新区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
        {
          name: "金山区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
        {
          name: "松江区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
        {
          name: "青浦区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
        {
          name: "奉贤区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
        {
          name: "崇明区",
          itemStyle: { normal: { areaColor: "rgba(239, 151, 41, 1)" } },
        },
      ];
      let index = parseInt(Math.random() * (15 - 0) + 0);
      regions[index].itemStyle.normal.areaColor = "rgba(250, 14, 28, 1)";
      let option = {
        backgroundColor: "",
        title: {
          left: "center",
          textStyle: {
            color: "#fff",
          },
        },
        visualMap: {
          show: false,
          min: 0,
          max: 200,
          left: "left",
          top: "bottom",
          calculable: true,
          seriesIndex: [1],
          pieces: [
            { lte: 1, label: "低", color: "rgba(18, 168, 128, 1)" },
            { lte: 2, gt: 1, label: "轻微", color: "rgba(28, 161, 227, 1)" },
            { lte: 3, gt: 2, label: "中等", color: "rgba(221, 210, 23, 1)" },
            { lte: 4, gt: 3, label: "较高", color: "rgba(239, 151, 41, 1)" },
            { lte: 5, gt: 4, label: "高", color: "rgba(250, 14, 28, 1)" },
          ],
        },
        geo: {
          map: "china",
          aspectScale: 0.72, //长宽比
          center: center,
          zoom: zoom,
          roam: false,
          label: {
            show: false,
          },
          data: colordata,
          emphasis: {
            disabled: true,
            itemStyle: {
              normal: {
                areaColor: {
                  type: "radial",
                  x: 0.3,
                  y: 0.3,
                  r: 0.8,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#464646", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#464646", // 100% 处的颜色
                    },
                  ],
                  globalCoord: true, // 缺省为 false
                },
                shadowColor: "#464646",
                shadowOffsetX: 5,
                shadowOffsetY: 5,
              },
            },
          },
          itemStyle: {
            borderColor: "#787879",
            borderWidth: 1,
            areaColor: "#333",
          },
          regions: regions,
        },
        series: [
          // {
          //   type: 'map',
          //   roam: false,
          //   center: center,
          //   zoom: zoom,
          //   label: {
          //     show: false,
          //     textStyle: {
          //       color: '#ccc'
          //     },
          //   },
          //   // selectedMode: false,
          //   selectedMode: 'multiple',
          //   emphasis: {
          //     disabled: true
          //   },
          //   select: {
          //     disabled: true,
          //     itemStyle: {
          //       borderColor: '#787879',
          //       borderWidth: 1,
          //       areaColor: '#334053'
          //     },
          //     label: {
          //       color: '#828282'
          //     },
          //   },
          //   data: colordata,
          //   itemStyle: {
          //     borderColor: '#787879',
          //     borderWidth: 1,
          //     areaColor: '#333'
          //   },
          //   //     roam: false,
          //   map: 'china', //使用
          //   // data: this.difficultData //热力图数据   不同区域 不同的底色
          // },
        ],
      };
      this.mapChart2.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.mapChart2.setOption(option);
    },
    initMap3() {
      let _this = this;
      if (!this.mapChart3) {
        this.mapChart3 = echarts.init(this.$refs.chinamap3, null, {
          width: this.GLOBAL.relPx(226),
          height: this.GLOBAL.relPx(260),
        });
        this.GLOBAL.echartsDomArray.push(this.mapChart3);
      }
      let center = [121.47898, 31.25942];
      let zoom = 13;
      let colordata = [
        { nama: "黄浦区", value: 1 },
        { nama: "浦东新区", value: 2 },
        { nama: "徐汇区", value: 3 },
      ];
      let regions = [
        {
          name: "黄浦区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
        {
          name: "徐汇区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
        {
          name: "长宁区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
        {
          name: "静安区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
        {
          name: "普陀区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
        {
          name: "虹口区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
        {
          name: "杨浦区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
        {
          name: "闵行区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
        {
          name: "宝山区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
        {
          name: "嘉定区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
        {
          name: "浦东新区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
        {
          name: "金山区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
        {
          name: "松江区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
        {
          name: "青浦区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
        {
          name: "奉贤区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
        {
          name: "崇明区",
          itemStyle: { normal: { areaColor: "rgba(18, 168, 128, 1)" } },
        },
      ];
      // let index = parseInt(Math.random() * (15 - 0) + 0)
      // regions[index].itemStyle.normal.areaColor = 'rgba(28, 161, 227, 1)'
      let option = {
        backgroundColor: "",
        title: {
          left: "center",
          textStyle: {
            color: "#fff",
          },
        },
        visualMap: {
          show: false,
          min: 0,
          max: 200,
          left: "left",
          top: "bottom",
          calculable: true,
          seriesIndex: [1],
          pieces: [
            { lte: 1, label: "低", color: "rgba(18, 168, 128, 1)" },
            { lte: 2, gt: 1, label: "轻微", color: "rgba(28, 161, 227, 1)" },
            { lte: 3, gt: 2, label: "中等", color: "rgba(221, 210, 23, 1)" },
            { lte: 4, gt: 3, label: "较高", color: "rgba(239, 151, 41, 1)" },
            { lte: 5, gt: 4, label: "高", color: "rgba(250, 14, 28, 1)" },
          ],
        },
        geo: {
          map: "china",
          aspectScale: 0.72, //长宽比
          center: center,
          zoom: zoom,
          roam: false,
          label: {
            show: false,
          },
          data: colordata,
          emphasis: {
            disabled: true,
            itemStyle: {
              normal: {
                areaColor: {
                  type: "radial",
                  x: 0.3,
                  y: 0.3,
                  r: 0.8,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#464646", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#464646", // 100% 处的颜色
                    },
                  ],
                  globalCoord: true, // 缺省为 false
                },
                shadowColor: "#464646",
                shadowOffsetX: 5,
                shadowOffsetY: 5,
              },
            },
          },
          itemStyle: {
            borderColor: "#787879",
            borderWidth: 1,
            areaColor: "#333",
          },
          regions: regions,
        },
        series: [
          // {
          //   type: 'map',
          //   roam: false,
          //   center: center,
          //   zoom: zoom,
          //   label: {
          //     show: false,
          //     textStyle: {
          //       color: '#ccc'
          //     },
          //   },
          //   // selectedMode: false,
          //   selectedMode: 'multiple',
          //   emphasis: {
          //     disabled: true
          //   },
          //   select: {
          //     disabled: true,
          //     itemStyle: {
          //       borderColor: '#787879',
          //       borderWidth: 1,
          //       areaColor: '#334053'
          //     },
          //     label: {
          //       color: '#828282'
          //     },
          //   },
          //   data: colordata,
          //   itemStyle: {
          //     borderColor: '#787879',
          //     borderWidth: 1,
          //     areaColor: '#333'
          //   },
          //   //     roam: false,
          //   map: 'china', //使用
          //   // data: this.difficultData //热力图数据   不同区域 不同的底色
          // },
        ],
      };
      this.mapChart3.clear();
      // 使用刚指定的配置项和数据显示图表。
      this.mapChart3.setOption(option);
    },
    init() {
      let settimer1 = setTimeout(() => {
        this.wordAni1 = true;
        this.wordAni3 = true;
      }, 500);
      let settimer2 = setTimeout(() => {
        this.wordAni2 = true;
        this.wordAni4 = true;
      }, 1000);
      let settimer3 = setTimeout(() => {
        this.coreAni1 = true;
      }, 1500);
      let settimer4 = setTimeout(() => {
        this.coreAni2 = true;
        this.lineAniFun();
        this.initMap();
      }, 2000);
      let settimer5 = setTimeout(() => {
        this.coreAni3 = true;
        // this.initanchor()
      }, 2500);
      let settimer6 = setTimeout(() => { }, 3000);
      let settimer7 = setTimeout(() => {
        this.showimgAni = true;
        this.timeCoreAni = true;
      }, 3500);
      let settimer8 = setTimeout(() => {
        this.downtextAni1 = true;
        this.downtextAni2 = true;
      }, 4000);
      let settimer9 = setTimeout(() => { }, 4500);
      let settimer10 = setTimeout(() => { }, 5000);
      let settimer11 = setTimeout(() => {
        this.iconAni1 = false;
        this.iconAni2 = true;
        this.word1 = "预测结果发布";
        this.showPage2 = true;
        this.initDataJson();
        this.$emit("stepEnd", 3);
      }, 5500);
      let settimer12 = setTimeout(() => {
        this.iconAni3 = false;
        this.iconAni4 = true;
      }, 6000);

      this.$once("hook:beforeDestroy", () => {
        clearTimeout(settimer1);
        settimer1 = null;
        clearTimeout(settimer2);
        settimer2 = null;
        clearTimeout(settimer3);
        settimer3 = null;
        clearTimeout(settimer4);
        settimer4 = null;
        clearTimeout(settimer5);
        settimer5 = null;
        clearTimeout(settimer6);
        settimer6 = null;
        clearTimeout(settimer7);
        settimer7 = null;
        clearTimeout(settimer8);
        settimer8 = null;
        clearTimeout(settimer9);
        settimer9 = null;
        clearTimeout(settimer10);
        settimer10 = null;
        clearTimeout(settimer11);
        settimer11 = null;
        clearTimeout(settimer12);
        settimer12 = null;
      });
    },
    addTileAuto2(size) {
      let _this = this;
      let timer = setInterval(() => {
        _this.addTile2(size);
        size -= 1;
        if (size < 0) {
          clearInterval(timer);
          clearInterval(this.timer);
          this.numRandom2();
        }
      }, 100);
      _this.GLOBAL.timerArraySet.push(timer);
    },
    addTile2(size) {
      this.drawImageToCanvas2().then((ctx) => {
        const mosaic = new Mosaic(ctx, {
          tileWidth: size <= 0 ? 10 : size,
          tileHeight: size <= 0 ? 10 : size,
        });
        if (size <= 0) {
          mosaic.eraseAllTiles();
          return false;
        }
        mosaic.drawAllTiles();
      });
    },
    initMosaic2() {
      this.drawImageToCanvas2().then((ctx) => {
        const mosaic = new Mosaic(ctx, {
          tileWidth: 40,
          tileHeight: 40,
        });
        mosaic.drawAllTiles(40);
      });
    },
    drawImageToCanvas2() {
      const canvas = document.querySelector("#canvas2");
      const ctx = canvas.getContext("2d");
      let imageUrl;
      if (this.imgSrc2) {
        imageUrl = this.imgSrc2;
      }
      return new Promise((resolve) => {
        const image = new Image();
        image.crossOrigin = "Annoymous";
        image.onload = function () {
          canvas.width = 840;
          canvas.height = 366;
          // canvas.width = image.width
          // canvas.height = image.height
          // ctx.drawImage(this, 0, 0, image.width, image.height)
          ctx.drawImage(this, 0, 0, 840, 366);
          resolve(ctx);
        };
        image.src = imageUrl;
      });
    },
    numRandom() {
      this.timer = setInterval(() => {
        this.num1 = parseInt(Math.random() * (85 - 75) + 75);
        this.num2 = parseInt(Math.random() * (80 - 70) + 70);
        this.num3 = parseInt(Math.random() * (75 - 65) + 65);
        this.num4 = parseInt(Math.random() * (75 - 70) + 70);
      }, 800);
      this.GLOBAL.timerArraySet.push(this.timer);
      this.timer2 = setInterval(() => {
        this.num5 = parseInt(Math.random() * (95 - 85) + 85);
        this.num6 = parseInt(Math.random() * (80 - 70) + 70);
        this.num7 = parseInt(Math.random() * (65 - 55) + 55);
        this.num8 = parseInt(Math.random() * (65 - 55) + 55);
      }, 800);
      this.GLOBAL.timerArraySet.push(this.timer2);
    },
    numRandom2() {
      let timer = setInterval(() => {
        this.num1 = parseInt(Math.random() * (35 - 25) + 25);
        this.num2 = parseInt(Math.random() * (30 - 20) + 20);
        this.num3 = parseInt(Math.random() * (25 - 15) + 15);
        this.num4 = parseInt(Math.random() * (25 - 20) + 20);
      }, 1500);
      this.GLOBAL.timerArraySet.push(timer);
    },
    numRandom3() {
      let timer2 = setInterval(() => {
        this.num5 = parseInt(Math.random() * (45 - 35) + 35);
        this.num6 = parseInt(Math.random() * (30 - 20) + 20);
        this.num7 = parseInt(Math.random() * (15 - 5) + 5);
        this.num8 = parseInt(Math.random() * (15 - 5) + 5);
      }, 1500);
      this.GLOBAL.timerArraySet.push(timer2);
    },
    lineAniFun() {
      this.timerpercent = setInterval(() => {
        this.percent += 1;
        if (this.percent >= 100) {
          clearInterval(this.timerpercent);
        }
      }, 30);
    },
    lineAniFun2() {
      this.timerpercent2 = setInterval(() => {
        this.percent2 += 1;
        if (this.percent2 >= 100) {
          clearInterval(this.timerpercent2);
        }
      }, 30);
    },
    lineAniFun3() {
      this.timerpercent3 = setInterval(() => {
        this.time10 -= 0.1;
        if (this.time10 <= 0) {
          this.time10 = 0;
          clearInterval(this.timerpercent3);
          this.showVideo = true;
          clearInterval(this.timer2);
          this.numRandom3();
        }
      }, 100);
    },
    endFun(index) {
      // this.$emit('endFun', index)
    },
    resetFun() {
      if (this.pointAnitimer1) {
        clearInterval(this.pointAnitimer1);
      }
      if (this.pointAnitimer2) {
        clearInterval(this.pointAnitimer2);
      }
      if (this.timerpercent) {
        clearInterval(this.timerpercent);
      }
      if (this.timerpercent2) {
        clearInterval(this.timerpercent2);
      }
      if (this.timerpercent3) {
        clearInterval(this.timerpercent3);
      }
    },
  },
  beforeDestroy() {
    this.resetFun();
  },
};
</script>

<style lang="scss">
.pageWork2_3 {
  .fourCore {
    width: 577px;
    height: 286px;
    margin-top: 10px;

    .core {
      background-image: url("~@/assets/images/1920/work2/item.png");
      background-size: cover;
      width: 577px;
      height: 247px;
    }

    .word {
      font-size: 18px;
      text-align: center;
    }

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }

    .son {
      width: 50%;
      height: 50%;
      text-align: center;

      .t1 {
        font-size: 34px;
        line-height: 34px;
        padding-top: 30px;

        span {
          font-size: 16px;
        }
      }

      .t2 {
        font-size: 16px;
      }
    }
  }

  .downtitle {
    text-align: center;
  }

  .centerCore {
    opacity: 0;

    &.ani {
      animation: imgOpacity 0.5s linear 1 forwards;
    }
  }

  .rightBox {
    padding-right: 10px;
    width: 1210px;

    .core {
      background: rgba($color: #26262a, $alpha: 0.15);
      width: 580px;
      height: 790px;

      .item {
        border-radius: 4px;
        box-sizing: border-box;
        padding: 24px 20px;
        // opacity: 0;
        margin-bottom: 30px;
        padding-bottom: 1px;
        height: 380px;

        .son {
          margin-bottom: 30px;

          // opacity: 0;
          .imgcore {
            width: 427px;
            height: 241px;
            margin-top: 20px;
            margin-left: 30px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .ict {
            .icon {
              width: 21px;
              height: 21px;
              margin-right: 10px;

              img {
                width: 100%;
                height: 100%;
              }
            }

            .text {
              font-size: 16px;
              line-height: 21px;

              &.on {
                padding-left: 30px;
              }
            }

            &.on {
              padding-left: 30px;
            }
          }

          .pointList {
            margin-top: 20px;

            .point {
              width: 100%;

              .pointson {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                background-color: rgba($color: #7fb41c, $alpha: 0.4);
                margin: 10px 7px;

                &.on {
                  background-color: rgba($color: #3174f3, $alpha: 1);
                }

                &.s2 {
                  &.on {
                    background-color: rgba($color: #00c2fc, $alpha: 1);
                  }
                }
              }
            }
          }

          .textBox {
            margin-top: 20px;
            font-size: 18px;
            color: #999999;
            line-height: 16px;
          }

          .itemImgList {
            margin-top: 20px;
            padding: 0 60px;

            .imgSon {
              .img {
                width: 68px;
                height: 49px;

                img {
                  width: 100%;
                  height: 100%;
                }
              }

              .dp {
                width: 10px;
                height: 10px;
                background: #666666;
                border-radius: 50%;
                margin: 15px auto 0 auto;

                &.on {
                  background: #21d571;
                }
              }
            }
          }

          &.ani {
            animation: imgOpacity 1s linear 1 forwards;
          }
        }

        &.ani {
          animation: imgOpacity 0.5s linear 1 forwards;
        }
      }
    }
  }

  .coreItem {
    width: 578px;
    // height: 793px;
    background: rgba($color: #26262a, $alpha: 0.15);
    border-radius: 4px;
    opacity: 0;

    .title {
      font-size: 18px;
      padding: 15px;
    }

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .tsCore {
    margin-top: 10px;
    // height: 270px;
    font-size: 16px;
    text-align: center;
    position: relative;
    opacity: 0;

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }

    .top {
      .img {
        position: relative;
        margin: 0 auto;
        width: 97px;
        height: 95px;
        box-sizing: border-box;
        margin-top: 10px;

        .img1 {
          width: 97px;
          height: 95px;
          position: absolute;
          top: 0;
          left: 0;
        }

        .img2 {
          width: 34px;
          height: 29px;
          margin: 0 auto;
          display: block;
        }
      }

      .text {
        margin-top: 10px;
      }
    }

    .line {
      position: absolute;
      z-index: 0;
      width: 140px;
      height: 0;
      background-size: cover;
      top: 95px;

      &.l1 {
        background-image: url("~@/assets/images/1920/work2/L.png");
        left: 140px;
      }

      &.l2 {
        background-image: url("~@/assets/images/1920/work2/R.png");
        right: 140px;
      }

      &.ani {
        animation: heightAni3 0.5s linear 1 forwards;
      }
    }

    .down {
      margin-top: 17px;
      text-align: center;

      .son {
        position: relative;

        .text {
          position: absolute;
          top: 80px;
          left: -10px;
          width: 108px;

          p {
            margin: 0;
          }

          &.on {
            right: 0;
          }
        }
      }

      .cc {
        margin: 35px 5px 20px 5px;

        &.ani {
          animation: imgOpacity2 2s linear infinite forwards;
        }

        .text {
          width: 100%;
        }
      }
    }
  }

  .lineword {
    text-align: center;
  }

  .lineCore {
    margin: 15px 0;
    margin-left: 20px;

    .text {
      font-size: 16px;
      line-height: 16px;
    }

    .line {
      width: 430px;
      height: 10px;
      background: rgba(17, 17, 17, 1);
      border-radius: 5px;
      margin: 0 auto;
      margin-top: 3px;
      margin-left: 10px;

      .inner {
        background-image: url("~@/assets/images/page4/step4/percent.png");
        width: 0%;
        height: 10px;
        border-radius: 5px;
      }

      &.on {
        width: 520px;
      }
    }

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .showimg {
    width: 576px;
    height: 331px;
    margin: 0 auto;
    margin-top: 30px;
    opacity: 0;

    canvas {
      width: 100%;
      height: 100%;
    }

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .downtext {
    font-size: 18px;
    text-align: center;
    margin-top: 14px;
    opacity: 0;

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .timeCore {
    height: 331px;
    margin-top: 30px;
    padding-top: 80px;
    box-sizing: border-box;
    background-color: #2a2a2d;
    opacity: 0;

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .videoCore {
    height: 331px;
    margin-top: 30px;
    box-sizing: border-box;

    // background-color: #2A2A2D;
    video {
      width: 576px;
      height: 331px;
    }

    &.ani {
      animation: imgOpacity 1s linear 1 forwards;
    }
  }

  .circleBox {
    width: 146px;
    height: 146px;
    position: relative;
    margin: 0 auto;
    // margin-top: 50px;
    // opacity: 0;

    &.ani {
      animation: imgOpacity 0.5s linear 1 forwards;
    }

    .center_text {
      display: none;
    }

    .per {
      font-size: 50px;
      text-align: center;
      width: 100%;
      line-height: 146px;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 9;
      color: #fff;

      span {
        font-size: 24px;
      }
    }

    .text {
      font-size: 20px;
      text-align: center;
      margin-top: 20px;
    }
  }

  .rightCore {
    .rightitem {
      width: 576px;
      height: 380px;
      background: rgba($color: #26262a, $alpha: 0.2);
      border-radius: 4px;
      margin-bottom: 30px;
      position: relative;
      opacity: 0;

      .time {
        padding-top: 15px;
        padding-right: 15px;
        color: rgba(136, 136, 136, 1);
        font-size: 12px;
      }

      &.ani {
        animation: imgOpacity 1s linear 1 forwards;
      }

      .textposi {
        font-size: 16px;
        color: #17e372;
        position: absolute;
        bottom: 122px;
        right: 100px;
        z-index: 1;
      }

      .title {
        padding: 13px;
      }

      .map {
        margin: 0 auto;
        width: 300px;
      }

      .anchorBox {
        .anchor {
          width: 250px;
          margin: 0 auto;
        }

        .anchorcore {
          text-align: center;
          transform: translateY(-30px);

          .text1 {
            font-size: 16px;
          }

          .text2 {
            font-size: 24px;
          }
        }
      }

      .downtextCore {
        font-size: 16px;
        text-align: center;
        padding: 0 40px;

        .son {
          .t2 {
            margin-top: 10px;
          }
        }
      }
    }

    &.hide {
      display: none;
    }
  }

  .fiveColor {
    padding-top: 15px;
    padding-right: 15px;

    .item {
      width: 27px;
      height: 13px;
      font-size: 11px;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      line-height: 13px;
      margin-left: 5px;

      &.c1 {
        background-color: rgba(18, 168, 128, 1);
      }

      &.c2 {
        background-color: rgba(28, 161, 227, 1);
      }

      &.c3 {
        background-color: rgba(221, 210, 23, 1);
      }

      &.c4 {
        background-color: rgba(239, 151, 41, 1);
      }

      &.c5 {
        background-color: rgba(250, 14, 28, 1);
      }
    }
  }

  .newcore {
    .lecore {
      // background-image: url('~@/assets/images/1920/work3/gif1.gif');
      background-size: cover;
      width: 260px;
      height: 260px;
      box-sizing: border-box;
      margin: 10px 20px;
      position: relative;

      video {
        width: 260px;
        height: 260px;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
      }

      .posicore {
        position: relative;
        width: 100%;
        height: 100%;
        animation: rotateAni 8s linear infinite forwards;
        z-index: 2;

        .item {
          position: absolute;

          &.p1 {
            top: 105px;
            left: 0;
          }

          &.p2 {
            top: 30px;
            left: 25px;
          }

          &.p3 {
            top: 0;
            left: 96px;
          }

          &.p4 {
            top: 30px;
            right: 25px;
          }

          &.p5 {
            top: 105px;
            left: 192px;
          }

          &.p6 {
            top: 170px;
            right: 25px;
          }

          &.p7 {
            top: 192px;
            left: 96px;
          }
        }
      }
    }

    .lecore2 {
      background-size: cover;
      width: 260px;
      height: 260px;
      box-sizing: border-box;
      margin: 10px 20px;
      position: relative;

      video {
        width: 260px;
        height: 260px;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
      }

      .word {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.5);
        position: absolute;
        z-index: 2;

        &.p1 {
          top: 100px;
          left: 50px;
          animation: wordAni 2.5s linear infinite forwards;
        }

        &.p2 {
          top: 130px;
          left: 100px;
          animation: wordAni 5s linear infinite forwards;
        }

        &.p3 {
          top: 100px;
          left: 150px;
          animation: wordAni 4s linear infinite forwards;
        }

        &.p4 {
          top: 180px;
          left: 150px;
          animation: wordAni 3s linear infinite forwards;
        }
      }
    }
  }

  .levelCore {
    padding: 0 13px;

    .item {
      width: 266px;
      height: 124px;
      background: rgba($color: #414350, $alpha: 0.1);
      border-radius: 6px;
      box-sizing: border-box;
      padding: 10px;

      .levelicon {
        margin-top: 10px;

        .word {
          font-size: 14px;
          font-weight: bold;
          text-align: center;
          padding-top: 15px;
        }

        .icon {
          width: 65px;
          height: 56px;
          background-size: cover;

          &.icon1 {
            background-image: url("~@/assets/images/1920/work3/icon-level-1.png");
          }

          &.icon2 {
            background-image: url("~@/assets/images/1920/work3/icon-level-2.png");
          }

          &.icon3 {
            background-image: url("~@/assets/images/1920/work3/icon-level-3.png");
          }

          &.icon4 {
            background-image: url("~@/assets/images/1920/work3/icon-level-4.png");
          }

          &.icon5 {
            background-image: url("~@/assets/images/1920/work3/icon-level-5.png");
          }
        }
      }

      .info {
        width: 160px;
        font-size: 12px;
        color: rgba(136, 136, 136, 1);
      }
    }
  }

  @keyframes heightAni3 {
    0% {
      height: 0;
    }

    100% {
      height: 76px;
    }
  }
}

@keyframes imgOpacity {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes imgOpacity2 {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0.2;
  }
}

@keyframes rotateAni {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes wordAni {
  0% {
    transform: scale(1);
    color: rgba(255, 255, 255, 0.5);
  }

  25% {
    transform: scale(1.5);
    color: rgba(255, 255, 255, 1);
  }

  50% {
    transform: scale(1);
    color: rgba(255, 255, 255, 0.5);
  }

  75% {
    transform: scale(0.5);
    color: rgba(255, 255, 255, 0.25);
  }

  100% {
    transform: scale(1);
    color: rgba(255, 255, 255, 0.5);
  }
}
</style>

<style lang="scss">
.pageWork2_3 {
  .el-carousel__container {
    width: 427px;
    height: 241px;
  }
}
</style>
