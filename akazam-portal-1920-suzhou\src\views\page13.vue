<template>
  <div class="pageCommon page13">
    <!-- 头部-s -->
    <header-common :icon="6" :name="'作业应用-健康气象'"></header-common>
    <!-- 头部-e -->
    <!-- <div class="openMenu" @click="showMenu = !showMenu"></div> -->
    <div class="menu flex-box-between newMenu" v-show="showMenu">
      <div class="item" :class="showItem ? 'ani' : ''" @click="goWork(1)">
        <img class="img1" src="~@/assets/images/page4/icon-1_on.png" alt="">
        <img class="img2" src="~@/assets/images/page4/icon-1.png" alt="">
        <p>人工智能</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''" @click="goWork(2)">
        <img class="img1" src="~@/assets/images/page4/icon-2_on.png" alt="">
        <img class="img2" src="~@/assets/images/page4/icon-2.png" alt="">
        <p>人脸识别</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''"><img src="~@/assets/images/page4/icon-3.png" alt="">
        <p>自动驾驶</p>
      </div>
      <div class="item" :class="[showItem ? 'ani' : '']" @click="goWork(4)">
        <img class="img1" src="~@/assets/images/page4/icon-4_on.png" alt="">
        <img class="img2" src="~@/assets/images/page4/icon-4.png" alt="">
        <p>云渲染</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''"><img src="~@/assets/images/page4/icon-5.png" alt="">
        <p>工业仿真</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''"><img src="~@/assets/images/page4/icon-6.png" alt="">
        <p>预测分析</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''"><img src="~@/assets/images/page4/icon-7.png" alt="">
        <p>智慧城市</p>
      </div>
      <div class="item" :class="[showItem ? 'ani' : '']" @click="goWork(8)">
        <img class="img1" src="~@/assets/images/page4/icon-8_on.png" alt="">
        <img class="img2" src="~@/assets/images/page4/icon-8.png" alt="">
        <p>图像识别</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''" @click="goWork(9)">
        <img class="img1" src="~@/assets/images/page4/icon-9_on.png" alt="">
        <img class="img2" src="~@/assets/images/page4/icon-9.png" alt="">
        <p>文生图</p>
      </div>
    </div>
    <div class="loading flex-box-center" v-if="step == 0">
      <div class="icon"><i class="el-icon-loading"></i></div>
      <div class="text">等待作业指令中</div>
    </div>
    <div class="akazam" :class="isfull ? 'full' : ''">
      <div class="full" @click.stop="fullFun"></div>
      <div class="akazamWeb" v-if="step === 0">
        <div class="top flex-box">
          <div class="item on">快捷向导</div>
          <div class="item">概览</div>
        </div>
        <div class="imgnav flex-box" v-show="!showWeather">
          <div class="left">
            <div class="item">
              <div class="son">
                <div class="icon"><img src="~@/assets/images/1920/work2/nav1.png" alt=""></div>
                <div class="text">图像识别</div>
              </div>
            </div>
          </div>
          <div class="right flex-box-between">
            <div class="item">
              <div class="son">
                <div class="icon"><img src="~@/assets/images/1920/work2/nav2.png" alt=""></div>
                <div class="text">人脸识别</div>
              </div>
            </div>
            <div class="item">
              <div class="son">
                <div class="icon"><img src="~@/assets/images/1920/work2/nav3.png" alt=""></div>
                <div class="text">云渲染</div>
              </div>
            </div>
            <div class="item" @click="endFunBefore()">
              <div class="son">
                <div class="icon"><img src="~@/assets/images/1920/work2/nav9.png" alt=""></div>
                <div class="text">健康气象</div>
              </div>
            </div>
            <div class="item">
              <div class="son">
                <div class="icon"><img src="~@/assets/images/1920/work2/nav5.png" alt=""></div>
                <div class="text">AI知识库</div>
              </div>
            </div>
            <div class="item">
              <div class="son">
                <div class="icon"><img src="~@/assets/images/1920/work2/nav6.png" alt=""></div>
                <div class="text">文本翻译</div>
              </div>
            </div>
            <div class="item">
              <div class="son">
                <div class="icon"><img src="~@/assets/images/1920/work2/nav7.png" alt=""></div>
                <div class="text">图像生成</div>
              </div>
            </div>
            <div class="item">
              <div class="son">
                <div class="icon"><img src="~@/assets/images/1920/work2/nav8.png" alt=""></div>
                <div class="text">智能问答</div>
              </div>
            </div>
            <div class="item">
              <div class="arrow"><img src="~@/assets/images/1920/work2/navarr.png" alt=""></div>
            </div>
          </div>
        </div>
        <div class="down" v-show="showWeather">
          <div class="titlebox flex-box">
            <div class="color"></div>
            <div class="text">健康气象</div>
          </div>
          <div class="weather flex-box-between">
            <div class="son flex-box on">
              <div class="choose"></div>
              <div class="icon"><img src="~@/assets/images/1920/work3/menu-icon-1.png" alt=""></div>
              <div class="info">
                <p>慢阻肺</p>
                <p>气象风险预测服务</p>
              </div>
            </div>
            <div class="son flex-box on">
              <div class="choose"></div>
              <div class="icon"><img src="~@/assets/images/1920/work3/menu-icon-2.png" alt=""></div>
              <div class="info">
                <p>中暑</p>
                <p>气象风险预测服务</p>
              </div>
            </div>
            <div class="son flex-box">
              <div class="icon"><img src="~@/assets/images/1920/work3/menu-icon-3.png" alt=""></div>
              <div class="info">
                <p>哮喘</p>
                <p>气象风险预测服务</p>
              </div>
            </div>
            <div class="son flex-box">
              <div class="icon"><img src="~@/assets/images/1920/work3/menu-icon-4.png" alt=""></div>
              <div class="info">
                <p>老年人感冒</p>
                <p>气象风险预测服务</p>
              </div>
            </div>
            <div class="son flex-box">
              <div class="icon"><img src="~@/assets/images/1920/work3/menu-icon-5.png" alt=""></div>
              <div class="info">
                <p>儿童感冒</p>
                <p>气象风险预测服务</p>
              </div>
            </div>
            <div class="son flex-box">
              <div class="icon"><img src="~@/assets/images/1920/work3/menu-icon-6.png" alt=""></div>
              <div class="info">
                <p>青少年、成人感冒</p>
                <p>气象风险预测服务</p>
              </div>
            </div>
          </div>
        </div>
        <div class="down">
          <div class="titlebox flex-box">
            <div class="color"></div>
            <div class="text">数据选取</div>
          </div>
          <div class="item flex-box-between">
            <div class="icon"><img src="~@/assets/images/1920/work2/navd1.png" alt=""></div>
            <div class="textcore">
              <div class="title">模型训练</div>
              <div class="desc">客户具备模型训练能力，期望采用作业级算力服务提供的运行环境， 快捷训练</div>
            </div>
          </div>
          <div class="item flex-box-between">
            <div class="icon"><img src="~@/assets/images/1920/work2/navd2.png" alt=""></div>
            <div class="textcore">
              <div class="title">模型微调</div>
              <div class="desc">客户已获取基础模型，期望采用作业级算力服务提供的运行环境，发 布模型微调任务</div>
            </div>
          </div>
        </div>
        <div class="footer flex-box-end">
          <el-button type="primary" v-show="showWeather" @click="endFun(1)">下一步</el-button>
        </div>
      </div>
      <div class="akazamWeb" v-if="step === 1">
        <div class="form">
          <div class="formitem flex-box">
            <div class="titlesss flex-box">
              <div class="color"></div>
              <div class="text">预测数据</div>
            </div>
            <div class="sonBox">
              <div class="son flex-box">
                <div class="elcore onon">
                  <div class="tables">
                    <div class="title flex-box-between">
                      <div>应用服务名称</div>
                      <div>大小</div>
                    </div>
                    <div class="list">
                      <div class="item flex-box-between">
                        <div class="le flex-box">
                          <div class="name">慢阻肺</div>
                        </div>
                        <div class="rt">
                          <div class="name">36G</div>
                        </div>
                      </div>
                      <div class="item flex-box-between">
                        <div class="le flex-box">
                          <div class="name">中暑</div>
                        </div>
                        <div class="rt">
                          <div class="name">31G</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="formitem flex-box">
            <div class="titlesss flex-box">
              <div class="color"></div>
              <div class="text">算力需求</div>
            </div>
            <div class="sonBox">
              <div class="son flex-box">
                <div class="longtitle flex-box-between">
                  <div class="l">> 应用作业-气象风险预测模型-慢阻肺，中暑</div>
                  <div class="r">超算作业</div>
                </div>
              </div>
              <div class="son flex-box">
                <div class="text">CPU >=</div>
                <div class="elcore">
                  <el-input v-model="datastep.input2">
                    <template slot="append">核</template>
                  </el-input>
                </div>
              </div>
              <div class="son flex-box">
                <div class="text">内存 >=</div>
                <div class="elcore">
                  <el-input v-model="datastep.input3">
                    <template slot="append">G</template>
                  </el-input>
                </div>
              </div>
            </div>
          </div>
          <div class="formitem flex-box">
            <div class="titlesss flex-box">
              <div class="color"></div>
              <div class="text">网络需求</div>
            </div>
            <div class="sonBox">
              <div class="son flex-box">
                <div class="text">带宽 >=</div>
                <div class="elcore">
                  <el-input v-model="datastep.input5">
                    <template slot="append">M/s</template>
                  </el-input>
                </div>
              </div>
              <div class="son flex-box">
                <div class="text">网络类型</div>
                <div class="elcore">
                  <el-select size="middle" v-model="datastep.input6">
                    <el-option v-for="(item, index) in datastep.options3" :key="index" :value="item"
                      :label="item"></el-option>
                  </el-select>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="footer flex-box-end">
          <!-- <el-button type="primary" @click="endFun(0)">重置应用</el-button> -->
          <el-button type="primary" @click="endFun(2)">下一步：运行资源池</el-button>
        </div>
      </div>
      <div class="akazamWeb" v-if="step === 2">
        <div class="form">
          <div class="formitem flex-box">
            <div class="titlesss flex-box">
              <div class="color"></div>
              <div class="text">分配策略</div>
            </div>
            <div class="sonBox">
              <div class="son flex-box">
                <div class="text">分配方式</div>
                <div class="elcore">
                  <div class="tabs flex-box">
                    <div class="item" v-for="(item, index) in datastep.strategyTabs" :key="index"
                      :class="datastep.strategy == item ? 'on' : ''" @click="strategyChoose(item)">{{ item }}</div>
                  </div>
                </div>
              </div>
              <div class="son flex-box">
                <div class="text">应用作业</div>
                <div class="elcore elradio">
                  <div class="namebox flex-box-between">
                    <div class="word">气象风险预测模型-慢阻肺，中暑</div>
                    <div class="rt">超算作业</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="formitem flex-box">
            <div class="titlesss flex-box">
              <div class="color"></div>
              <div class="text">运行资源池</div>
            </div>
            <div class="sonBox">
              <div class="son flex-box">
                <div class="text">运行资源池</div>
                <div class="elcore elradio">
                  <div class="namebox flex-box-between">
                    <div class="word">{{ datastep.businesscity }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="footer flex-box-end">
          <!-- <el-button type="primary" @click="endFun(0)">重置应用</el-button> -->
          <el-button type="primary" @click="endFun(1)">上一步：应用服务加载</el-button>
          <el-button type="primary" @click="endFun(3)">下一步：预测结果发布</el-button>
        </div>
      </div>
      <div class="akazamWeb" v-if="step === 3">
        <div class="form">
          <div class="formitem flex-box">
            <div class="titlesss flex-box">
              <div class="color"></div>
              <div class="text">健康气象作业</div>
            </div>
          </div>
          <div class="longtables">
            <div class="label flex-box">
              <div class="item w1">作业ID</div>
              <div class="item w2">应用服务名称</div>
              <div class="item w3">状态</div>
            </div>
            <div class="value flex-box">
              <div class="item w1">HA71212</div>
              <div class="item w2">慢阻肺</div>
              <div class="item w3">{{page2Finished ? '预测完成' : '预测中'}}</div>
            </div>
            <div class="value flex-box">
              <div class="item w1">HA71213</div>
              <div class="item w2">中暑</div>
              <div class="item w3">{{page2Finished ? '预测完成' : '预测中'}}</div>
            </div>
          </div>
        </div>
        <div class="footer flex-box-end">
          <!-- <el-button type="primary" @click="endFun(0)">重置应用</el-button> -->
          <el-button type="primary" @click="endFun(2)">上一步：运行资源池</el-button>
        </div>
      </div>
      <div class="akazamWeb" v-if="step === 4">
        <div class="loadcore" v-if="datastep.step4Loading">
          <div class="icon"><i class="el-icon-loading"></i></div>
          <div class="text">环境运行准备中</div>
        </div>
        <div class="form" v-else>
          <div class="formitem flex-box">
            <div class="title flex-box">
              <div class="color"></div>
              <div class="text">渲染作业</div>
            </div>
          </div>
          <div class="tableOne">
            <div class="list">
              <div class="item flex-box-between">
                <div>作业ID </div>
                <div>场景名 </div>
                <div>状态 </div>
                <div>操作 </div>
              </div>
              <div class="item flex-box-between">
                <div>62W122001</div>
                <div>离线渲染包.max</div>
                <div>{{ datastep.step4Finished ? '渲染完成' : '渲染中' }}</div>
                <div><span v-if="!datastep.step4Finished">-</span><i v-else class='el-icon-download'></i></div>
              </div>
            </div>
          </div>
        </div>
        <div class="footer flex-box-end">
          <!-- <el-button type="primary" @click="endFun(0)">重置应用</el-button> -->
          <el-button type="primary" @click="endFun(3)">上一步：提交作业</el-button>
        </div>
      </div>
    </div>
    <!-- step1 -->
    <div class="relative">
      <step0 v-if="step == 1" @endFun="endFun" @stepEnd="stepEnd" :propsdata="datastep"></step0>
      <step1 v-if="step == 2" @endFun="endFun" @stepEnd="stepEnd" :propsdata="datastep" ref="step1"
        @step2Loading="step2Loading"></step1>
      <step2 v-if="step == 3" @endFun="endFun" @stepEnd="stepEnd" :propsdata="datastep"></step2>
    </div>
  </div>
</template>


<script>
import headerCommon from '../components/header/Index'
import step0 from '../components/1920work3/step0.vue'
import step1 from '../components/1920work3/step1.vue'
import step2 from '../components/1920work3/step2.vue'
import { getWork } from "@/api/common";

export default {
  components: {
    headerCommon,
    step0,
    step1,
    step2,
  },
  data() {
    return {
      menuIndex: null,
      showMenu: false,
      step: 0,
      showBg: false,
      showItem: false,
      isAuto: null,
      workTimer: null,
      isfull: false,
      datastep: {
        options1: ['3D应用', '影视素材', '效果图'],
        options2: ['RTX3060', 'RTX3090'],
        options3: ['专线网络连接'],
        options4: ['离线存储', '实时'],
        options5: ['3DS MAX'],
        input1: '3D应用',
        input2: '8',
        input3: '64',
        input4: 'RTX3060',
        input5: '10',
        input6: '专线网络连接',
        input7: '100',
        input8: '离线存储',
        input9: '3DS MAX',
        strategyTabs: ['智能分配', '指定资源池'],
        strategy: '指定资源池',
        strategyList: [
          { name: '天翼云-上海一', spec: '6C16G 4*12.74TFlops', desc: '2.26元/路/时', price: '0.18元/GB', gpu: 15, cpu: 18, ram: 35, strategy: ['效率优先', '智能分配'], min: 0, max: 200, use: 120, num1: 1, num2: 1, num3: 6, num4: 0, delay: 1 },
          { name: '上海气象局专属资源池', spec: '8C16G 12TFSP及以上', desc: '1.25元/路/时', price: '0.15元/GB', gpu: 20, cpu: 22, ram: 26, strategy: ['指定资源池', '智能分配'], min: 0, max: 400, use: 210, num1: 0, num2: 0, num3: 8, num4: 0, delay: 0.5 },
          { name: '天翼云-上海二', spec: '8C16G 12TFSP及以上  ', desc: '1.40元/路/时', price: '0.15元/GB', gpu: 32, cpu: 23, ram: 28, strategy: ['指定资源池', '智能分配'], min: 0, max: 400, use: 80, num1: 1, num2: 2, num3: 2, num4: 0, delay: 1 },
          { name: '天翼云-上海三', spec: '6C16G 4*12.74TFlops', desc: '2.50元/路/时', price: '0.2元/GB', gpu: 17, cpu: 14, ram: 21, strategy: ['指定资源池', '效率优先'], min: 0, max: 300, use: 180, num1: 0, num2: 0, num3: 1, num4: 0, delay: 1 },
          { name: '天翼云-厦门', spec: '8C16G 12TFSP及以上', desc: '2.50元/路/时', price: '0.2元/GB', gpu: 40, cpu: 31, ram: 18, strategy: ['效率优先'], min: 0, max: 200, use: 140, num1: 0, num2: 0, num3: 5, num4: 0, delay: 1 },
        ],
        businesscity: '上海气象局专属资源池',
        otherNow: { name: '上海气象局专属资源池', spec: '8C16G 12TFSP及以上', desc: '1.25元/路/时', price: '0.15元/GB', gpu: 20, cpu: 22, ram: 26, strategy: ['指定资源池', '智能分配'], min: 0, max: 400, use: 210, num1: 0, num2: 0, num3: 8, num4: 0, delay: 0.5 },
        otherTwo: [
          { name: '天翼云-上海二', spec: '8C16G 12TFSP及以上  ', desc: '1.40元/路/时', price: '0.15元/GB', gpu: 32, cpu: 23, ram: 28, strategy: ['指定资源池', '智能分配'], min: 0, max: 400, use: 80, num1: 1, num2: 2, num3: 2, num4: 0, delay: 1 },
          { name: '天翼云-上海三', spec: '6C16G 4*12.74TFlops', desc: '2.50元/路/时', price: '0.2元/GB', gpu: 17, cpu: 14, ram: 21, strategy: ['指定资源池', '效率优先'], min: 0, max: 300, use: 180, num1: 0, num2: 0, num3: 1, num4: 0, delay: 1 },
        ],
        options3_1: ['Windows', 'Linux'],
        options3_2: ['3DS MAX', 'Cinema 4D', 'Blender'],
        options3_3: ['vray6.00.01', 'vray 5.20.06', 'vray 5.20.03', 'Octane', 'Arnold'],
        options3_1_val: 'Windows',
        options3_2_val: '3DS MAX',
        options3_3_val: 'vray6.00.01',
        step4Loading: true,
        step4Finished: false
      },
      step2loadAni: false,

      showWeather: false,
      page2Finished: false
    };
  },
  filters: {

  },
  mounted() {
    // setTimeout(() => {
    //   this.step = 0
    // }, 1000);
    // this.endFun(3)
  },
  created() {

  },
  methods: {
    step4Finished() {
      this.datastep.step4Finished = true
    },
    step4Loading() {
      this.datastep.step4Loading = false
    },
    businesscityInput(e) {
      let businesscity = this.datastep.businesscity
      let strategy = this.datastep.strategy
      if (strategy == '智能分配') {
        if (businesscity == '天翼云-上海') {
          this.datastep.otherTwo = [this.datastep.strategyList[1], this.datastep.strategyList[2]]
        } else if (businesscity == '天翼云-雅安') {
          this.datastep.otherTwo = [this.datastep.strategyList[0], this.datastep.strategyList[2]]
        } else if (businesscity == '天翼云-贵安') {
          this.datastep.otherTwo = [this.datastep.strategyList[0], this.datastep.strategyList[1]]
        }
      } else if (strategy == '效率优先') {
        if (businesscity == '天翼云-上海') {
          this.datastep.otherTwo = [this.datastep.strategyList[3], this.datastep.strategyList[4]]
        } else if (businesscity == '天翼云-扬州') {
          this.datastep.otherTwo = [this.datastep.strategyList[0], this.datastep.strategyList[4]]
        } else if (businesscity == '天翼云-厦门') {
          this.datastep.otherTwo = [this.datastep.strategyList[0], this.datastep.strategyList[3]]
        }
      } else if (strategy == '指定资源池') {
        if (businesscity == '天翼云-雅安') {
          this.datastep.otherTwo = [this.datastep.strategyList[2], this.datastep.strategyList[3]]
        } else if (businesscity == '天翼云-贵安') {
          this.datastep.otherTwo = [this.datastep.strategyList[1], this.datastep.strategyList[3]]
        } else if (businesscity == '天翼云-扬州') {
          this.datastep.otherTwo = [this.datastep.strategyList[1], this.datastep.strategyList[2]]
        }
      }
      this.datastep.otherNow = this.datastep.strategyList.filter(item => (item.name == businesscity))[0]
      this.$refs.step1.initanchor()
    },
    strategyChoose(val) {
      return false
      if (this.datastep.strategy != val) {
        this.datastep.strategy = val
        // if (val == '指定资源池') {
        //   this.datastep.businesscity = '天翼云-雅安'
        // } else {
        //   this.datastep.businesscity = '天翼云-上海'
        // }
        // this.businesscityInput()
      }
    },
    goWork(index) {
      this.showMenu = false
      this.showIframe = true
      if (index === 4) {
        this.$router.push({
          path: '/page12'
        })
      } else if (index === 8) {
        this.$router.push({
          path: '/page2'
        })
      }
    },
    fullFun() {
      this.isfull = !this.isfull
    },
    step2Loading() {
      this.step2loadAni = true
    },
    stepEnd(val) {
      if (this.isAuto) {
        // this.isfull = true
        this.setGetwork()
      }
      if (val == 3) {
        this.page2Finished = true
      }
    },
    setGetwork() {
      let _this = this
      _this.workTimer = setInterval(() => {
        _this._getWork()
      }, 1000);
      _this.GLOBAL.timerArraySet.push(this.workTimer)
    },
    _getWork() {
      getWork().then(res => {
        if (res.status === 200 && res.result && res.result.step) {
          let step = res.result.step
          if (step == 6) {
            if (res.result.predictResult) {
              this.predictResult = res.result.predictResult
            }
          }
          if (step != this.step) {
            this.step = step
            clearInterval(this.workTimer)
            // this.isfull = false
          }
          if (step == 6) {
            clearInterval(this.workTimer)
            // this.isfull = false
          }
        } else {
        }
      })
    },
    endFun(index) {
      this.step = index
      if (index == 4) {
        this.datastep.step4Loading = true
        this.datastep.step4Finished = false
      }
    },
    endFunBefore() {
      this.showWeather = true
    },
  },
  destroyed() {
  },
};
</script>

<style lang="scss" scoped>
.page13 {
  .weather {
    .son {
      width: 288px;
      height: 100px;
      background: #414350;
      border-radius: 12px;
      padding: 15px 28px;
      margin-top: 25px;
      box-sizing: border-box;
      border: 2px solid #414350;
      position: relative;
      .choose{
        background-image: url('~@/assets/images/1920/work3/choose.png');
        background-size: cover;
        width: 38px;
        height: 38px;
        position: absolute;
        bottom: -1px;
        right: -1px;
        z-index: 1;
      }
      .info {
        padding-left: 20px;

        p {
          margin: 0;
          font-size: 16px;
          opacity: 0.3;
          padding-top: 5px;
        }
      }
      .icon{
        width: 61px;
        height: 61px;
        img{
          width: 100%;
          height: 100%;
        }
      }

      &.on {
        border-color: rgba(221, 128, 35, 1);
        .info {
          p {
            opacity: 1;
          }
        }
      }
    }
  }

  .bg {
    background-image: url('~@/assets/images/1920/work/step1/listbg.png');
    background-size: cover;
    width: 760px;
    height: 303px;
    position: absolute;
    bottom: 46px;
    left: 0;
    z-index: 0;
    // opacity: 0;

    &.ani {
      animation: transopacity 1s ease-in 1 forwards;
    }
  }

  .openMenu {
    width: 250px;
    height: 32px;
    position: absolute;
    top: 130px;
    left: 40px;
    cursor: pointer;
    opacity: 0;
  }

  .menu {
    box-sizing: border-box;

    .item {
      width: 193px;
      height: 181px;
      background-color: rgba($color: #ffffff, $alpha: 0.1);
      box-sizing: border-box;
      padding-top: 33px;

      img {
        width: 70px;
        height: 70px;
        margin: 0 auto;
        display: block;
      }

      .img1 {
        display: none;
      }

      p {
        text-align: center;
        font-size: 24px;
        color: #ffffff;
      }

      &.on {
        background-color: #28A2CE;
      }

      &.ani {
        animation: transopacity 1s ease-in 1 forwards;
      }

      &:nth-child(8),
      &:nth-child(4) {
        cursor: pointer;

        &:hover {
          background-color: #28A2CE;

          .img2 {
            display: none;
          }

          .img1 {
            display: block;
          }
        }
      }
    }

    &.newMenu {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 642px;
      height: 672px;
      z-index: 999;
      background-color: #3A3C47;
      overflow: hidden;
      box-sizing: border-box;
      padding: 15px;
    }
  }

  .relative {
    position: relative;
    z-index: 2;
    padding-top: 60px;
  }

  .loading {
    position: fixed;
    // background-color: rgba($color: #000000, $alpha: 0.5);
    // width: 100vw;
    // height: 100vh;
    z-index: 98;
    bottom: 0;
    left: 0;
    right: 0;
    box-sizing: border-box;
    padding-bottom: 45vh;
    box-sizing: border-box;

    .icon {
      font-size: 40px;
    }

    .text {
      font-size: 40px;
      margin-left: 30px;
    }
  }


}

.akazam {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 642px;
  height: 672px;
  z-index: 99;
  background-color: RGBA(58, 60, 71, 1);
  overflow: hidden;
  box-sizing: border-box;

  .akazamWeb {
    width: 100%;
    box-sizing: border-box;
    padding: 13px;
    height: 100%;
    overflow: hidden;
    overflow-y: scroll;
    // position: relative;

    .loadcore {
      text-align: center;
      padding-top: 240px;
      color: #ffffff;

      .icon {
        font-size: 80px;
        line-height: 80px;
      }

      .text {
        font-size: 24px;
        margin-top: 20px;
      }
    }

    .down {
      margin-top: 30px;

      .titlebox {
        .color {
          width: 4px;
          height: 14px;
          background: #848691;
          margin-right: 7px;
        }

        .text {
          font-size: 16px;
          font-weight: bold;
          line-height: 16px;
        }
      }

      .item {
        width: 600px;
        height: 100px;
        background: #414350;
        border-radius: 12px;
        box-sizing: border-box;
        padding: 15px 28px;
        margin-top: 25px;
        .icon {
          width: 45px;
          height: 45px;
          margin-top: 10px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .textcore {
          width: 480px;

          .desc {
            color: #999999;
            margin-top: 10px;
          }
        }
      }
    }

    .imgnav {
      margin-top: 25px;

      .left {
        .item {
          width: 110px;
          height: 236px;
          background: #414350;
          border-radius: 12px;
          box-sizing: border-box;
          padding-top: 70px;

          .son {
            opacity: 0.5;

            .icon {
              width: 58px;
              height: 58px;
              margin: 0 auto;

              img {
                width: 100%;
                height: 100%;
              }
            }

            .text {
              font-size: 16px;
              text-align: center;
              margin-top: 12px;
            }
          }
        }
      }

      .right {
        width: 475px;
        margin-left: 15px;

        .item {
          width: 110px;
          height: 110px;
          background: #414350;
          border-radius: 12px;
          box-sizing: border-box;
          padding-top: 10px;
          margin-bottom: 15px;

          .arrow {
            width: 19px;
            height: 32px;
            margin: 0 auto;
            margin-top: 30px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .son {
            opacity: 0.5;

            .icon {
              width: 58px;
              height: 58px;
              margin: 0 auto;

              img {
                width: 100%;
                height: 100%;
              }
            }

            .text {
              font-size: 16px;
              text-align: center;
              margin-top: 12px;
            }
          }

          &:nth-child(3) {
            cursor: pointer;

            .son {
              opacity: 0.95;
            }

            &:hover {
              .son {
                opacity: 1;
              }
            }
          }
        }
      }
    }

    .top {
      .item {
        padding: 7px 20px;
        font-size: 16px;
        background: #40424E;
        border-radius: 4px;
        line-height: 16px;
        margin-right: 10px;

        &.on {
          font-weight: bold;
          background: #DD8023;
        }
      }
    }

    .footer {
      width: 100%;
      height: 44px;
      background: #414350;
      border: 1px solid #262832;
      border-radius: 0px 0px 5px 5px;
      position: absolute;
      bottom: 0;
      left: 0;
      box-sizing: border-box;
      padding-right: 20px;
      padding-top: 6px;

      .el-button {
        margin-left: 20px;
      }
    }

    .form {
      line-height: 40px;
      font-size: 16px;

      .tableOne {
        width: 100%;
        background-color: #414350;
        padding: 10px 15px;
        font-size: 16px;
        line-height: 16px;
        border-radius: 4px;
        box-sizing: border-box;

        .list {
          .item {
            padding: 10px 0;

            &:first-child {
              color: #CCCCCC;
              border-bottom: 1px solid rgba($color: #ffffff, $alpha: 0.2);
            }

            div {
              &:nth-child(1) {
                width: 120px;
              }

              &:nth-child(2) {
                width: 120px;
              }

              &:nth-child(3) {
                width: 80px;
              }

              &:nth-child(4) {
                width: 60px;
              }
            }
          }
        }
      }

      .formitem {
        margin: 8px 0;

        .titlesss {
          width: 120px;

          .color {
            width: 4px;
            height: 14px;
            background: #848691;
            margin-top: 13px;
            margin-right: 8px;
          }

          .text {
            font-size: 16px;
            font-weight: bold;
          }
        }

        .sonBox {
          padding-left: 18px;

          .son {
            padding-bottom: 16px;

            .longtitle {
              width: calc(642px - 180px);
              background-color: #414350;
              border-radius: 4px;
              box-sizing: border-box;
              padding: 0 10px;

              .r {
                width: 73px;
                height: 26px;
                background: #4D4F5B;
                border-radius: 4px;
                line-height: 26px;
                margin-top: 6px;
                text-align: center;
              }
            }

            .elcore {
              width: calc(642px - 290px);

              .tables {
                width: calc(100% - 20px);
                background-color: #414350;
                padding: 10px;
                font-size: 16px;
                line-height: 16px;

                .title {
                  color: #CCCCCC;
                  padding-bottom: 15px;
                  border-bottom: 1px solid rgba($color: #ffffff, $alpha: 0.2);
                }

                .list {
                  .item {
                    padding: 10px 0;

                    .le {
                      .check {
                        margin-right: 10px;
                      }
                    }

                    // &:first-child {
                    //   color: #CCCCCC;
                    // }
                  }
                }
              }

              .load {
                padding-top: 20px;
                font-size: 16px;
                line-height: 20px;

                i {
                  display: block;
                  width: 20px;
                  height: 20px;
                  font-size: 20px;
                  margin-right: 10px;
                }

                &.on {
                  color: #05CBC4;
                }
              }

              .elAni {
                opacity: 0;

                &.ani {
                  animation: transopacity 1s linear 1 forwards;
                }
              }

              .namebox {
                width: 100%;
                height: 34px;
                background: #414350;
                border-radius: 4px;
                box-sizing: border-box;
                padding: 0 8px;

                .word {
                  line-height: 34px;
                }

                .rt {
                  width: 73px;
                  height: 26px;
                  background: #4D4F5B;
                  border-radius: 4px;
                  line-height: 26px;
                  margin-top: 4px;
                  text-align: center;
                }
              }

              .tabs {
                .item {
                  width: 30%;
                  height: 34px;
                  background: #414350;
                  border-radius: 4px;
                  text-align: center;
                  line-height: 34px;
                  cursor: pointer;
                  margin-right: 10px;

                  &.on {
                    background: #5E606B;
                  }
                }
              }

              &.on {
                border-radius: 4px;
                width: calc(642px - 160px);
              }
              &.onon {
                border-radius: 4px;
                width: calc(642px - 180px);
              }
            }

            .text {
              width: 100px;
            }
          }
        }
      }


    }
  }

  .full {
    position: absolute;
    left: 7px;
    bottom: 7px;
    cursor: pointer;
    z-index: 2;
    background-image: url('~@/assets/images/1920/ICON.png');
    width: 28px;
    height: 28px;
    background-size: cover;

    &.on {
      // background-image: url('~@/assets/images/page4/screen.png');
    }

    // background-color: rgba($color: #2A2A2D, $alpha: 0.5);
    // background-size: 80%;
    // background-position: center center;
    // border-radius: 8px;
    // background-repeat: no-repeat;
  }

  &.full {
    width: 1056px;
    height: 990px;

    .akazamWeb {
      .form {
        .formitem {
          .sonBox {
            .son {
              .elcore {
                width: calc(1056px - 260px);
              }
            }
          }
        }
      }
    }
  }
}

.longtables {
  width: 100%;
  background: #414350;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 10px;
  .label{
    font-size: 16px;
    color: rgba(204, 204, 204, 1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }
  .value{
    font-size: 16px;
    color: rgba(255, 255, 255, 1);
  }
  .w1{
    width: 180px;
  }
  .w2{
    width: 300px;
  }
  .w3{
    width: 100px;
  }
}

@keyframes transopacity {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
</style>


<style lang="scss">
.akazam {

  .el-input,
  .el-select {
    width: 100%;
  }

  .el-input,
  .el-select,
  .el-radio,
  .el-button {
    font-size: 16px;
  }

  .el-input-group__append {
    background-color: #4D4F5A;
    color: #999999;
  }

  .el-button {
    padding: 0 10px;
    margin: 0;
    height: 30px;
    line-height: 30px;
  }

  .elradio {
    .el-radio {
      display: block;
      margin-top: 20px;
    }
  }
}
</style>