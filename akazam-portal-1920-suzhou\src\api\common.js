/**
 * 请求统一管理
 */
import Request from '../common/request';

/* Common */
export const rankGender = data => Request.get('/ranking/gender', data);

export function getWork (params = {}) {
  return Request({
    url: '/hcssh/hyperone/demo/query/current',
    method: 'get',
    params: params
  })
}

export function getWorkById (params) {
  return Request({
    url: '/hcssh/hyperone/demo/query/' + params.id,
    method: 'get',
    params: params
  })
}

export function getCode (params = {}) {
  return Request({
    url: '/stage-api/captchaImage',
    method: 'get',
    params: params
  })
}

export function postForm (data) {
  return Request({
    url: '/stage-api/portal/chance/addByWeb',
    method: 'post',
    data: data
  })
}

export function resetWork (params = {}) {
  return Request({
    url: '/hcssh/hyperone/demo/reset',
    method: 'get',
    params: params
  })
}

export function getJson (params = {}) {
  return Request({
    url: '/data/cpvp.json',
    method: 'get',
    params: params
  })
}