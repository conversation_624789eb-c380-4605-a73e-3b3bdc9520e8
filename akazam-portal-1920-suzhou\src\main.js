import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import '@/assets/style/common.scss' // global css
import "@/assets/font/font.css"; // font css
import Video from 'video.js'
import "video.js/dist/video-js.css";
import ElementUI from 'element-ui';
import './assets/style/index.css'
// import 'element-ui/lib/theme-chalk/index.css';
import './utils/rem'

import global from './utils/global'
Vue.prototype.GLOBAL = global

Vue.prototype.$video = Video

Vue.config.productionTip = false
Vue.use(ElementUI);

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
