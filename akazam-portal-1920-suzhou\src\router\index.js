import Vue from 'vue'
import VueRouter from 'vue-router'
import G<PERSON><PERSON><PERSON><PERSON> from '../utils/global'
import * as echarts from 'echarts';

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'page0',
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "about" */ '@/views/page0.vue') // 地球
  },
  {
    path: '/page5',
    name: 'page5',
    component: () => import(/* webpackChunkName: "about" */ '@/views/page5.vue') // 
  },
  {
    path: '/page6',
    name: 'page6',
    component: () => import(/* webpackChunkName: "about" */ '@/views/page6.vue') // 
  },
  {
    path: '/page3',
    name: 'page3',
    component: () => import(/* webpackChunkName: "about" */ '@/views/page3.vue') // 资源池概览
  },
  {
    path: '/page4',
    name: 'page4',
    component: () => import(/* webpackChunkName: "about" */ '@/views/page4.vue') // 平台作业视图
  },
  {
    path: '/page2',
    name: 'page2',
    component: () => import(/* webpackChunkName: "about" */ '@/views/page2.vue') // 作业-图像分类
  },
  {
    path: '/page12',
    name: 'page12',
    component: () => import(/* webpackChunkName: "about" */ '@/views/page12.vue') // 作业-渲染
  },
  {
    path: '/page13',
    name: 'page13',
    component: () => import(/* webpackChunkName: "about" */ '@/views/page13.vue') // 作业-气象
  },
  {
    path: '/page14',
    name: 'page14',
    component: () => import(/* webpackChunkName: "about" */ '@/views/page14.vue') // 作业-开通算力资源
  },
  {
    path: '*',
    redirect: '/'
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

router.beforeEach((to, from, next) => {
  // 清除单次延时器 setTimeout
  if (GLOBAL && GLOBAL.timerArrayOut && GLOBAL.timerArrayOut.length > 0) {
    for (let i = 0; i < GLOBAL.timerArrayOut.length; i++) {
      clearTimeout(GLOBAL.timerArrayOut[i]);
    }
    GLOBAL.timerArrayOut.splice(0, GLOBAL.timerArrayOut.length)
  }
  // 清除无限延时器 setInterval
  if (GLOBAL && GLOBAL.timerArraySet && GLOBAL.timerArraySet.length > 0) {
    for (let i = 0; i < GLOBAL.timerArraySet.length; i++) {
      clearInterval(GLOBAL.timerArraySet[i]);
    }
    GLOBAL.timerArraySet.splice(0, GLOBAL.timerArraySet.length)
  }
  // 清除echarts实例
  if (GLOBAL && GLOBAL.echartsDomArray && GLOBAL.echartsDomArray.length > 0) {
    for (let i = 0; i < GLOBAL.echartsDomArray.length; i++) {
      GLOBAL.echartsDomArray[i].dispose()
    }
    GLOBAL.echartsDomArray.splice(0, GLOBAL.echartsDomArray.length)
  }
  next()
})

export default router
