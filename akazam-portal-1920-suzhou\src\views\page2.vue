<template>
  <div class="pageCommon page4">
    <!-- 头部-s -->
    <header-common :icon="6" :name="'作业应用-图像分类学习'"></header-common>
    <!-- 头部-e -->
    <!-- <div class="openMenu" @click="showMenu = !showMenu"></div> -->
    <div class="menu flex-box-between newMenu" v-show="showMenu">
      <div class="item" :class="showItem ? 'ani' : ''" @click="goWork(1)">
        <img class="img1" src="~@/assets/images/page4/icon-1_on.png" alt="">
        <img class="img2" src="~@/assets/images/page4/icon-1.png" alt="">
        <p>人工智能</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''" @click="goWork(2)">
        <img class="img1" src="~@/assets/images/page4/icon-2_on.png" alt="">
        <img class="img2" src="~@/assets/images/page4/icon-2.png" alt="">
        <p>人脸识别</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''"><img src="~@/assets/images/page4/icon-3.png" alt="">
        <p>自动驾驶</p>
      </div>
      <div class="item" :class="[showItem ? 'ani' : '']" @click="goWork(4)">
        <img class="img1" src="~@/assets/images/page4/icon-4_on.png" alt="">
        <img class="img2" src="~@/assets/images/page4/icon-4.png" alt="">
        <p>云渲染</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''"><img src="~@/assets/images/page4/icon-5.png" alt="">
        <p>工业仿真</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''"><img src="~@/assets/images/page4/icon-6.png" alt="">
        <p>预测分析</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''"><img src="~@/assets/images/page4/icon-7.png" alt="">
        <p>智慧城市</p>
      </div>
      <div class="item" :class="[showItem ? 'ani' : '']" @click="goWork(8)">
        <img class="img1" src="~@/assets/images/page4/icon-8_on.png" alt="">
        <img class="img2" src="~@/assets/images/page4/icon-8.png" alt="">
        <p>图像识别</p>
      </div>
      <div class="item" :class="showItem ? 'ani' : ''" @click="goWork(9)">
        <img class="img1" src="~@/assets/images/page4/icon-9_on.png" alt="">
        <img class="img2" src="~@/assets/images/page4/icon-9.png" alt="">
        <p>文生图</p>
      </div>
    </div>
    <!-- <div class="bg" :class="showBg ? 'ani' : ''"></div> -->
    <!-- step1 -->
    <div class="relative">
      <step0 v-if="step == 1" @endFun="endFun" @stepEnd="stepEnd"></step0>
      <step1 v-if="step == 2" @endFun="endFun" @stepEnd="stepEnd" :job1SelectType="job1SelectType"
        :job1SelectRegion="job1SelectRegion" :job2SelectType="job2SelectType" :job2SelectRegion="job2SelectRegion">
      </step1>
      <step2 v-if="step == 3" @endFun="endFun" @stepEnd="stepEnd" :job1SelectType="job1SelectType"
        :job1SelectRegion="job1SelectRegion" :job2SelectType="job2SelectType" :job2SelectRegion="job2SelectRegion">
      </step2>
      <step3 v-if="step == 4" @endFun="endFun" @stepEnd="stepEnd" :job1SelectType="job1SelectType"
        :job1SelectRegion="job1SelectRegion" :job2SelectType="job2SelectType" :job2SelectRegion="job2SelectRegion">
      </step3>
      <step4 v-if="step == 5 || step == 6" ref="bsShow" @endFun="endFun" @stepEnd="stepEnd" :showBs="showBs"
        :job1SelectType="job1SelectType" :job1SelectRegion="job1SelectRegion" :job2SelectType="job2SelectType"
        :job2SelectRegion="job2SelectRegion" :job3SelectRegion="job3SelectRegion"></step4>
      <step5 v-if="step == 7" ref="testShow" :predictResult="predictResult" @endFun="endFun" @stepEnd="stepEnd"
        :job1SelectType="job1SelectType" :job1SelectRegion="job1SelectRegion" :job2SelectType="job2SelectType"
        :job2SelectRegion="job2SelectRegion"></step5>
    </div>
    <div class="loading flex-box-center" v-if="isAuto && !step">
      <div class="icon"><i class="el-icon-loading"></i></div>
      <div class="text">等待作业指令中</div>
    </div>
    <div class="iframeBox" :class="isfull ? 'full' : ''">
      <div class="full" @click.stop="fullFun"></div>
      <iframe class="iframeBoxSon" :src="`${baseUrl}/hcssh/#/app/demo`" frameborder="0"></iframe>
    </div>
  </div>
</template>


<script>
import headerCommon from '../components/header/Index'
import step0 from '../components/1920work/step0.vue'
import step1 from '../components/1920work/step1.vue'
import step2 from '../components/1920work/step2.vue'
import step3 from '../components/1920work/step3.vue'
import step4 from '../components/1920work/step4.vue'
import step5 from '../components/1920work/step5.vue'
import { getWork, getWorkById, resetWork } from "@/api/common";
import { tree } from 'd3'

export default {
  components: {
    headerCommon,
    step0,
    step1,
    step2,
    step3,
    step4,
    step5
  },
  data() {
    return {
      menuIndex: null,
      showMenu: false,
      step: null,
      showBg: false,
      showItem: false,
      isAuto: null,
      workTimer: null,
      isfull: false,
      predictResult: '',
      predictTime: '',
      appid: '',
      showBs: false,
      secondFunShow: true,
      job1SelectRegion: "华为云-上海一",
      job1SelectType: "智能推荐",
      job2SelectRegion: "天翼云-苏州(合营)",
      job2SelectType: "智能推荐",
      job3SelectRegion: "天翼云-太原2",
      showIframe: false,
      baseUrl: process.env.VUE_APP_BASE_URL
    };
  },
  filters: {

  },
  mounted() {
    let _this = this
    if (this.isAuto) {
      _this.setGetwork()
    } else {
      setTimeout(() => {
        this.step = 1
        // this.showMenu = false
      }, 1000);
    }
    // this.showBg = true
    // setTimeout(() => {
    //   this.showItem = true
    //   this.step = 1
    // }, 1000);
    // setTimeout(() => {
    //   this.menuIndex = 8
    // }, 2000);
    // setTimeout(() => {
    //   this.showMenu = false
    // }, 2500);
  },
  created() {
    this.isAuto = this.GLOBAL.isAuto
    if (this.isAuto) {
      this.resetwork()
    }
  },
  methods: {
    resetwork() {
      resetWork().then(res => {
        
      })
    },
    goWork(index) {
      // this.showMenu = false
      this.showIframe = true
      if (index === 4) {
        this.$router.push({
          path: '/page12'
        })
      } else if (index === 8) {
        this.$router.push({
          path: '/page2'
        })
      }
    },
    fullFun() {
      this.isfull = !this.isfull
    },
    stepEnd() {
      if (this.isAuto) {
        this.setGetwork()
      }
    },
    setGetwork() {
      let _this = this
      _this.workTimer = setInterval(() => {
        _this._getWork()
      }, 1000);
      _this.GLOBAL.timerArraySet.push(this.workTimer)
    },
    _getWork() {
      if (this.appid) {
        getWorkById({ id: this.appid }).then(res => {
          if (res.status === 200 && res.result) {
            if (res.result.appId) {
              this.appid = res.result.appId
            }
            if (res.result.step) {
              let step = res.result.step
              if (step != this.step) {
                this.step = step
                // clearInterval(this.workTimer)
              }
              if (step == 2) {
                if (res.result.job1SelectRegion) {
                  this.job1SelectRegion = res.result.job1SelectRegion
                }
                if (res.result.job1SelectType) {
                  this.job1SelectType = res.result.job1SelectType
                }
                if (res.result.job2SelectRegion) {
                  this.job2SelectRegion = res.result.job2SelectRegion
                }
                if (res.result.job2SelectType) {
                  this.job2SelectType = res.result.job2SelectType
                }
              } else if (step == 5) {
                if (res.result.job3SelectRegion) {
                  this.job3SelectRegion = res.result.job3SelectRegion
                }
              } else if (step == 6) {
                this.step = step
                if (res.result.job3SelectRegion) {
                  this.job3SelectRegion = res.result.job3SelectRegion
                }
                if (this.secondFunShow) {
                  this.secondFunShow = false
                  this.$refs.bsShow.secondFun()
                }
              } else if (step == 7) {
                this.step = step
                if (this.predictResult == '' && this.predictTime == '') {
                  if (res.result.predictResult && res.result.predictTime) {
                    if (res.result.predictResult.indexOf('Zebra') > -1) {
                      this.predictResult = '斑马-Zebra'
                    }
                    if (res.result.predictResult.indexOf('Tiger') > -1) {
                      this.predictResult = '老虎-Tiger'
                    }
                    if (res.result.predictResult.indexOf('Deer') > -1) {
                      this.predictResult = '鹿-Deer'
                    }
                    if (res.result.predictResult.indexOf('Cat') > -1) {
                      this.predictResult = '猫-Cat'
                    }
                    if (res.result.predictResult.indexOf('Magpie') > -1) {
                      this.predictResult = '喜鹊-Magpie'
                    }
                    if (res.result.predictResult.indexOf('Cheetah') > -1) {
                      this.predictResult = '猎豹-Cheetah'
                    }
                    if (res.result.predictResult.indexOf('Tree frog') > -1) {
                      this.predictResult = '树蛙-Tree frog'
                    }
                    if (res.result.predictResult.indexOf('Panda') > -1) {
                      this.predictResult = '熊猫-Panda'
                    }
                    if (res.result.predictResult.indexOf('Corgi') > -1) {
                      this.predictResult = '柯吉犬-Corgi'
                    }
                    this.predictTime = res.result.predictTime
                    this.$refs.testShow.init()
                  }
                } else {
                  if (res.result.predictResult && res.result.predictTime) {
                    if (res.result.predictTime != this.predictTime) {
                      if (res.result.predictResult.indexOf('Zebra') > -1) {
                        this.predictResult = '斑马-Zebra'
                      }
                      if (res.result.predictResult.indexOf('Tiger') > -1) {
                        this.predictResult = '老虎-Tiger'
                      }
                      if (res.result.predictResult.indexOf('Deer') > -1) {
                        this.predictResult = '鹿-Deer'
                      }
                      if (res.result.predictResult.indexOf('Cat') > -1) {
                        this.predictResult = '猫-Cat'
                      }
                      if (res.result.predictResult.indexOf('Magpie') > -1) {
                        this.predictResult = '喜鹊-Magpie'
                      }
                      if (res.result.predictResult.indexOf('Cheetah') > -1) {
                        this.predictResult = '猎豹-Cheetah'
                      }
                      if (res.result.predictResult.indexOf('Tree frog') > -1) {
                        this.predictResult = '树蛙-Tree frog'
                      }
                      if (res.result.predictResult.indexOf('Panda') > -1) {
                        this.predictResult = '熊猫-Panda'
                      }
                      if (res.result.predictResult.indexOf('Corgi') > -1) {
                        this.predictResult = '柯吉犬-Corgi'
                      }
                      this.predictTime = res.result.predictTime
                      this.$refs.testShow.init()
                    }
                  }
                }
              }
            }
          }
        })
      } else {
        getWork().then(res => {
          if (res.status === 200 && res.result) {
            if (res.result.appId) {
              this.appid = res.result.appId
              // this.showMenu = false
            }
            if (res.result.step) {
              let step = res.result.step
              if (step != this.step) {
                this.step = step
                // clearInterval(this.workTimer)
              }
              if (step == 2) {
                if (res.result.job1SelectRegion) {
                  this.job1SelectRegion = res.result.job1SelectRegion
                }
                if (res.result.job1SelectType) {
                  this.job1SelectType = res.result.job1SelectType
                }
                if (res.result.job2SelectRegion) {
                  this.job2SelectRegion = res.result.job2SelectRegion
                }
                if (res.result.job2SelectType) {
                  this.job2SelectType = res.result.job2SelectType
                }
              } else if (step == 6) {
                this.step = step
                if (this.secondFunShow) {
                  this.secondFunShow = false
                  this.$refs.bsShow.secondFun()
                }
              } else if (step == 7) {
                this.step = step
                if (this.predictResult == '' && this.predictTime == '') {
                  if (res.result.predictResult && res.result.predictTime) {
                    if (res.result.predictResult.indexOf('Zebra') > -1) {
                      this.predictResult = '斑马-Zebra'
                    }
                    if (res.result.predictResult.indexOf('Tiger') > -1) {
                      this.predictResult = '老虎-Tiger'
                    }
                    if (res.result.predictResult.indexOf('Deer') > -1) {
                      this.predictResult = '鹿-Deer'
                    }
                    if (res.result.predictResult.indexOf('Cat') > -1) {
                      this.predictResult = '猫-Cat'
                    }
                    if (res.result.predictResult.indexOf('Magpie') > -1) {
                      this.predictResult = '喜鹊-Magpie'
                    }
                    if (res.result.predictResult.indexOf('Cheetah') > -1) {
                      this.predictResult = '猎豹-Cheetah'
                    }
                    if (res.result.predictResult.indexOf('Tree frog') > -1) {
                      this.predictResult = '树蛙-Tree frog'
                    }
                    if (res.result.predictResult.indexOf('Panda') > -1) {
                      this.predictResult = '熊猫-Panda'
                    }
                    if (res.result.predictResult.indexOf('Corgi') > -1) {
                      this.predictResult = '柯吉犬-Corgi'
                    }
                    this.predictTime = res.result.predictTime
                    this.$refs.testShow.init()
                  }
                } else {
                  if (res.result.predictResult && res.result.predictTime) {
                    if (res.result.predictTime != this.predictTime) {
                      if (res.result.predictResult.indexOf('Zebra') > -1) {
                        this.predictResult = '斑马-Zebra'
                      }
                      if (res.result.predictResult.indexOf('Tiger') > -1) {
                        this.predictResult = '老虎-Tiger'
                      }
                      if (res.result.predictResult.indexOf('Deer') > -1) {
                        this.predictResult = '鹿-Deer'
                      }
                      if (res.result.predictResult.indexOf('Cat') > -1) {
                        this.predictResult = '猫-Cat'
                      }
                      if (res.result.predictResult.indexOf('Magpie') > -1) {
                        this.predictResult = '喜鹊-Magpie'
                      }
                      if (res.result.predictResult.indexOf('Cheetah') > -1) {
                        this.predictResult = '猎豹-Cheetah'
                      }
                      if (res.result.predictResult.indexOf('Tree frog') > -1) {
                        this.predictResult = '树蛙-Tree frog'
                      }
                      if (res.result.predictResult.indexOf('Panda') > -1) {
                        this.predictResult = '熊猫-Panda'
                      }
                      if (res.result.predictResult.indexOf('Corgi') > -1) {
                        this.predictResult = '柯吉犬-Corgi'
                      }
                      this.predictTime = res.result.predictTime
                      this.$refs.testShow.init()
                    }
                  }
                }
              }
            }
          }
        })
      }
    },
    endFun(index) {
      this.step = index
    }
  },
  destroyed() {
  },
};
</script>

<style lang="scss" scoped>
.page4 {
  position: relative;

  .openMenu {
    width: 250px;
    height: 32px;
    position: absolute;
    top: 130px;
    left: 40px;
    cursor: pointer;
    opacity: 0;
  }

  .bg {
    background-image: url('~@/assets/images/1920/work/step1/listbg.png');
    background-size: cover;
    width: 760px;
    height: 303px;
    position: absolute;
    bottom: 46px;
    left: 0;
    z-index: 0;
    // opacity: 0;

    &.ani {
      animation: transopacity 1s ease-in 1 forwards;
    }
  }



  .menu {
    box-sizing: border-box;

    .item {
      width: 193px;
      height: 181px;
      background-color: rgba($color: #ffffff, $alpha: 0.1);
      box-sizing: border-box;
      padding-top: 33px;

      img {
        width: 70px;
        height: 70px;
        margin: 0 auto;
        display: block;
      }

      .img1 {
        display: none;
      }

      p {
        text-align: center;
        font-size: 24px;
        color: #ffffff;
      }

      &.on {
        background-color: #28A2CE;
      }

      &.ani {
        animation: transopacity 1s ease-in 1 forwards;
      }

      &:nth-child(8),
      &:nth-child(4) {
        cursor: pointer;

        &:hover {
          background-color: #28A2CE;

          .img2 {
            display: none;
          }

          .img1 {
            display: block;
          }
        }
      }
    }

    &.newMenu {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 642px;
      height: 672px;
      z-index: 999;
      background-color: #3A3C47;
      overflow: hidden;
      box-sizing: border-box;
      padding: 15px;
    }
  }

  .relative {
    position: relative;
    z-index: 2;
    padding-top: 60px;
  }

  .loading {
    position: fixed;
    // background-color: rgba($color: #000000, $alpha: 0.5);
    // width: 100vw;
    // height: 100vh;
    z-index: 98;
    bottom: 0;
    left: 0;
    right: 0;
    box-sizing: border-box;
    padding-bottom: 45vh;
    box-sizing: border-box;

    .icon {
      font-size: 40px;
    }

    .text {
      font-size: 40px;
      margin-left: 30px;
    }
  }


}

.iframeBox {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 642px;
  height: 672px;
  z-index: 99;
  background-color: RGBA(58, 60, 71, 1);
  overflow: hidden;

  .full {
    position: absolute;
    left: 10px;
    bottom: 10px;
    cursor: pointer;
    z-index: 2;
    background-image: url('~@/assets/images/1920/ICON.png');
    width: 28px;
    height: 28px;
    background-size: cover;

    &.on {
      // background-image: url('~@/assets/images/page4/screen.png');
    }

    // background-color: rgba($color: #2A2A2D, $alpha: 0.5);
    // background-size: 80%;
    // background-position: center center;
    // border-radius: 8px;
    // background-repeat: no-repeat;
  }

  iframe {
    width: 100%;
    height: 100%;
    opacity: 1;
  }

  &.full {
    width: 1056px;
    height: 990px;
  }
}

@keyframes transopacity {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
</style>


